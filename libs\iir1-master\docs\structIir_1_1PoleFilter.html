<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>iir1: Iir::PoleFilter&lt; BaseClass, StateType, MaxAnalogPoles, MaxDigitalPoles &gt; Struct Template Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">iir1
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespaceIir.html">Iir</a></li><li class="navelem"><a class="el" href="structIir_1_1PoleFilter.html">PoleFilter</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="structIir_1_1PoleFilter-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">Iir::PoleFilter&lt; BaseClass, StateType, MaxAnalogPoles, MaxDigitalPoles &gt; Struct Template Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;<a class="el" href="PoleFilter_8h_source.html">PoleFilter.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for Iir::PoleFilter&lt; BaseClass, StateType, MaxAnalogPoles, MaxDigitalPoles &gt;:</div>
<div class="dyncontent">
 <div class="center">
  <img src="structIir_1_1PoleFilter.png" usemap="#Iir::PoleFilter_3C_20BaseClass_2C_20StateType_2C_20MaxAnalogPoles_2C_20MaxDigitalPoles_20_3E_map" alt=""/>
  <map id="Iir::PoleFilter_3C_20BaseClass_2C_20StateType_2C_20MaxAnalogPoles_2C_20MaxDigitalPoles_20_3E_map" name="Iir::PoleFilter_3C_20BaseClass_2C_20StateType_2C_20MaxAnalogPoles_2C_20MaxDigitalPoles_20_3E_map">
<area href="classIir_1_1CascadeStages.html" alt="Iir::CascadeStages&lt;(MaxAnalogPoles+1)/2, StateType &gt;" shape="rect" coords="444,0,878,24"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="inherited"></a>
Additional Inherited Members</h2></td></tr>
<tr class="inherit_header pub_methods_classIir_1_1CascadeStages"><td colspan="2" onclick="javascript:toggleInherit('pub_methods_classIir_1_1CascadeStages')"><img src="closed.png" alt="-"/>&#160;Public Member Functions inherited from <a class="el" href="classIir_1_1CascadeStages.html">Iir::CascadeStages&lt;(MaxAnalogPoles+1)/2, StateType &gt;</a></td></tr>
<tr class="memitem:ae5253fde0be7ccfa459a3f70fe8e3e31 inherit pub_methods_classIir_1_1CascadeStages"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classIir_1_1CascadeStages.html#ae5253fde0be7ccfa459a3f70fe8e3e31">reset</a> ()</td></tr>
<tr class="separator:ae5253fde0be7ccfa459a3f70fe8e3e31 inherit pub_methods_classIir_1_1CascadeStages"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a56d24da4cf09f898b534b1b05578244d inherit pub_methods_classIir_1_1CascadeStages"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classIir_1_1CascadeStages.html#a56d24da4cf09f898b534b1b05578244d">setup</a> (const double(&amp;sosCoefficients)[MaxStages][6])</td></tr>
<tr class="separator:a56d24da4cf09f898b534b1b05578244d inherit pub_methods_classIir_1_1CascadeStages"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa18e9abcaac65fd21be31ee859add3bb inherit pub_methods_classIir_1_1CascadeStages"><td class="memItemLeft" align="right" valign="top">Sample&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classIir_1_1CascadeStages.html#aa18e9abcaac65fd21be31ee859add3bb">filter</a> (const Sample in)</td></tr>
<tr class="separator:aa18e9abcaac65fd21be31ee859add3bb inherit pub_methods_classIir_1_1CascadeStages"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a034a9be8ae590b814c8499898a93987a inherit pub_methods_classIir_1_1CascadeStages"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="structIir_1_1Cascade_1_1Storage.html">Cascade::Storage</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classIir_1_1CascadeStages.html#a034a9be8ae590b814c8499898a93987a">getCascadeStorage</a> ()</td></tr>
<tr class="separator:a034a9be8ae590b814c8499898a93987a inherit pub_methods_classIir_1_1CascadeStages"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><h3>template&lt;class BaseClass, class StateType, int MaxAnalogPoles, int MaxDigitalPoles = MaxAnalogPoles&gt;<br />
struct Iir::PoleFilter&lt; BaseClass, StateType, MaxAnalogPoles, MaxDigitalPoles &gt;</h3>

<p>Storage for pole filters </p>
</div><hr/>The documentation for this struct was generated from the following file:<ul>
<li>iir/<a class="el" href="PoleFilter_8h_source.html">PoleFilter.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Fri Jan 6 2023 23:53:34 for iir1 by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
