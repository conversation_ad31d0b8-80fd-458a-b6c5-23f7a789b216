/********************************************************************************
** Form generated from reading UI file 'sensorsetform.ui'
**
** Created by: Qt User Interface Compiler version 5.15.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_SENSORSETFORM_H
#define UI_SENSORSETFORM_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QTabWidget>
#include <QtWidgets/QTableWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_SensorSetForm
{
public:
    QVBoxLayout *verticalLayout_2;
    QTabWidget *tabWidget;
    QWidget *tab;
    QVBoxLayout *verticalLayout_3;
    QTableWidget *MicManagerTableWidget;
    QWidget *tab_2;
    QVBoxLayout *verticalLayout;
    QWidget *FreResponseWaveWidget;
    QWidget *THDWaveWidget;
    QTableWidget *MouthTableWidget;
    QWidget *tab_3;
    QVBoxLayout *verticalLayout_4;
    QTableWidget *AccManagerTableWidget;
    QWidget *tab_4;
    QVBoxLayout *verticalLayout_5;
    QTableWidget *ForceManagerTableWidget;
    QWidget *widget;
    QHBoxLayout *horizontalLayout;
    QPushButton *saveButton;
    QPushButton *exitButton;

    void setupUi(QWidget *SensorSetForm)
    {
        if (SensorSetForm->objectName().isEmpty())
            SensorSetForm->setObjectName(QString::fromUtf8("SensorSetForm"));
        SensorSetForm->resize(1229, 831);
        verticalLayout_2 = new QVBoxLayout(SensorSetForm);
        verticalLayout_2->setObjectName(QString::fromUtf8("verticalLayout_2"));
        tabWidget = new QTabWidget(SensorSetForm);
        tabWidget->setObjectName(QString::fromUtf8("tabWidget"));
        tabWidget->setTabPosition(QTabWidget::South);
        tab = new QWidget();
        tab->setObjectName(QString::fromUtf8("tab"));
        verticalLayout_3 = new QVBoxLayout(tab);
        verticalLayout_3->setObjectName(QString::fromUtf8("verticalLayout_3"));
        MicManagerTableWidget = new QTableWidget(tab);
        if (MicManagerTableWidget->columnCount() < 9)
            MicManagerTableWidget->setColumnCount(9);
        QTableWidgetItem *__qtablewidgetitem = new QTableWidgetItem();
        MicManagerTableWidget->setHorizontalHeaderItem(0, __qtablewidgetitem);
        QTableWidgetItem *__qtablewidgetitem1 = new QTableWidgetItem();
        MicManagerTableWidget->setHorizontalHeaderItem(1, __qtablewidgetitem1);
        QTableWidgetItem *__qtablewidgetitem2 = new QTableWidgetItem();
        MicManagerTableWidget->setHorizontalHeaderItem(2, __qtablewidgetitem2);
        QTableWidgetItem *__qtablewidgetitem3 = new QTableWidgetItem();
        MicManagerTableWidget->setHorizontalHeaderItem(3, __qtablewidgetitem3);
        QTableWidgetItem *__qtablewidgetitem4 = new QTableWidgetItem();
        MicManagerTableWidget->setHorizontalHeaderItem(4, __qtablewidgetitem4);
        QTableWidgetItem *__qtablewidgetitem5 = new QTableWidgetItem();
        MicManagerTableWidget->setHorizontalHeaderItem(5, __qtablewidgetitem5);
        QTableWidgetItem *__qtablewidgetitem6 = new QTableWidgetItem();
        MicManagerTableWidget->setHorizontalHeaderItem(6, __qtablewidgetitem6);
        QTableWidgetItem *__qtablewidgetitem7 = new QTableWidgetItem();
        MicManagerTableWidget->setHorizontalHeaderItem(7, __qtablewidgetitem7);
        QTableWidgetItem *__qtablewidgetitem8 = new QTableWidgetItem();
        MicManagerTableWidget->setHorizontalHeaderItem(8, __qtablewidgetitem8);
        MicManagerTableWidget->setObjectName(QString::fromUtf8("MicManagerTableWidget"));

        verticalLayout_3->addWidget(MicManagerTableWidget);

        tabWidget->addTab(tab, QString());
        tab_2 = new QWidget();
        tab_2->setObjectName(QString::fromUtf8("tab_2"));
        verticalLayout = new QVBoxLayout(tab_2);
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        FreResponseWaveWidget = new QWidget(tab_2);
        FreResponseWaveWidget->setObjectName(QString::fromUtf8("FreResponseWaveWidget"));
        FreResponseWaveWidget->setMinimumSize(QSize(600, 300));
        FreResponseWaveWidget->setMaximumSize(QSize(16777215, 300));

        verticalLayout->addWidget(FreResponseWaveWidget);

        THDWaveWidget = new QWidget(tab_2);
        THDWaveWidget->setObjectName(QString::fromUtf8("THDWaveWidget"));
        THDWaveWidget->setMinimumSize(QSize(600, 300));
        THDWaveWidget->setMaximumSize(QSize(16777215, 300));

        verticalLayout->addWidget(THDWaveWidget);

        MouthTableWidget = new QTableWidget(tab_2);
        if (MouthTableWidget->columnCount() < 6)
            MouthTableWidget->setColumnCount(6);
        QTableWidgetItem *__qtablewidgetitem9 = new QTableWidgetItem();
        MouthTableWidget->setHorizontalHeaderItem(0, __qtablewidgetitem9);
        QTableWidgetItem *__qtablewidgetitem10 = new QTableWidgetItem();
        MouthTableWidget->setHorizontalHeaderItem(1, __qtablewidgetitem10);
        QTableWidgetItem *__qtablewidgetitem11 = new QTableWidgetItem();
        MouthTableWidget->setHorizontalHeaderItem(2, __qtablewidgetitem11);
        QTableWidgetItem *__qtablewidgetitem12 = new QTableWidgetItem();
        MouthTableWidget->setHorizontalHeaderItem(3, __qtablewidgetitem12);
        QTableWidgetItem *__qtablewidgetitem13 = new QTableWidgetItem();
        MouthTableWidget->setHorizontalHeaderItem(4, __qtablewidgetitem13);
        QTableWidgetItem *__qtablewidgetitem14 = new QTableWidgetItem();
        MouthTableWidget->setHorizontalHeaderItem(5, __qtablewidgetitem14);
        MouthTableWidget->setObjectName(QString::fromUtf8("MouthTableWidget"));

        verticalLayout->addWidget(MouthTableWidget);

        tabWidget->addTab(tab_2, QString());
        tab_3 = new QWidget();
        tab_3->setObjectName(QString::fromUtf8("tab_3"));
        verticalLayout_4 = new QVBoxLayout(tab_3);
        verticalLayout_4->setObjectName(QString::fromUtf8("verticalLayout_4"));
        AccManagerTableWidget = new QTableWidget(tab_3);
        if (AccManagerTableWidget->columnCount() < 9)
            AccManagerTableWidget->setColumnCount(9);
        QTableWidgetItem *__qtablewidgetitem15 = new QTableWidgetItem();
        AccManagerTableWidget->setHorizontalHeaderItem(0, __qtablewidgetitem15);
        QTableWidgetItem *__qtablewidgetitem16 = new QTableWidgetItem();
        AccManagerTableWidget->setHorizontalHeaderItem(1, __qtablewidgetitem16);
        QTableWidgetItem *__qtablewidgetitem17 = new QTableWidgetItem();
        AccManagerTableWidget->setHorizontalHeaderItem(2, __qtablewidgetitem17);
        QTableWidgetItem *__qtablewidgetitem18 = new QTableWidgetItem();
        AccManagerTableWidget->setHorizontalHeaderItem(3, __qtablewidgetitem18);
        QTableWidgetItem *__qtablewidgetitem19 = new QTableWidgetItem();
        AccManagerTableWidget->setHorizontalHeaderItem(4, __qtablewidgetitem19);
        QTableWidgetItem *__qtablewidgetitem20 = new QTableWidgetItem();
        AccManagerTableWidget->setHorizontalHeaderItem(5, __qtablewidgetitem20);
        QTableWidgetItem *__qtablewidgetitem21 = new QTableWidgetItem();
        AccManagerTableWidget->setHorizontalHeaderItem(6, __qtablewidgetitem21);
        QTableWidgetItem *__qtablewidgetitem22 = new QTableWidgetItem();
        AccManagerTableWidget->setHorizontalHeaderItem(7, __qtablewidgetitem22);
        QTableWidgetItem *__qtablewidgetitem23 = new QTableWidgetItem();
        AccManagerTableWidget->setHorizontalHeaderItem(8, __qtablewidgetitem23);
        AccManagerTableWidget->setObjectName(QString::fromUtf8("AccManagerTableWidget"));

        verticalLayout_4->addWidget(AccManagerTableWidget);

        tabWidget->addTab(tab_3, QString());
        tab_4 = new QWidget();
        tab_4->setObjectName(QString::fromUtf8("tab_4"));
        verticalLayout_5 = new QVBoxLayout(tab_4);
        verticalLayout_5->setObjectName(QString::fromUtf8("verticalLayout_5"));
        ForceManagerTableWidget = new QTableWidget(tab_4);
        if (ForceManagerTableWidget->columnCount() < 8)
            ForceManagerTableWidget->setColumnCount(8);
        QTableWidgetItem *__qtablewidgetitem24 = new QTableWidgetItem();
        ForceManagerTableWidget->setHorizontalHeaderItem(0, __qtablewidgetitem24);
        QTableWidgetItem *__qtablewidgetitem25 = new QTableWidgetItem();
        ForceManagerTableWidget->setHorizontalHeaderItem(1, __qtablewidgetitem25);
        QTableWidgetItem *__qtablewidgetitem26 = new QTableWidgetItem();
        ForceManagerTableWidget->setHorizontalHeaderItem(2, __qtablewidgetitem26);
        QTableWidgetItem *__qtablewidgetitem27 = new QTableWidgetItem();
        ForceManagerTableWidget->setHorizontalHeaderItem(3, __qtablewidgetitem27);
        QTableWidgetItem *__qtablewidgetitem28 = new QTableWidgetItem();
        ForceManagerTableWidget->setHorizontalHeaderItem(4, __qtablewidgetitem28);
        QTableWidgetItem *__qtablewidgetitem29 = new QTableWidgetItem();
        ForceManagerTableWidget->setHorizontalHeaderItem(5, __qtablewidgetitem29);
        QTableWidgetItem *__qtablewidgetitem30 = new QTableWidgetItem();
        ForceManagerTableWidget->setHorizontalHeaderItem(6, __qtablewidgetitem30);
        QTableWidgetItem *__qtablewidgetitem31 = new QTableWidgetItem();
        ForceManagerTableWidget->setHorizontalHeaderItem(7, __qtablewidgetitem31);
        ForceManagerTableWidget->setObjectName(QString::fromUtf8("ForceManagerTableWidget"));

        verticalLayout_5->addWidget(ForceManagerTableWidget);

        tabWidget->addTab(tab_4, QString());

        verticalLayout_2->addWidget(tabWidget);

        widget = new QWidget(SensorSetForm);
        widget->setObjectName(QString::fromUtf8("widget"));
        widget->setMaximumSize(QSize(16777215, 50));
        horizontalLayout = new QHBoxLayout(widget);
        horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));
        saveButton = new QPushButton(widget);
        saveButton->setObjectName(QString::fromUtf8("saveButton"));

        horizontalLayout->addWidget(saveButton);

        exitButton = new QPushButton(widget);
        exitButton->setObjectName(QString::fromUtf8("exitButton"));

        horizontalLayout->addWidget(exitButton);


        verticalLayout_2->addWidget(widget);


        retranslateUi(SensorSetForm);

        tabWidget->setCurrentIndex(0);


        QMetaObject::connectSlotsByName(SensorSetForm);
    } // setupUi

    void retranslateUi(QWidget *SensorSetForm)
    {
        SensorSetForm->setWindowTitle(QCoreApplication::translate("SensorSetForm", "Form", nullptr));
        QTableWidgetItem *___qtablewidgetitem = MicManagerTableWidget->horizontalHeaderItem(0);
        ___qtablewidgetitem->setText(QCoreApplication::translate("SensorSetForm", "\345\220\215\347\247\260", nullptr));
        QTableWidgetItem *___qtablewidgetitem1 = MicManagerTableWidget->horizontalHeaderItem(1);
        ___qtablewidgetitem1->setText(QCoreApplication::translate("SensorSetForm", "\347\201\265\346\225\217\345\272\246", nullptr));
        QTableWidgetItem *___qtablewidgetitem2 = MicManagerTableWidget->horizontalHeaderItem(2);
        ___qtablewidgetitem2->setText(QCoreApplication::translate("SensorSetForm", "\345\215\225\344\275\215", nullptr));
        QTableWidgetItem *___qtablewidgetitem3 = MicManagerTableWidget->horizontalHeaderItem(3);
        ___qtablewidgetitem3->setText(QCoreApplication::translate("SensorSetForm", "\351\242\221\347\216\207", nullptr));
        QTableWidgetItem *___qtablewidgetitem4 = MicManagerTableWidget->horizontalHeaderItem(4);
        ___qtablewidgetitem4->setText(QCoreApplication::translate("SensorSetForm", "\346\230\257\345\220\246\345\210\260\346\234\237", nullptr));
        QTableWidgetItem *___qtablewidgetitem5 = MicManagerTableWidget->horizontalHeaderItem(5);
        ___qtablewidgetitem5->setText(QCoreApplication::translate("SensorSetForm", "\346\240\241\345\207\206\345\244\251\346\225\260", nullptr));
        QTableWidgetItem *___qtablewidgetitem6 = MicManagerTableWidget->horizontalHeaderItem(6);
        ___qtablewidgetitem6->setText(QCoreApplication::translate("SensorSetForm", "\346\230\257\345\220\246\345\256\232\346\234\237\346\243\200\346\237\245", nullptr));
        QTableWidgetItem *___qtablewidgetitem7 = MicManagerTableWidget->horizontalHeaderItem(7);
        ___qtablewidgetitem7->setText(QCoreApplication::translate("SensorSetForm", "\344\270\212\346\254\241\346\240\241\345\207\206\346\227\245\346\234\237", nullptr));
        QTableWidgetItem *___qtablewidgetitem8 = MicManagerTableWidget->horizontalHeaderItem(8);
        ___qtablewidgetitem8->setText(QCoreApplication::translate("SensorSetForm", "\344\270\213\346\254\241\346\240\241\345\207\206\346\227\245\346\234\237", nullptr));
        tabWidget->setTabText(tabWidget->indexOf(tab), QCoreApplication::translate("SensorSetForm", "\351\272\246\345\205\213\351\243\216", nullptr));
        QTableWidgetItem *___qtablewidgetitem9 = MouthTableWidget->horizontalHeaderItem(0);
        ___qtablewidgetitem9->setText(QCoreApplication::translate("SensorSetForm", "\345\220\215\347\247\260", nullptr));
        QTableWidgetItem *___qtablewidgetitem10 = MouthTableWidget->horizontalHeaderItem(1);
        ___qtablewidgetitem10->setText(QCoreApplication::translate("SensorSetForm", "\346\230\257\345\220\246\345\210\260\346\234\237", nullptr));
        QTableWidgetItem *___qtablewidgetitem11 = MouthTableWidget->horizontalHeaderItem(2);
        ___qtablewidgetitem11->setText(QCoreApplication::translate("SensorSetForm", "\346\240\241\345\207\206\345\244\251\346\225\260", nullptr));
        QTableWidgetItem *___qtablewidgetitem12 = MouthTableWidget->horizontalHeaderItem(3);
        ___qtablewidgetitem12->setText(QCoreApplication::translate("SensorSetForm", "\346\230\257\345\220\246\345\256\232\346\234\237\346\240\241\345\207\206", nullptr));
        QTableWidgetItem *___qtablewidgetitem13 = MouthTableWidget->horizontalHeaderItem(4);
        ___qtablewidgetitem13->setText(QCoreApplication::translate("SensorSetForm", "\344\270\212\346\254\241\346\240\241\345\207\206\346\227\245\346\234\237", nullptr));
        QTableWidgetItem *___qtablewidgetitem14 = MouthTableWidget->horizontalHeaderItem(5);
        ___qtablewidgetitem14->setText(QCoreApplication::translate("SensorSetForm", "\344\270\213\346\254\241\346\240\241\345\207\206\346\227\245\346\234\237", nullptr));
        tabWidget->setTabText(tabWidget->indexOf(tab_2), QCoreApplication::translate("SensorSetForm", "\344\272\272\345\267\245\345\230\264", nullptr));
        QTableWidgetItem *___qtablewidgetitem15 = AccManagerTableWidget->horizontalHeaderItem(0);
        ___qtablewidgetitem15->setText(QCoreApplication::translate("SensorSetForm", "\345\220\215\347\247\260", nullptr));
        QTableWidgetItem *___qtablewidgetitem16 = AccManagerTableWidget->horizontalHeaderItem(1);
        ___qtablewidgetitem16->setText(QCoreApplication::translate("SensorSetForm", "\347\201\265\346\225\217\345\272\246", nullptr));
        QTableWidgetItem *___qtablewidgetitem17 = AccManagerTableWidget->horizontalHeaderItem(2);
        ___qtablewidgetitem17->setText(QCoreApplication::translate("SensorSetForm", "\345\215\225\344\275\215", nullptr));
        QTableWidgetItem *___qtablewidgetitem18 = AccManagerTableWidget->horizontalHeaderItem(3);
        ___qtablewidgetitem18->setText(QCoreApplication::translate("SensorSetForm", "\351\242\221\347\216\207", nullptr));
        QTableWidgetItem *___qtablewidgetitem19 = AccManagerTableWidget->horizontalHeaderItem(4);
        ___qtablewidgetitem19->setText(QCoreApplication::translate("SensorSetForm", "\346\230\257\345\220\246\345\210\260\346\234\237", nullptr));
        QTableWidgetItem *___qtablewidgetitem20 = AccManagerTableWidget->horizontalHeaderItem(5);
        ___qtablewidgetitem20->setText(QCoreApplication::translate("SensorSetForm", "\346\240\241\345\207\206\345\244\251\346\225\260", nullptr));
        QTableWidgetItem *___qtablewidgetitem21 = AccManagerTableWidget->horizontalHeaderItem(6);
        ___qtablewidgetitem21->setText(QCoreApplication::translate("SensorSetForm", "\346\230\257\345\220\246\345\256\232\346\234\237\346\243\200\346\237\245", nullptr));
        QTableWidgetItem *___qtablewidgetitem22 = AccManagerTableWidget->horizontalHeaderItem(7);
        ___qtablewidgetitem22->setText(QCoreApplication::translate("SensorSetForm", "\344\270\212\346\254\241\346\240\241\345\207\206\346\227\245\346\234\237", nullptr));
        QTableWidgetItem *___qtablewidgetitem23 = AccManagerTableWidget->horizontalHeaderItem(8);
        ___qtablewidgetitem23->setText(QCoreApplication::translate("SensorSetForm", "\344\270\213\346\254\241\346\240\241\345\207\206\346\227\245\346\234\237", nullptr));
        tabWidget->setTabText(tabWidget->indexOf(tab_3), QCoreApplication::translate("SensorSetForm", "\345\212\240\351\200\237\345\272\246", nullptr));
        QTableWidgetItem *___qtablewidgetitem24 = ForceManagerTableWidget->horizontalHeaderItem(0);
        ___qtablewidgetitem24->setText(QCoreApplication::translate("SensorSetForm", "\345\220\215\347\247\260", nullptr));
        QTableWidgetItem *___qtablewidgetitem25 = ForceManagerTableWidget->horizontalHeaderItem(1);
        ___qtablewidgetitem25->setText(QCoreApplication::translate("SensorSetForm", "\347\201\265\346\225\217\345\272\246", nullptr));
        QTableWidgetItem *___qtablewidgetitem26 = ForceManagerTableWidget->horizontalHeaderItem(2);
        ___qtablewidgetitem26->setText(QCoreApplication::translate("SensorSetForm", "\345\215\225\344\275\215", nullptr));
        QTableWidgetItem *___qtablewidgetitem27 = ForceManagerTableWidget->horizontalHeaderItem(3);
        ___qtablewidgetitem27->setText(QCoreApplication::translate("SensorSetForm", "\346\230\257\345\220\246\345\210\260\346\234\237", nullptr));
        QTableWidgetItem *___qtablewidgetitem28 = ForceManagerTableWidget->horizontalHeaderItem(4);
        ___qtablewidgetitem28->setText(QCoreApplication::translate("SensorSetForm", "\346\240\241\345\207\206\345\244\251\346\225\260", nullptr));
        QTableWidgetItem *___qtablewidgetitem29 = ForceManagerTableWidget->horizontalHeaderItem(5);
        ___qtablewidgetitem29->setText(QCoreApplication::translate("SensorSetForm", "\346\230\257\345\220\246\345\256\232\346\234\237\346\240\241\345\207\206", nullptr));
        QTableWidgetItem *___qtablewidgetitem30 = ForceManagerTableWidget->horizontalHeaderItem(6);
        ___qtablewidgetitem30->setText(QCoreApplication::translate("SensorSetForm", "\344\270\212\346\254\241\346\240\241\345\207\206\346\227\245\346\234\237", nullptr));
        QTableWidgetItem *___qtablewidgetitem31 = ForceManagerTableWidget->horizontalHeaderItem(7);
        ___qtablewidgetitem31->setText(QCoreApplication::translate("SensorSetForm", "\344\270\213\346\254\241\346\240\241\345\207\206\346\227\245\346\234\237", nullptr));
        tabWidget->setTabText(tabWidget->indexOf(tab_4), QCoreApplication::translate("SensorSetForm", "\345\212\233\344\274\240\346\204\237\345\231\250", nullptr));
        saveButton->setText(QCoreApplication::translate("SensorSetForm", "\344\277\235\345\255\230", nullptr));
        exitButton->setText(QCoreApplication::translate("SensorSetForm", "\351\200\200\345\207\272", nullptr));
    } // retranslateUi

};

namespace Ui {
    class SensorSetForm: public Ui_SensorSetForm {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_SENSORSETFORM_H
