<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>iir1: iir/Layout.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">iir1
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_5231e4e0b49b19c482755f817f3ac47e.html">iir</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">Layout.h</div>  </div>
</div><!--header-->
<div class="contents">
<div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160; </div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;<span class="preprocessor">#ifndef IIR1_LAYOUT_H</span></div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;<span class="preprocessor">#define IIR1_LAYOUT_H</span></div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160; </div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;<span class="preprocessor">#include &quot;Common.h&quot;</span></div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;<span class="preprocessor">#include &quot;MathSupplement.h&quot;</span></div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160; </div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespaceIir.html">Iir</a> {</div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160; </div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;        <span class="keyword">static</span> <span class="keyword">const</span> <span class="keywordtype">char</span> errPoleisNaN[] = <span class="stringliteral">&quot;Pole to add is NaN.&quot;</span>;</div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;        <span class="keyword">static</span> <span class="keyword">const</span> <span class="keywordtype">char</span> errZeroisNaN[] = <span class="stringliteral">&quot;Zero to add is NaN.&quot;</span>;</div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160; </div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;        <span class="keyword">static</span> <span class="keyword">const</span> <span class="keywordtype">char</span> errCantAdd2ndOrder[] = <span class="stringliteral">&quot;Can&#39;t add 2nd order after a 1st order filter.&quot;</span>;</div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160; </div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;        <span class="keyword">static</span> <span class="keyword">const</span> <span class="keywordtype">char</span> errPolesNotComplexConj[] = <span class="stringliteral">&quot;Poles not complex conjugate.&quot;</span>;</div>
<div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;        <span class="keyword">static</span> <span class="keyword">const</span> <span class="keywordtype">char</span> errZerosNotComplexConj[] = <span class="stringliteral">&quot;Zeros not complex conjugate.&quot;</span>;</div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160; </div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;        <span class="keyword">static</span> <span class="keyword">const</span> <span class="keywordtype">char</span> pairIndexOutOfBounds[] = <span class="stringliteral">&quot;Pair index out of bounds.&quot;</span>;</div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160; </div>
<div class="line"><a name="l00062"></a><span class="lineno"><a class="line" href="classIir_1_1LayoutBase.html">   62</a></span>&#160;        <span class="keyword">class </span>IIR_EXPORT <a class="code" href="classIir_1_1LayoutBase.html">LayoutBase</a></div>
<div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;        {</div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;        <span class="keyword">public</span>:</div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;                <a class="code" href="classIir_1_1LayoutBase.html">LayoutBase</a> ()</div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;                        : m_numPoles (0)</div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;                        , m_maxPoles (0)</div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;                        , m_pair (<span class="keyword">nullptr</span>)</div>
<div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;                {</div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;                }</div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160; </div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;                <a class="code" href="classIir_1_1LayoutBase.html">LayoutBase</a> (<span class="keywordtype">int</span> maxPoles, <a class="code" href="structIir_1_1PoleZeroPair.html">PoleZeroPair</a>* pairs)</div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;                        : m_numPoles (0)</div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;                        , m_maxPoles (maxPoles)</div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;                        , m_pair (pairs)</div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;                {</div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;                }</div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160; </div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;                <span class="keywordtype">void</span> setStorage (<span class="keyword">const</span> <a class="code" href="classIir_1_1LayoutBase.html">LayoutBase</a>&amp; other)</div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;                {</div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;                        m_numPoles = 0;</div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;                        m_maxPoles = other.m_maxPoles;</div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;                        m_pair = other.m_pair;</div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;                }</div>
<div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160; </div>
<div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;                <span class="keywordtype">void</span> reset ()</div>
<div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;                {</div>
<div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;                        m_numPoles = 0;</div>
<div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;                }</div>
<div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160; </div>
<div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;                <span class="keywordtype">int</span> getNumPoles ()<span class="keyword"> const</span></div>
<div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;<span class="keyword">                </span>{</div>
<div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;                        <span class="keywordflow">return</span> m_numPoles;</div>
<div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;                }</div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160; </div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;                <span class="keywordtype">int</span> getMaxPoles ()<span class="keyword"> const</span></div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;<span class="keyword">                </span>{</div>
<div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;                        <span class="keywordflow">return</span> m_maxPoles;</div>
<div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;                }</div>
<div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160; </div>
<div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;                <span class="keywordtype">void</span> add (<span class="keyword">const</span> complex_t&amp; pole, <span class="keyword">const</span> complex_t&amp; zero)</div>
<div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;                {</div>
<div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;                        <span class="keywordflow">if</span> (m_numPoles&amp;1)</div>
<div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;                                throw_invalid_argument(errCantAdd2ndOrder);</div>
<div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;                        <span class="keywordflow">if</span> (Iir::is_nan(pole))</div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;                                throw_invalid_argument(errPoleisNaN);</div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;                        <span class="keywordflow">if</span> (Iir::is_nan(zero))</div>
<div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;                                throw_invalid_argument(errZeroisNaN);</div>
<div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;                        m_pair[m_numPoles/2] = <a class="code" href="structIir_1_1PoleZeroPair.html">PoleZeroPair</a> (pole, zero);</div>
<div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;                        ++m_numPoles;</div>
<div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;                }</div>
<div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160; </div>
<div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;                <span class="keywordtype">void</span> addPoleZeroConjugatePairs (<span class="keyword">const</span> complex_t&amp; pole,</div>
<div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;                                                <span class="keyword">const</span> complex_t&amp; zero)</div>
<div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;                {</div>
<div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;                        <span class="keywordflow">if</span> (m_numPoles&amp;1)</div>
<div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;                                throw_invalid_argument(errCantAdd2ndOrder);</div>
<div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;                        <span class="keywordflow">if</span> (Iir::is_nan(pole))</div>
<div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;                                throw_invalid_argument(errPoleisNaN);</div>
<div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;                        <span class="keywordflow">if</span> (Iir::is_nan(zero))</div>
<div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;                                throw_invalid_argument(errZeroisNaN);</div>
<div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;                        m_pair[m_numPoles/2] = <a class="code" href="structIir_1_1PoleZeroPair.html">PoleZeroPair</a> (</div>
<div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;                                pole, zero, std::conj (pole), std::conj (zero));</div>
<div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;                        m_numPoles += 2;</div>
<div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;                }</div>
<div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160; </div>
<div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;                <span class="keywordtype">void</span> add (<span class="keyword">const</span> <a class="code" href="structIir_1_1ComplexPair.html">ComplexPair</a>&amp; poles, <span class="keyword">const</span> <a class="code" href="structIir_1_1ComplexPair.html">ComplexPair</a>&amp; zeros)</div>
<div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;                {</div>
<div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;                        <span class="keywordflow">if</span> (m_numPoles&amp;1)</div>
<div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;                                throw_invalid_argument(errCantAdd2ndOrder);</div>
<div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;                        <span class="keywordflow">if</span> (!poles.<a class="code" href="structIir_1_1ComplexPair.html#a79d121320c8b042faebcc0364398b071">isMatchedPair</a> ())</div>
<div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;                                throw_invalid_argument(errPolesNotComplexConj);</div>
<div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;                        <span class="keywordflow">if</span> (!zeros.<a class="code" href="structIir_1_1ComplexPair.html#a79d121320c8b042faebcc0364398b071">isMatchedPair</a> ())</div>
<div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;                                throw_invalid_argument(errZerosNotComplexConj);</div>
<div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;                        m_pair[m_numPoles/2] = <a class="code" href="structIir_1_1PoleZeroPair.html">PoleZeroPair</a> (poles.first, zeros.first,</div>
<div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;                                                             poles.second, zeros.second);</div>
<div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;                        m_numPoles += 2;</div>
<div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;                }</div>
<div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160; </div>
<div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;                <span class="keyword">const</span> <a class="code" href="structIir_1_1PoleZeroPair.html">PoleZeroPair</a>&amp; getPair (<span class="keywordtype">int</span> pairIndex)<span class="keyword"> const</span></div>
<div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;<span class="keyword">                </span>{</div>
<div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;                        <span class="keywordflow">if</span> ((pairIndex &lt; 0) || (pairIndex &gt;= (m_numPoles+1)/2))</div>
<div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;                                throw_invalid_argument(pairIndexOutOfBounds);</div>
<div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;                        <span class="keywordflow">return</span> m_pair[pairIndex];</div>
<div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;                }</div>
<div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160; </div>
<div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;                <span class="keyword">const</span> <a class="code" href="structIir_1_1PoleZeroPair.html">PoleZeroPair</a>&amp; operator[] (<span class="keywordtype">int</span> pairIndex)<span class="keyword"> const</span></div>
<div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;<span class="keyword">                </span>{</div>
<div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;                        <span class="keywordflow">return</span> getPair (pairIndex);</div>
<div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;                }</div>
<div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160; </div>
<div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;                <span class="keywordtype">double</span> getNormalW ()<span class="keyword"> const</span></div>
<div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;<span class="keyword">                </span>{</div>
<div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;                        <span class="keywordflow">return</span> m_normalW;</div>
<div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;                }</div>
<div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160; </div>
<div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;                <span class="keywordtype">double</span> getNormalGain ()<span class="keyword"> const</span></div>
<div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;<span class="keyword">                </span>{</div>
<div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;                        <span class="keywordflow">return</span> m_normalGain;</div>
<div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;                }</div>
<div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160; </div>
<div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;                <span class="keywordtype">void</span> setNormal (<span class="keywordtype">double</span> w, <span class="keywordtype">double</span> g)</div>
<div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;                {</div>
<div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;                        m_normalW = w;</div>
<div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;                        m_normalGain = g;</div>
<div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;                }</div>
<div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160; </div>
<div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;        <span class="keyword">private</span>:</div>
<div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;                <span class="keywordtype">int</span> m_numPoles = 0;</div>
<div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;                <span class="keywordtype">int</span> m_maxPoles = 0;</div>
<div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;                <a class="code" href="structIir_1_1PoleZeroPair.html">PoleZeroPair</a>* m_pair = <span class="keyword">nullptr</span>;</div>
<div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;                <span class="keywordtype">double</span> m_normalW = 0;</div>
<div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;                <span class="keywordtype">double</span> m_normalGain = 1;</div>
<div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;        };</div>
<div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160; </div>
<div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;<span class="comment">//------------------------------------------------------------------------------</span></div>
<div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160; </div>
<div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;        <span class="keyword">template</span> &lt;<span class="keywordtype">int</span> MaxPoles&gt;</div>
<div class="line"><a name="l00182"></a><span class="lineno"><a class="line" href="classIir_1_1Layout.html">  182</a></span>&#160;                <span class="keyword">class </span><a class="code" href="classIir_1_1Layout.html">Layout</a></div>
<div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;        {</div>
<div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;        <span class="keyword">public</span>:</div>
<div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;                <span class="keyword">operator</span> <a class="code" href="classIir_1_1LayoutBase.html">LayoutBase</a> ()</div>
<div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;                {</div>
<div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;                        <span class="keywordflow">return</span> <a class="code" href="classIir_1_1LayoutBase.html">LayoutBase</a> (MaxPoles, m_pairs);</div>
<div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;                }</div>
<div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160; </div>
<div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;        <span class="keyword">private</span>:</div>
<div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160;                <a class="code" href="structIir_1_1PoleZeroPair.html">PoleZeroPair</a> m_pairs[(MaxPoles+1)/2] = {};</div>
<div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;        };</div>
<div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160; </div>
<div class="line"><a name="l00194"></a><span class="lineno">  194</span>&#160;}</div>
<div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160; </div>
<div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="ttc" id="aclassIir_1_1LayoutBase_html"><div class="ttname"><a href="classIir_1_1LayoutBase.html">Iir::LayoutBase</a></div><div class="ttdef"><b>Definition:</b> Layout.h:63</div></div>
<div class="ttc" id="aclassIir_1_1Layout_html"><div class="ttname"><a href="classIir_1_1Layout.html">Iir::Layout</a></div><div class="ttdef"><b>Definition:</b> Layout.h:183</div></div>
<div class="ttc" id="anamespaceIir_html"><div class="ttname"><a href="namespaceIir.html">Iir</a></div><div class="ttdef"><b>Definition:</b> Biquad.cpp:40</div></div>
<div class="ttc" id="astructIir_1_1ComplexPair_html"><div class="ttname"><a href="structIir_1_1ComplexPair.html">Iir::ComplexPair</a></div><div class="ttdef"><b>Definition:</b> Types.h:48</div></div>
<div class="ttc" id="astructIir_1_1ComplexPair_html_a79d121320c8b042faebcc0364398b071"><div class="ttname"><a href="structIir_1_1ComplexPair.html#a79d121320c8b042faebcc0364398b071">Iir::ComplexPair::isMatchedPair</a></div><div class="ttdeci">bool isMatchedPair() const</div><div class="ttdef"><b>Definition:</b> Types.h:72</div></div>
<div class="ttc" id="astructIir_1_1PoleZeroPair_html"><div class="ttname"><a href="structIir_1_1PoleZeroPair.html">Iir::PoleZeroPair</a></div><div class="ttdef"><b>Definition:</b> Types.h:93</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Fri Jan 6 2023 23:53:32 for iir1 by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
