﻿#include "audiochannelconfigmanager.h"

// 修改：构造函数
// 原因：显式初始化 audioInputs 和 audioOutputs
// 目的：确保 QMap 有效，防止崩溃
AudioChannelConfigManager::AudioChannelConfigManager(const QString& configPath, QObject* parent)
    : QObject(parent), configFilePath(configPath)
{
    qDebug() << "Constructing AudioChannelConfigManager";
    QDir().mkpath(QFileInfo(configPath).absolutePath());
    QMutexLocker locker(&mutex);
    audioInputs[ChannelType::isInput].clear();
    audioOutputs[ChannelType::isOutput].clear();
    qDebug() << "Initialized audioInputs and audioOutputs";
}

// 修改：saveToFile
// 原因：将 channelObj["name"] 设置为 QMap 键（info.name），而非 Sound_Card_Channel_Name
// 目的：修复 JSON 和 QMap 键错误，确保 addChannel 使用正确键名
bool AudioChannelConfigManager::saveToFile()
{
    QMutexLocker locker(&mutex); // Lock for thread safety

    QJsonObject root;

    // Save inputs
    QJsonArray inputsArray;
    for (auto it = audioInputs[ChannelType::isInput].constBegin(); it != audioInputs[ChannelType::isInput].constEnd(); ++it) {
        const QString& name = it.key(); // QMap 键（info.name，如 InChannel_0）
        const AudioChannalInfoConfig& config = it.value();
        qDebug() << "Saving input channel: name=" << name << ", Sound_Card_Channel_Name=" << config.Sound_Card_Channel_Name;
        QJsonObject channelObj;
        channelObj["name"] = name; // 使用 QMap 键
        channelObj["config"] = configToJson(config);
        inputsArray.append(channelObj);
    }
    root["inputs"] = inputsArray;

    // Save outputs
    QJsonArray outputsArray;
    for (auto it = audioOutputs[ChannelType::isOutput].constBegin(); it != audioOutputs[ChannelType::isOutput].constEnd(); ++it) {
        const QString& name = it.key(); // QMap 键（info.name，如 OutChannel_0）
        const AudioChannalInfoConfig& config = it.value();
        qDebug() << "Saving output channel: name=" << name << ", Sound_Card_Channel_Name=" << config.Sound_Card_Channel_Name;
        QJsonObject channelObj;
        channelObj["name"] = name; // 使用 QMap 键
        channelObj["config"] = configToJson(config);
        outputsArray.append(channelObj);
    }
    root["outputs"] = outputsArray;

    QFile file(configFilePath);
    qDebug() << "Opening file for writing: " << configFilePath;
    if (!file.open(QIODevice::WriteOnly)) {
        qDebug() << "Failed to open config file for writing: " << configFilePath;
        return false;
    }

    QJsonDocument doc(root);
    file.write(doc.toJson());
    file.close();
    qDebug() << "Config file saved";
    return true;
}
// 恢复：loadFromFile
// 原因：回滚到原始版本，移除详细日志和异常捕获，保留核心功能
// 目的：简化逻辑，避免崩溃，保持加载 JSON 功能
bool AudioChannelConfigManager::loadFromFile()
{
    QFile file(configFilePath);
    if (!file.exists()) {
        QMessageBox::warning(nullptr, "Warning",
            QString("Configuration file '%1' not found. Creating a new empty configuration file.").arg(configFilePath));

        QJsonObject root;
        root["inputs"] = QJsonArray();
        root["outputs"] = QJsonArray();

        if (!file.open(QIODevice::WriteOnly)) {
            return false; // Failed to create file
        }

        QJsonDocument doc(root);
        file.write(doc.toJson());
        file.close();

        QMutexLocker locker(&mutex);
        audioInputs.clear();
        audioOutputs.clear();
        return true;
    }

    if (!file.open(QIODevice::ReadOnly)) {
        return false;
    }

    QJsonDocument doc = QJsonDocument::fromJson(file.readAll());
    file.close();

    if (doc.isNull() || !doc.isObject()) {
        return false;
    }

    QMutexLocker locker(&mutex);
    audioInputs.clear();
    audioOutputs.clear();

    QJsonObject root = doc.object();

    QJsonArray inputsArray = root["inputs"].toArray();
    for (const auto& inputVal : inputsArray) {
        QJsonObject inputObj = inputVal.toObject();
        QString name = inputObj["name"].toString();
        AudioChannalInfoConfig config = jsonToConfig(inputObj["config"].toObject());
        audioInputs[ChannelType::isInput].insert(name, config);
    }

    QJsonArray outputsArray = root["outputs"].toArray();
    for (const auto& outputVal : outputsArray) {
        QJsonObject outputObj = outputVal.toObject();
        QString name = outputObj["name"].toString();
        AudioChannalInfoConfig config = jsonToConfig(outputObj["config"].toObject());
        audioOutputs[ChannelType::isOutput].insert(name, config);
    }

    return true;
}

bool AudioChannelConfigManager::addChannel(ChannelType type, const QString& channelName, const AudioChannalInfoConfig& config) {
    QMutexLocker locker(&mutex); // Lock for thread safety

    if (type == ChannelType::isInput) {
        if (audioInputs[ChannelType::isInput].contains(channelName)) {
            return false;
        }
        audioInputs[ChannelType::isInput].insert(channelName, config);
    } else {
        if (audioOutputs[ChannelType::isOutput].contains(channelName)) {
            return false;
        }
        audioOutputs[ChannelType::isOutput].insert(channelName, config);
    }
     qDebug() << "channelNamename:" << channelName;
    return true;
}

bool AudioChannelConfigManager::deleteChannel(ChannelType type, const QString& channelName) {
    QMutexLocker locker(&mutex); // Lock for thread safety

    if (type == ChannelType::isInput) {
        return audioInputs[ChannelType::isInput].remove(channelName) > 0;
    } else {
        return audioOutputs[ChannelType::isOutput].remove(channelName) > 0;
    }
}

bool AudioChannelConfigManager::modifyChannel(ChannelType type, const QString& channelName, const AudioChannalInfoConfig& config) {
    QMutexLocker locker(&mutex); // Lock for thread safety

    if (type == ChannelType::isInput) {
        if (!audioInputs[ChannelType::isInput].contains(channelName)) {
            return false;
        }
        audioInputs[ChannelType::isInput][channelName] = config;
    } else {
        if (!audioOutputs[ChannelType::isOutput].contains(channelName)) {
            return false;
        }
        audioOutputs[ChannelType::isOutput][channelName] = config;
    }
    return true;
}

bool AudioChannelConfigManager::queryChannel(ChannelType type, const QString& channelName, AudioChannalInfoConfig& config) const {
    QMutexLocker locker(&mutex); // Lock for thread safety

    if (type == ChannelType::isInput) {
        if (!audioInputs[ChannelType::isInput].contains(channelName)) {
            return false;
        }
        config = audioInputs[ChannelType::isInput][channelName];
    } else {
        if (!audioOutputs[ChannelType::isOutput].contains(channelName)) {
            return false;
        }
        config = audioOutputs[ChannelType::isOutput][channelName];
    }
    return true;
}

QJsonObject AudioChannelConfigManager::configToJson(const AudioChannalInfoConfig& config) const {
    QJsonObject json;
    json["DriverTypeIndexes"] = static_cast<int>(config.DriverTypeIndexes);
    json["Driver_Type"] = config.Driver_Type;
    json["Driver_Name"] = config.Driver_Name;
    json["Sound_Card_Channel_Name"] = config.Sound_Card_Channel_Name;
    json["DriverID"] = static_cast<int>(config.DriverID);
    json["Channel_Gain"] = config.Channel_Gain;
    json["Channel_Max_Value"] = config.Channel_Max_Value;
    json["Channel_Min_Value"] = config.Channel_Min_Value;
    return json;
}

AudioChannalInfoConfig AudioChannelConfigManager::jsonToConfig(const QJsonObject& json) const {

    AudioChannalInfoConfig config;
    config.DriverTypeIndexes = json["DriverTypeIndexes"].toInt();
    config.Driver_Type = json["Driver_Type"].toString();
    config.Driver_Name = json["Driver_Name"].toString();
    config.Sound_Card_Channel_Name = json["Sound_Card_Channel_Name"].toString();
    config.DriverID = json["DriverID"].toInt();
    config.Channel_Gain = static_cast<float>(json["Channel_Gain"].toDouble());
    config.Channel_Max_Value = static_cast<float>(json["Channel_Max_Value"].toDouble());
    config.Channel_Min_Value = static_cast<float>(json["Channel_Min_Value"].toDouble());
    return config;
}

void AudioChannelConfigManager::clearChannels(ChannelType type) {
    QMutexLocker locker(&mutex); // Lock for thread safety
    if (type == ChannelType::isInput) {
        audioInputs[type].clear();
    } else {
        audioOutputs[type].clear();
    }
}


QStringList AudioChannelConfigManager::getInputChannelNames() const {
    QMutexLocker locker(&mutex); // 确保线程安全
    return audioInputs[ChannelType::isInput].keys();
}

QStringList AudioChannelConfigManager::getOutputChannelNames() const {
    QMutexLocker locker(&mutex); // 确保线程安全
    return audioOutputs[ChannelType::isOutput].keys();
}


bool AudioChannelConfigManager::getChannelConfig(const QString& channelName, AudioChannalInfoConfig& config) const {
    QMutexLocker locker(&mutex); // 确保线程安全

    // 首先检查输入通道
    if (audioInputs[ChannelType::isInput].contains(channelName)) {
        config = audioInputs[ChannelType::isInput][channelName];
        return true;
    }

    // 然后检查输出通道
    if (audioOutputs[ChannelType::isOutput].contains(channelName)) {
        config = audioOutputs[ChannelType::isOutput][channelName];
        return true;
    }

    // 通道名称不存在
    return false;
}
