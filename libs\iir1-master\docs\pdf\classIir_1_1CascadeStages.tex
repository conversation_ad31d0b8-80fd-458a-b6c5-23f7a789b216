\hypertarget{classIir_1_1CascadeStages}{}\doxysubsection{Iir\+::Cascade\+Stages$<$ Max\+Stages, State\+Type $>$ Class Template Reference}
\label{classIir_1_1CascadeStages}\index{Iir::CascadeStages$<$ MaxStages, StateType $>$@{Iir::CascadeStages$<$ MaxStages, StateType $>$}}


{\ttfamily \#include $<$Cascade.\+h$>$}

Inheritance diagram for Iir\+::Cascade\+Stages$<$ Max\+Stages, State\+Type $>$\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=0.606061cm]{classIir_1_1CascadeStages}
\end{center}
\end{figure}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
void \mbox{\hyperlink{classIir_1_1CascadeStages_ae5253fde0be7ccfa459a3f70fe8e3e31}{reset}} ()
\item 
void \mbox{\hyperlink{classIir_1_1CascadeStages_a56d24da4cf09f898b534b1b05578244d}{setup}} (const double(\&sos\+Coefficients)\mbox{[}Max\+Stages\mbox{]}\mbox{[}6\mbox{]})
\item 
{\footnotesize template$<$typename Sample $>$ }\\Sample \mbox{\hyperlink{classIir_1_1CascadeStages_aa18e9abcaac65fd21be31ee859add3bb}{filter}} (const Sample in)
\item 
const \mbox{\hyperlink{structIir_1_1Cascade_1_1Storage}{Cascade\+::\+Storage}} \mbox{\hyperlink{classIir_1_1CascadeStages_a034a9be8ae590b814c8499898a93987a}{get\+Cascade\+Storage}} ()
\end{DoxyCompactItemize}


\doxysubsubsection{Detailed Description}
\subsubsection*{template$<$int Max\+Stages, class State\+Type$>$\newline
class Iir\+::\+Cascade\+Stages$<$ Max\+Stages, State\+Type $>$}

Storage for \mbox{\hyperlink{classIir_1_1Cascade}{Cascade}}\+: This holds a chain of 2nd order filters with its coefficients. 

\doxysubsubsection{Member Function Documentation}
\mbox{\Hypertarget{classIir_1_1CascadeStages_aa18e9abcaac65fd21be31ee859add3bb}\label{classIir_1_1CascadeStages_aa18e9abcaac65fd21be31ee859add3bb}} 
\index{Iir::CascadeStages$<$ MaxStages, StateType $>$@{Iir::CascadeStages$<$ MaxStages, StateType $>$}!filter@{filter}}
\index{filter@{filter}!Iir::CascadeStages$<$ MaxStages, StateType $>$@{Iir::CascadeStages$<$ MaxStages, StateType $>$}}
\doxyparagraph{\texorpdfstring{filter()}{filter()}}
{\footnotesize\ttfamily template$<$int Max\+Stages, class State\+Type $>$ \\
template$<$typename Sample $>$ \\
Sample \mbox{\hyperlink{classIir_1_1CascadeStages}{Iir\+::\+Cascade\+Stages}}$<$ Max\+Stages, State\+Type $>$\+::filter (\begin{DoxyParamCaption}\item[{const Sample}]{in }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Filters one sample through the whole chain of biquads and return the result 
\begin{DoxyParams}{Parameters}
{\em in} & Sample to be filtered \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}
filtered sample 
\end{DoxyReturn}
\mbox{\Hypertarget{classIir_1_1CascadeStages_a034a9be8ae590b814c8499898a93987a}\label{classIir_1_1CascadeStages_a034a9be8ae590b814c8499898a93987a}} 
\index{Iir::CascadeStages$<$ MaxStages, StateType $>$@{Iir::CascadeStages$<$ MaxStages, StateType $>$}!getCascadeStorage@{getCascadeStorage}}
\index{getCascadeStorage@{getCascadeStorage}!Iir::CascadeStages$<$ MaxStages, StateType $>$@{Iir::CascadeStages$<$ MaxStages, StateType $>$}}
\doxyparagraph{\texorpdfstring{getCascadeStorage()}{getCascadeStorage()}}
{\footnotesize\ttfamily template$<$int Max\+Stages, class State\+Type $>$ \\
const \mbox{\hyperlink{structIir_1_1Cascade_1_1Storage}{Cascade\+::\+Storage}} \mbox{\hyperlink{classIir_1_1CascadeStages}{Iir\+::\+Cascade\+Stages}}$<$ Max\+Stages, State\+Type $>$\+::get\+Cascade\+Storage (\begin{DoxyParamCaption}{ }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Returns the coefficients of the entire \mbox{\hyperlink{classIir_1_1Biquad}{Biquad}} chain \mbox{\Hypertarget{classIir_1_1CascadeStages_ae5253fde0be7ccfa459a3f70fe8e3e31}\label{classIir_1_1CascadeStages_ae5253fde0be7ccfa459a3f70fe8e3e31}} 
\index{Iir::CascadeStages$<$ MaxStages, StateType $>$@{Iir::CascadeStages$<$ MaxStages, StateType $>$}!reset@{reset}}
\index{reset@{reset}!Iir::CascadeStages$<$ MaxStages, StateType $>$@{Iir::CascadeStages$<$ MaxStages, StateType $>$}}
\doxyparagraph{\texorpdfstring{reset()}{reset()}}
{\footnotesize\ttfamily template$<$int Max\+Stages, class State\+Type $>$ \\
void \mbox{\hyperlink{classIir_1_1CascadeStages}{Iir\+::\+Cascade\+Stages}}$<$ Max\+Stages, State\+Type $>$\+::reset (\begin{DoxyParamCaption}{ }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Resets all biquads (i.\+e. the delay lines but not the coefficients) \mbox{\Hypertarget{classIir_1_1CascadeStages_a56d24da4cf09f898b534b1b05578244d}\label{classIir_1_1CascadeStages_a56d24da4cf09f898b534b1b05578244d}} 
\index{Iir::CascadeStages$<$ MaxStages, StateType $>$@{Iir::CascadeStages$<$ MaxStages, StateType $>$}!setup@{setup}}
\index{setup@{setup}!Iir::CascadeStages$<$ MaxStages, StateType $>$@{Iir::CascadeStages$<$ MaxStages, StateType $>$}}
\doxyparagraph{\texorpdfstring{setup()}{setup()}}
{\footnotesize\ttfamily template$<$int Max\+Stages, class State\+Type $>$ \\
void \mbox{\hyperlink{classIir_1_1CascadeStages}{Iir\+::\+Cascade\+Stages}}$<$ Max\+Stages, State\+Type $>$\+::setup (\begin{DoxyParamCaption}\item[{const double(\&)}]{sos\+Coefficients\mbox{[}\+Max\+Stages\mbox{]}\mbox{[}6\mbox{]} }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Sets the coefficients of the whole chain of biquads. 
\begin{DoxyParams}{Parameters}
{\em sos\+Coefficients} & 2D array in Python style sos ordering\+: 0-\/2\+: FIR, 3-\/5\+: IIR coeff. \\
\hline
\end{DoxyParams}


The documentation for this class was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
iir/Cascade.\+h\end{DoxyCompactItemize}
