/********************************************************************************
** Form generated from reading UI file 'stimulussignalform.ui'
**
** Created by: Qt User Interface Compiler version 5.15.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_STIMULUSSIGNALFORM_H
#define UI_STIMULUSSIGNALFORM_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QStackedWidget>
#include <QtWidgets/QTableWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_StimulusSignalForm
{
public:
    QVBoxLayout *verticalLayout_40;
    QHBoxLayout *horizontalLayout_5;
    QSpacerItem *horizontalSpacer_4;
    QPushButton *pushButton_7;
    QPushButton *pushButton_8;
    QPushButton *pushButton_9;
    QSpacerItem *horizontalSpacer_3;
    QHBoxLayout *horizontalLayout_6;
    QTableWidget *SignalTableWidget;
    QVBoxLayout *verticalLayout_34;
    QWidget *wavewidget;
    QHBoxLayout *horizontalLayout_4;
    QWidget *widget_control;
    QVBoxLayout *verticalLayout_37;
    QWidget *widget_2;
    QGridLayout *gridLayout;
    QWidget *widget_7;
    QVBoxLayout *verticalLayout_5;
    QLabel *label_5;
    QComboBox *signal_unitcomboBox;
    QWidget *widget_4;
    QVBoxLayout *verticalLayout_2;
    QLabel *label_2;
    QLineEdit *Gain_dBlineEdit;
    QWidget *widget_6;
    QVBoxLayout *verticalLayout_4;
    QLabel *label_4;
    QLineEdit *Gain_constantlineEdit;
    QWidget *widget_5;
    QVBoxLayout *verticalLayout_3;
    QLabel *label_3;
    QComboBox *sample_rate_combobox;
    QWidget *widget_10;
    QVBoxLayout *verticalLayout_8;
    QLabel *label_8;
    QLineEdit *lineEdit_3;
    QWidget *widget_3;
    QVBoxLayout *verticalLayout;
    QLabel *label;
    QComboBox *stimulus_signal_type_combobox;
    QStackedWidget *stackedWidget;
    QWidget *page;
    QVBoxLayout *verticalLayout_38;
    QWidget *widget_8;
    QVBoxLayout *verticalLayout_6;
    QLabel *label_6;
    QComboBox *Step_sweep_fre_point_typecomboBox;
    QWidget *widget_15;
    QGridLayout *gridLayout_2;
    QWidget *widget_11;
    QVBoxLayout *verticalLayout_9;
    QLabel *label_9;
    QLineEdit *Step_sweep_start_freqlineEdit;
    QWidget *widget_14;
    QVBoxLayout *verticalLayout_12;
    QLabel *label_12;
    QLineEdit *Step_sweep_min_circleslineEdit;
    QWidget *widget_13;
    QVBoxLayout *verticalLayout_11;
    QLabel *label_11;
    QLineEdit *Step_sweep_min_durationlineEdit;
    QWidget *widget_12;
    QVBoxLayout *verticalLayout_10;
    QLabel *label_10;
    QLineEdit *Step_sweep_stop_freqlineEdit;
    QWidget *widget_9;
    QVBoxLayout *verticalLayout_7;
    QLabel *label_7;
    QComboBox *Step_sweep_octave_typecomboBox;
    QWidget *Step_sweepButtonwidget;
    QHBoxLayout *horizontalLayout_2;
    QPushButton *Step_sweepInputButton;
    QPushButton *Step_sweepOutputButton;
    QSpacerItem *verticalSpacer_3;
    QWidget *page_2;
    QGridLayout *gridLayout_9;
    QWidget *widget_16;
    QGridLayout *gridLayout_3;
    QWidget *widget_24;
    QVBoxLayout *verticalLayout_20;
    QLabel *label_20;
    QLineEdit *conlogsweep_delay_durationlineEdit;
    QWidget *widget_22;
    QVBoxLayout *verticalLayout_18;
    QLabel *label_18;
    QLineEdit *conlogsweep_pre_durationlineEdit;
    QWidget *widget_20;
    QVBoxLayout *verticalLayout_16;
    QLabel *label_16;
    QLineEdit *conlogsweep_stop_freqlineEdit;
    QWidget *widget_21;
    QVBoxLayout *verticalLayout_17;
    QLabel *label_17;
    QLineEdit *conlogsweep_durationlineEdit;
    QWidget *widget_19;
    QVBoxLayout *verticalLayout_15;
    QLabel *label_15;
    QLineEdit *conlogsweep_start_freqlineEdit;
    QSpacerItem *verticalSpacer;
    QWidget *page_3;
    QGridLayout *gridLayout_8;
    QWidget *level_stepButtonwidget;
    QHBoxLayout *horizontalLayout;
    QPushButton *level_stepInputButton;
    QPushButton *level_stepOutPutButton;
    QWidget *widget;
    QHBoxLayout *horizontalLayout_7;
    QWidget *widget_27;
    QVBoxLayout *verticalLayout_22;
    QLabel *label_22;
    QLineEdit *level_step_step_durationlineEdit;
    QWidget *widget_28;
    QVBoxLayout *verticalLayout_23;
    QLabel *label_23;
    QLineEdit *level_step_step_circleslineEdit;
    QSpacerItem *horizontalSpacer;
    QWidget *widget_17;
    QGridLayout *gridLayout_4;
    QWidget *widget_29;
    QVBoxLayout *verticalLayout_30;
    QLabel *label_30;
    QLineEdit *level_step_pointslineEdit;
    QWidget *widget_25;
    QVBoxLayout *verticalLayout_19;
    QLabel *label_19;
    QLineEdit *level_step_start_levellineEdit;
    QWidget *widget_26;
    QVBoxLayout *verticalLayout_21;
    QLabel *label_21;
    QLineEdit *level_step_stop_levellineEdit;
    QWidget *widget_23;
    QVBoxLayout *verticalLayout_14;
    QLabel *label_14;
    QComboBox *level_step_amplitdeTypeSampcomboBox;
    QWidget *widget_30;
    QVBoxLayout *verticalLayout_25;
    QLabel *label_25;
    QLineEdit *level_step_frequencylineEdit;
    QWidget *widget_18;
    QVBoxLayout *verticalLayout_13;
    QLabel *label_13;
    QComboBox *level_step_levelTypeSampcomboBox;
    QSpacerItem *verticalSpacer_4;
    QWidget *page_4;
    QVBoxLayout *verticalLayout_32;
    QWidget *widget_43;
    QVBoxLayout *verticalLayout_39;
    QLabel *label_37;
    QComboBox *PS_stepped_freq_typecomboBox;
    QWidget *widget_36;
    QGridLayout *gridLayout_6;
    QWidget *widget_38;
    QVBoxLayout *verticalLayout_31;
    QLabel *label_32;
    QLineEdit *PS_stepped_pointslineEdit;
    QWidget *widget_39;
    QVBoxLayout *verticalLayout_35;
    QLabel *label_33;
    QComboBox *PS_stepped_step_typecomboBox;
    QWidget *widget_40;
    QVBoxLayout *verticalLayout_36;
    QLabel *label_34;
    QLineEdit *PS_stepped_start_freqlineEdit;
    QWidget *widget_37;
    QVBoxLayout *verticalLayout_33;
    QLabel *label_31;
    QLineEdit *PS_stepped_stop_freqlineEdit;
    QSpacerItem *horizontalSpacer_2;
    QWidget *PS_steppedButtonwidget;
    QHBoxLayout *horizontalLayout_3;
    QPushButton *PS_steppedInputButton;
    QPushButton *PS_steppedOutputButton;
    QSpacerItem *verticalSpacer_2;
    QTableWidget *FreManagetableWidget;

    void setupUi(QWidget *StimulusSignalForm)
    {
        if (StimulusSignalForm->objectName().isEmpty())
            StimulusSignalForm->setObjectName(QString::fromUtf8("StimulusSignalForm"));
        StimulusSignalForm->resize(1083, 941);
        StimulusSignalForm->setMinimumSize(QSize(0, 0));
        verticalLayout_40 = new QVBoxLayout(StimulusSignalForm);
        verticalLayout_40->setSpacing(5);
        verticalLayout_40->setObjectName(QString::fromUtf8("verticalLayout_40"));
        horizontalLayout_5 = new QHBoxLayout();
        horizontalLayout_5->setSpacing(50);
        horizontalLayout_5->setObjectName(QString::fromUtf8("horizontalLayout_5"));
        horizontalSpacer_4 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_5->addItem(horizontalSpacer_4);

        pushButton_7 = new QPushButton(StimulusSignalForm);
        pushButton_7->setObjectName(QString::fromUtf8("pushButton_7"));
        pushButton_7->setMinimumSize(QSize(200, 20));

        horizontalLayout_5->addWidget(pushButton_7);

        pushButton_8 = new QPushButton(StimulusSignalForm);
        pushButton_8->setObjectName(QString::fromUtf8("pushButton_8"));
        pushButton_8->setMinimumSize(QSize(200, 20));

        horizontalLayout_5->addWidget(pushButton_8);

        pushButton_9 = new QPushButton(StimulusSignalForm);
        pushButton_9->setObjectName(QString::fromUtf8("pushButton_9"));
        pushButton_9->setMinimumSize(QSize(200, 20));

        horizontalLayout_5->addWidget(pushButton_9);

        horizontalSpacer_3 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_5->addItem(horizontalSpacer_3);


        verticalLayout_40->addLayout(horizontalLayout_5);

        horizontalLayout_6 = new QHBoxLayout();
        horizontalLayout_6->setObjectName(QString::fromUtf8("horizontalLayout_6"));
        SignalTableWidget = new QTableWidget(StimulusSignalForm);
        SignalTableWidget->setObjectName(QString::fromUtf8("SignalTableWidget"));
        SignalTableWidget->setMinimumSize(QSize(200, 0));
        SignalTableWidget->setMaximumSize(QSize(200, 16777215));

        horizontalLayout_6->addWidget(SignalTableWidget);

        verticalLayout_34 = new QVBoxLayout();
        verticalLayout_34->setObjectName(QString::fromUtf8("verticalLayout_34"));
        wavewidget = new QWidget(StimulusSignalForm);
        wavewidget->setObjectName(QString::fromUtf8("wavewidget"));
        wavewidget->setMinimumSize(QSize(850, 300));

        verticalLayout_34->addWidget(wavewidget);

        horizontalLayout_4 = new QHBoxLayout();
        horizontalLayout_4->setObjectName(QString::fromUtf8("horizontalLayout_4"));
        widget_control = new QWidget(StimulusSignalForm);
        widget_control->setObjectName(QString::fromUtf8("widget_control"));
        verticalLayout_37 = new QVBoxLayout(widget_control);
        verticalLayout_37->setObjectName(QString::fromUtf8("verticalLayout_37"));
        verticalLayout_37->setContentsMargins(11, 11, 11, 11);
        widget_2 = new QWidget(widget_control);
        widget_2->setObjectName(QString::fromUtf8("widget_2"));
        widget_2->setMinimumSize(QSize(530, 145));
        gridLayout = new QGridLayout(widget_2);
        gridLayout->setObjectName(QString::fromUtf8("gridLayout"));
        widget_7 = new QWidget(widget_2);
        widget_7->setObjectName(QString::fromUtf8("widget_7"));
        widget_7->setMinimumSize(QSize(120, 0));
        widget_7->setMaximumSize(QSize(220, 16777215));
        verticalLayout_5 = new QVBoxLayout(widget_7);
        verticalLayout_5->setSpacing(0);
        verticalLayout_5->setObjectName(QString::fromUtf8("verticalLayout_5"));
        verticalLayout_5->setContentsMargins(0, 0, 0, 0);
        label_5 = new QLabel(widget_7);
        label_5->setObjectName(QString::fromUtf8("label_5"));
        QSizePolicy sizePolicy(QSizePolicy::Fixed, QSizePolicy::Fixed);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(label_5->sizePolicy().hasHeightForWidth());
        label_5->setSizePolicy(sizePolicy);
        label_5->setMinimumSize(QSize(0, 0));
        label_5->setMaximumSize(QSize(16777215, 30));
        QFont font;
        font.setPointSize(12);
        label_5->setFont(font);

        verticalLayout_5->addWidget(label_5);

        signal_unitcomboBox = new QComboBox(widget_7);
        signal_unitcomboBox->addItem(QString());
        signal_unitcomboBox->addItem(QString());
        signal_unitcomboBox->addItem(QString());
        signal_unitcomboBox->addItem(QString());
        signal_unitcomboBox->addItem(QString());
        signal_unitcomboBox->addItem(QString());
        signal_unitcomboBox->setObjectName(QString::fromUtf8("signal_unitcomboBox"));
        signal_unitcomboBox->setMinimumSize(QSize(0, 0));
        signal_unitcomboBox->setMaximumSize(QSize(210, 16777215));
        signal_unitcomboBox->setFont(font);

        verticalLayout_5->addWidget(signal_unitcomboBox);


        gridLayout->addWidget(widget_7, 1, 1, 1, 1);

        widget_4 = new QWidget(widget_2);
        widget_4->setObjectName(QString::fromUtf8("widget_4"));
        verticalLayout_2 = new QVBoxLayout(widget_4);
        verticalLayout_2->setSpacing(0);
        verticalLayout_2->setObjectName(QString::fromUtf8("verticalLayout_2"));
        verticalLayout_2->setContentsMargins(0, 0, 0, 0);
        label_2 = new QLabel(widget_4);
        label_2->setObjectName(QString::fromUtf8("label_2"));
        label_2->setMaximumSize(QSize(110, 16777215));
        label_2->setFont(font);

        verticalLayout_2->addWidget(label_2);

        Gain_dBlineEdit = new QLineEdit(widget_4);
        Gain_dBlineEdit->setObjectName(QString::fromUtf8("Gain_dBlineEdit"));
        Gain_dBlineEdit->setMinimumSize(QSize(0, 0));
        Gain_dBlineEdit->setFont(font);

        verticalLayout_2->addWidget(Gain_dBlineEdit);


        gridLayout->addWidget(widget_4, 0, 1, 1, 1);

        widget_6 = new QWidget(widget_2);
        widget_6->setObjectName(QString::fromUtf8("widget_6"));
        verticalLayout_4 = new QVBoxLayout(widget_6);
        verticalLayout_4->setSpacing(0);
        verticalLayout_4->setObjectName(QString::fromUtf8("verticalLayout_4"));
        verticalLayout_4->setContentsMargins(0, 0, 0, 0);
        label_4 = new QLabel(widget_6);
        label_4->setObjectName(QString::fromUtf8("label_4"));
        label_4->setMaximumSize(QSize(110, 16777215));
        label_4->setFont(font);

        verticalLayout_4->addWidget(label_4);

        Gain_constantlineEdit = new QLineEdit(widget_6);
        Gain_constantlineEdit->setObjectName(QString::fromUtf8("Gain_constantlineEdit"));
        Gain_constantlineEdit->setMinimumSize(QSize(0, 0));
        Gain_constantlineEdit->setFont(font);

        verticalLayout_4->addWidget(Gain_constantlineEdit);


        gridLayout->addWidget(widget_6, 0, 3, 1, 1);

        widget_5 = new QWidget(widget_2);
        widget_5->setObjectName(QString::fromUtf8("widget_5"));
        widget_5->setMinimumSize(QSize(120, 0));
        widget_5->setMaximumSize(QSize(220, 16777215));
        verticalLayout_3 = new QVBoxLayout(widget_5);
        verticalLayout_3->setSpacing(0);
        verticalLayout_3->setObjectName(QString::fromUtf8("verticalLayout_3"));
        verticalLayout_3->setContentsMargins(0, 0, 0, 0);
        label_3 = new QLabel(widget_5);
        label_3->setObjectName(QString::fromUtf8("label_3"));
        sizePolicy.setHeightForWidth(label_3->sizePolicy().hasHeightForWidth());
        label_3->setSizePolicy(sizePolicy);
        label_3->setMinimumSize(QSize(0, 0));
        label_3->setMaximumSize(QSize(16777215, 30));

        verticalLayout_3->addWidget(label_3);

        sample_rate_combobox = new QComboBox(widget_5);
        sample_rate_combobox->addItem(QString());
        sample_rate_combobox->addItem(QString());
        sample_rate_combobox->addItem(QString());
        sample_rate_combobox->addItem(QString());
        sample_rate_combobox->addItem(QString());
        sample_rate_combobox->addItem(QString());
        sample_rate_combobox->addItem(QString());
        sample_rate_combobox->addItem(QString());
        sample_rate_combobox->addItem(QString());
        sample_rate_combobox->addItem(QString());
        sample_rate_combobox->setObjectName(QString::fromUtf8("sample_rate_combobox"));
        sample_rate_combobox->setMinimumSize(QSize(0, 0));
        sample_rate_combobox->setMaximumSize(QSize(210, 16777215));
        sample_rate_combobox->setFont(font);

        verticalLayout_3->addWidget(sample_rate_combobox);


        gridLayout->addWidget(widget_5, 1, 0, 1, 1);

        widget_10 = new QWidget(widget_2);
        widget_10->setObjectName(QString::fromUtf8("widget_10"));
        verticalLayout_8 = new QVBoxLayout(widget_10);
        verticalLayout_8->setSpacing(0);
        verticalLayout_8->setObjectName(QString::fromUtf8("verticalLayout_8"));
        verticalLayout_8->setContentsMargins(0, 0, 0, 0);
        label_8 = new QLabel(widget_10);
        label_8->setObjectName(QString::fromUtf8("label_8"));
        label_8->setMaximumSize(QSize(110, 16777215));
        label_8->setFont(font);

        verticalLayout_8->addWidget(label_8);

        lineEdit_3 = new QLineEdit(widget_10);
        lineEdit_3->setObjectName(QString::fromUtf8("lineEdit_3"));
        lineEdit_3->setMinimumSize(QSize(0, 0));
        lineEdit_3->setFont(font);

        verticalLayout_8->addWidget(lineEdit_3);


        gridLayout->addWidget(widget_10, 1, 3, 1, 1);

        widget_3 = new QWidget(widget_2);
        widget_3->setObjectName(QString::fromUtf8("widget_3"));
        verticalLayout = new QVBoxLayout(widget_3);
        verticalLayout->setSpacing(0);
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        verticalLayout->setContentsMargins(0, 0, 0, 0);
        label = new QLabel(widget_3);
        label->setObjectName(QString::fromUtf8("label"));
        label->setMaximumSize(QSize(85, 16777215));

        verticalLayout->addWidget(label);

        stimulus_signal_type_combobox = new QComboBox(widget_3);
        stimulus_signal_type_combobox->addItem(QString());
        stimulus_signal_type_combobox->addItem(QString());
        stimulus_signal_type_combobox->addItem(QString());
        stimulus_signal_type_combobox->addItem(QString());
        stimulus_signal_type_combobox->setObjectName(QString::fromUtf8("stimulus_signal_type_combobox"));
        stimulus_signal_type_combobox->setMinimumSize(QSize(0, 0));
        stimulus_signal_type_combobox->setFont(font);

        verticalLayout->addWidget(stimulus_signal_type_combobox);


        gridLayout->addWidget(widget_3, 0, 0, 1, 1);


        verticalLayout_37->addWidget(widget_2);

        stackedWidget = new QStackedWidget(widget_control);
        stackedWidget->setObjectName(QString::fromUtf8("stackedWidget"));
        page = new QWidget();
        page->setObjectName(QString::fromUtf8("page"));
        verticalLayout_38 = new QVBoxLayout(page);
        verticalLayout_38->setObjectName(QString::fromUtf8("verticalLayout_38"));
        widget_8 = new QWidget(page);
        widget_8->setObjectName(QString::fromUtf8("widget_8"));
        widget_8->setMinimumSize(QSize(120, 0));
        widget_8->setMaximumSize(QSize(220, 16777215));
        verticalLayout_6 = new QVBoxLayout(widget_8);
        verticalLayout_6->setSpacing(0);
        verticalLayout_6->setObjectName(QString::fromUtf8("verticalLayout_6"));
        verticalLayout_6->setContentsMargins(0, 0, 0, 0);
        label_6 = new QLabel(widget_8);
        label_6->setObjectName(QString::fromUtf8("label_6"));
        sizePolicy.setHeightForWidth(label_6->sizePolicy().hasHeightForWidth());
        label_6->setSizePolicy(sizePolicy);
        label_6->setMinimumSize(QSize(0, 0));
        label_6->setMaximumSize(QSize(16777215, 30));

        verticalLayout_6->addWidget(label_6);

        Step_sweep_fre_point_typecomboBox = new QComboBox(widget_8);
        Step_sweep_fre_point_typecomboBox->addItem(QString());
        Step_sweep_fre_point_typecomboBox->addItem(QString());
        Step_sweep_fre_point_typecomboBox->setObjectName(QString::fromUtf8("Step_sweep_fre_point_typecomboBox"));
        Step_sweep_fre_point_typecomboBox->setMinimumSize(QSize(0, 0));
        Step_sweep_fre_point_typecomboBox->setMaximumSize(QSize(16777215, 16777215));
        Step_sweep_fre_point_typecomboBox->setFont(font);

        verticalLayout_6->addWidget(Step_sweep_fre_point_typecomboBox);


        verticalLayout_38->addWidget(widget_8);

        widget_15 = new QWidget(page);
        widget_15->setObjectName(QString::fromUtf8("widget_15"));
        widget_15->setMinimumSize(QSize(0, 0));
        gridLayout_2 = new QGridLayout(widget_15);
        gridLayout_2->setObjectName(QString::fromUtf8("gridLayout_2"));
        widget_11 = new QWidget(widget_15);
        widget_11->setObjectName(QString::fromUtf8("widget_11"));
        verticalLayout_9 = new QVBoxLayout(widget_11);
        verticalLayout_9->setSpacing(0);
        verticalLayout_9->setObjectName(QString::fromUtf8("verticalLayout_9"));
        verticalLayout_9->setContentsMargins(0, 0, 0, 0);
        label_9 = new QLabel(widget_11);
        label_9->setObjectName(QString::fromUtf8("label_9"));
        label_9->setMaximumSize(QSize(120, 16777215));
        label_9->setFont(font);

        verticalLayout_9->addWidget(label_9);

        Step_sweep_start_freqlineEdit = new QLineEdit(widget_11);
        Step_sweep_start_freqlineEdit->setObjectName(QString::fromUtf8("Step_sweep_start_freqlineEdit"));
        Step_sweep_start_freqlineEdit->setMinimumSize(QSize(0, 0));
        Step_sweep_start_freqlineEdit->setFont(font);

        verticalLayout_9->addWidget(Step_sweep_start_freqlineEdit);


        gridLayout_2->addWidget(widget_11, 1, 0, 1, 1);

        widget_14 = new QWidget(widget_15);
        widget_14->setObjectName(QString::fromUtf8("widget_14"));
        verticalLayout_12 = new QVBoxLayout(widget_14);
        verticalLayout_12->setSpacing(0);
        verticalLayout_12->setObjectName(QString::fromUtf8("verticalLayout_12"));
        verticalLayout_12->setContentsMargins(0, 0, 0, 0);
        label_12 = new QLabel(widget_14);
        label_12->setObjectName(QString::fromUtf8("label_12"));
        label_12->setMaximumSize(QSize(110, 16777215));
        label_12->setFont(font);

        verticalLayout_12->addWidget(label_12);

        Step_sweep_min_circleslineEdit = new QLineEdit(widget_14);
        Step_sweep_min_circleslineEdit->setObjectName(QString::fromUtf8("Step_sweep_min_circleslineEdit"));
        Step_sweep_min_circleslineEdit->setMinimumSize(QSize(0, 0));
        Step_sweep_min_circleslineEdit->setFont(font);

        verticalLayout_12->addWidget(Step_sweep_min_circleslineEdit);


        gridLayout_2->addWidget(widget_14, 2, 2, 1, 1);

        widget_13 = new QWidget(widget_15);
        widget_13->setObjectName(QString::fromUtf8("widget_13"));
        verticalLayout_11 = new QVBoxLayout(widget_13);
        verticalLayout_11->setSpacing(0);
        verticalLayout_11->setObjectName(QString::fromUtf8("verticalLayout_11"));
        verticalLayout_11->setContentsMargins(0, 0, 0, 0);
        label_11 = new QLabel(widget_13);
        label_11->setObjectName(QString::fromUtf8("label_11"));
        label_11->setMaximumSize(QSize(200, 16777215));
        label_11->setFont(font);

        verticalLayout_11->addWidget(label_11);

        Step_sweep_min_durationlineEdit = new QLineEdit(widget_13);
        Step_sweep_min_durationlineEdit->setObjectName(QString::fromUtf8("Step_sweep_min_durationlineEdit"));
        Step_sweep_min_durationlineEdit->setMinimumSize(QSize(0, 0));
        Step_sweep_min_durationlineEdit->setFont(font);

        verticalLayout_11->addWidget(Step_sweep_min_durationlineEdit);


        gridLayout_2->addWidget(widget_13, 2, 0, 1, 2);

        widget_12 = new QWidget(widget_15);
        widget_12->setObjectName(QString::fromUtf8("widget_12"));
        verticalLayout_10 = new QVBoxLayout(widget_12);
        verticalLayout_10->setSpacing(0);
        verticalLayout_10->setObjectName(QString::fromUtf8("verticalLayout_10"));
        verticalLayout_10->setContentsMargins(0, 0, 0, 0);
        label_10 = new QLabel(widget_12);
        label_10->setObjectName(QString::fromUtf8("label_10"));
        label_10->setMaximumSize(QSize(120, 16777215));
        label_10->setFont(font);

        verticalLayout_10->addWidget(label_10);

        Step_sweep_stop_freqlineEdit = new QLineEdit(widget_12);
        Step_sweep_stop_freqlineEdit->setObjectName(QString::fromUtf8("Step_sweep_stop_freqlineEdit"));
        Step_sweep_stop_freqlineEdit->setMinimumSize(QSize(0, 0));
        Step_sweep_stop_freqlineEdit->setFont(font);

        verticalLayout_10->addWidget(Step_sweep_stop_freqlineEdit);


        gridLayout_2->addWidget(widget_12, 1, 1, 1, 2);

        widget_9 = new QWidget(widget_15);
        widget_9->setObjectName(QString::fromUtf8("widget_9"));
        widget_9->setMinimumSize(QSize(120, 0));
        widget_9->setMaximumSize(QSize(220, 16777215));
        verticalLayout_7 = new QVBoxLayout(widget_9);
        verticalLayout_7->setSpacing(0);
        verticalLayout_7->setObjectName(QString::fromUtf8("verticalLayout_7"));
        verticalLayout_7->setContentsMargins(0, 0, 0, 0);
        label_7 = new QLabel(widget_9);
        label_7->setObjectName(QString::fromUtf8("label_7"));
        sizePolicy.setHeightForWidth(label_7->sizePolicy().hasHeightForWidth());
        label_7->setSizePolicy(sizePolicy);
        label_7->setMinimumSize(QSize(0, 0));
        label_7->setMaximumSize(QSize(16777215, 30));

        verticalLayout_7->addWidget(label_7);

        Step_sweep_octave_typecomboBox = new QComboBox(widget_9);
        Step_sweep_octave_typecomboBox->addItem(QString());
        Step_sweep_octave_typecomboBox->addItem(QString());
        Step_sweep_octave_typecomboBox->addItem(QString());
        Step_sweep_octave_typecomboBox->addItem(QString());
        Step_sweep_octave_typecomboBox->setObjectName(QString::fromUtf8("Step_sweep_octave_typecomboBox"));
        Step_sweep_octave_typecomboBox->setMinimumSize(QSize(0, 0));
        Step_sweep_octave_typecomboBox->setMaximumSize(QSize(16777215, 16777215));
        Step_sweep_octave_typecomboBox->setFont(font);

        verticalLayout_7->addWidget(Step_sweep_octave_typecomboBox);


        gridLayout_2->addWidget(widget_9, 0, 0, 1, 1);


        verticalLayout_38->addWidget(widget_15);

        Step_sweepButtonwidget = new QWidget(page);
        Step_sweepButtonwidget->setObjectName(QString::fromUtf8("Step_sweepButtonwidget"));
        horizontalLayout_2 = new QHBoxLayout(Step_sweepButtonwidget);
        horizontalLayout_2->setObjectName(QString::fromUtf8("horizontalLayout_2"));
        Step_sweepInputButton = new QPushButton(Step_sweepButtonwidget);
        Step_sweepInputButton->setObjectName(QString::fromUtf8("Step_sweepInputButton"));

        horizontalLayout_2->addWidget(Step_sweepInputButton);

        Step_sweepOutputButton = new QPushButton(Step_sweepButtonwidget);
        Step_sweepOutputButton->setObjectName(QString::fromUtf8("Step_sweepOutputButton"));

        horizontalLayout_2->addWidget(Step_sweepOutputButton);


        verticalLayout_38->addWidget(Step_sweepButtonwidget);

        verticalSpacer_3 = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout_38->addItem(verticalSpacer_3);

        stackedWidget->addWidget(page);
        page_2 = new QWidget();
        page_2->setObjectName(QString::fromUtf8("page_2"));
        gridLayout_9 = new QGridLayout(page_2);
        gridLayout_9->setObjectName(QString::fromUtf8("gridLayout_9"));
        widget_16 = new QWidget(page_2);
        widget_16->setObjectName(QString::fromUtf8("widget_16"));
        widget_16->setMinimumSize(QSize(0, 0));
        gridLayout_3 = new QGridLayout(widget_16);
        gridLayout_3->setObjectName(QString::fromUtf8("gridLayout_3"));
        widget_24 = new QWidget(widget_16);
        widget_24->setObjectName(QString::fromUtf8("widget_24"));
        verticalLayout_20 = new QVBoxLayout(widget_24);
        verticalLayout_20->setSpacing(0);
        verticalLayout_20->setObjectName(QString::fromUtf8("verticalLayout_20"));
        verticalLayout_20->setContentsMargins(0, 0, 0, 0);
        label_20 = new QLabel(widget_24);
        label_20->setObjectName(QString::fromUtf8("label_20"));
        label_20->setMaximumSize(QSize(110, 16777215));
        label_20->setFont(font);

        verticalLayout_20->addWidget(label_20);

        conlogsweep_delay_durationlineEdit = new QLineEdit(widget_24);
        conlogsweep_delay_durationlineEdit->setObjectName(QString::fromUtf8("conlogsweep_delay_durationlineEdit"));
        conlogsweep_delay_durationlineEdit->setMinimumSize(QSize(0, 0));
        conlogsweep_delay_durationlineEdit->setFont(font);

        verticalLayout_20->addWidget(conlogsweep_delay_durationlineEdit);


        gridLayout_3->addWidget(widget_24, 2, 0, 1, 1);

        widget_22 = new QWidget(widget_16);
        widget_22->setObjectName(QString::fromUtf8("widget_22"));
        verticalLayout_18 = new QVBoxLayout(widget_22);
        verticalLayout_18->setSpacing(0);
        verticalLayout_18->setObjectName(QString::fromUtf8("verticalLayout_18"));
        verticalLayout_18->setContentsMargins(0, 0, 0, 0);
        label_18 = new QLabel(widget_22);
        label_18->setObjectName(QString::fromUtf8("label_18"));
        label_18->setMaximumSize(QSize(110, 16777215));
        label_18->setFont(font);

        verticalLayout_18->addWidget(label_18);

        conlogsweep_pre_durationlineEdit = new QLineEdit(widget_22);
        conlogsweep_pre_durationlineEdit->setObjectName(QString::fromUtf8("conlogsweep_pre_durationlineEdit"));
        conlogsweep_pre_durationlineEdit->setMinimumSize(QSize(0, 0));
        conlogsweep_pre_durationlineEdit->setFont(font);

        verticalLayout_18->addWidget(conlogsweep_pre_durationlineEdit);


        gridLayout_3->addWidget(widget_22, 1, 2, 1, 1);

        widget_20 = new QWidget(widget_16);
        widget_20->setObjectName(QString::fromUtf8("widget_20"));
        verticalLayout_16 = new QVBoxLayout(widget_20);
        verticalLayout_16->setSpacing(0);
        verticalLayout_16->setObjectName(QString::fromUtf8("verticalLayout_16"));
        verticalLayout_16->setContentsMargins(0, 0, 0, 0);
        label_16 = new QLabel(widget_20);
        label_16->setObjectName(QString::fromUtf8("label_16"));
        label_16->setMaximumSize(QSize(120, 16777215));
        label_16->setFont(font);

        verticalLayout_16->addWidget(label_16);

        conlogsweep_stop_freqlineEdit = new QLineEdit(widget_20);
        conlogsweep_stop_freqlineEdit->setObjectName(QString::fromUtf8("conlogsweep_stop_freqlineEdit"));
        conlogsweep_stop_freqlineEdit->setMinimumSize(QSize(0, 0));
        conlogsweep_stop_freqlineEdit->setFont(font);

        verticalLayout_16->addWidget(conlogsweep_stop_freqlineEdit);


        gridLayout_3->addWidget(widget_20, 0, 1, 1, 2);

        widget_21 = new QWidget(widget_16);
        widget_21->setObjectName(QString::fromUtf8("widget_21"));
        verticalLayout_17 = new QVBoxLayout(widget_21);
        verticalLayout_17->setSpacing(0);
        verticalLayout_17->setObjectName(QString::fromUtf8("verticalLayout_17"));
        verticalLayout_17->setContentsMargins(0, 0, 0, 0);
        label_17 = new QLabel(widget_21);
        label_17->setObjectName(QString::fromUtf8("label_17"));
        label_17->setMaximumSize(QSize(200, 16777215));
        label_17->setFont(font);

        verticalLayout_17->addWidget(label_17);

        conlogsweep_durationlineEdit = new QLineEdit(widget_21);
        conlogsweep_durationlineEdit->setObjectName(QString::fromUtf8("conlogsweep_durationlineEdit"));
        conlogsweep_durationlineEdit->setMinimumSize(QSize(0, 0));
        conlogsweep_durationlineEdit->setFont(font);

        verticalLayout_17->addWidget(conlogsweep_durationlineEdit);


        gridLayout_3->addWidget(widget_21, 1, 0, 1, 2);

        widget_19 = new QWidget(widget_16);
        widget_19->setObjectName(QString::fromUtf8("widget_19"));
        verticalLayout_15 = new QVBoxLayout(widget_19);
        verticalLayout_15->setSpacing(0);
        verticalLayout_15->setObjectName(QString::fromUtf8("verticalLayout_15"));
        verticalLayout_15->setContentsMargins(0, 0, 0, 0);
        label_15 = new QLabel(widget_19);
        label_15->setObjectName(QString::fromUtf8("label_15"));
        label_15->setMaximumSize(QSize(120, 16777215));
        label_15->setFont(font);

        verticalLayout_15->addWidget(label_15);

        conlogsweep_start_freqlineEdit = new QLineEdit(widget_19);
        conlogsweep_start_freqlineEdit->setObjectName(QString::fromUtf8("conlogsweep_start_freqlineEdit"));
        conlogsweep_start_freqlineEdit->setMinimumSize(QSize(0, 0));
        conlogsweep_start_freqlineEdit->setFont(font);

        verticalLayout_15->addWidget(conlogsweep_start_freqlineEdit);


        gridLayout_3->addWidget(widget_19, 0, 0, 1, 1);

        verticalSpacer = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        gridLayout_3->addItem(verticalSpacer, 3, 0, 1, 1);


        gridLayout_9->addWidget(widget_16, 0, 0, 1, 1);

        stackedWidget->addWidget(page_2);
        page_3 = new QWidget();
        page_3->setObjectName(QString::fromUtf8("page_3"));
        gridLayout_8 = new QGridLayout(page_3);
        gridLayout_8->setObjectName(QString::fromUtf8("gridLayout_8"));
        level_stepButtonwidget = new QWidget(page_3);
        level_stepButtonwidget->setObjectName(QString::fromUtf8("level_stepButtonwidget"));
        horizontalLayout = new QHBoxLayout(level_stepButtonwidget);
        horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));
        level_stepInputButton = new QPushButton(level_stepButtonwidget);
        level_stepInputButton->setObjectName(QString::fromUtf8("level_stepInputButton"));

        horizontalLayout->addWidget(level_stepInputButton);

        level_stepOutPutButton = new QPushButton(level_stepButtonwidget);
        level_stepOutPutButton->setObjectName(QString::fromUtf8("level_stepOutPutButton"));

        horizontalLayout->addWidget(level_stepOutPutButton);


        gridLayout_8->addWidget(level_stepButtonwidget, 4, 0, 1, 1);

        widget = new QWidget(page_3);
        widget->setObjectName(QString::fromUtf8("widget"));
        horizontalLayout_7 = new QHBoxLayout(widget);
        horizontalLayout_7->setObjectName(QString::fromUtf8("horizontalLayout_7"));
        widget_27 = new QWidget(widget);
        widget_27->setObjectName(QString::fromUtf8("widget_27"));
        verticalLayout_22 = new QVBoxLayout(widget_27);
        verticalLayout_22->setSpacing(0);
        verticalLayout_22->setObjectName(QString::fromUtf8("verticalLayout_22"));
        verticalLayout_22->setContentsMargins(0, 0, 0, 0);
        label_22 = new QLabel(widget_27);
        label_22->setObjectName(QString::fromUtf8("label_22"));
        label_22->setMaximumSize(QSize(200, 16777215));
        label_22->setFont(font);

        verticalLayout_22->addWidget(label_22);

        level_step_step_durationlineEdit = new QLineEdit(widget_27);
        level_step_step_durationlineEdit->setObjectName(QString::fromUtf8("level_step_step_durationlineEdit"));
        level_step_step_durationlineEdit->setMinimumSize(QSize(0, 0));
        level_step_step_durationlineEdit->setFont(font);

        verticalLayout_22->addWidget(level_step_step_durationlineEdit);


        horizontalLayout_7->addWidget(widget_27);

        widget_28 = new QWidget(widget);
        widget_28->setObjectName(QString::fromUtf8("widget_28"));
        verticalLayout_23 = new QVBoxLayout(widget_28);
        verticalLayout_23->setSpacing(0);
        verticalLayout_23->setObjectName(QString::fromUtf8("verticalLayout_23"));
        verticalLayout_23->setContentsMargins(0, 0, 0, 0);
        label_23 = new QLabel(widget_28);
        label_23->setObjectName(QString::fromUtf8("label_23"));
        label_23->setMaximumSize(QSize(110, 16777215));
        label_23->setFont(font);

        verticalLayout_23->addWidget(label_23);

        level_step_step_circleslineEdit = new QLineEdit(widget_28);
        level_step_step_circleslineEdit->setObjectName(QString::fromUtf8("level_step_step_circleslineEdit"));
        level_step_step_circleslineEdit->setMinimumSize(QSize(0, 0));
        level_step_step_circleslineEdit->setFont(font);

        verticalLayout_23->addWidget(level_step_step_circleslineEdit);


        horizontalLayout_7->addWidget(widget_28);


        gridLayout_8->addWidget(widget, 3, 0, 1, 1);

        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        gridLayout_8->addItem(horizontalSpacer, 2, 1, 1, 1);

        widget_17 = new QWidget(page_3);
        widget_17->setObjectName(QString::fromUtf8("widget_17"));
        widget_17->setMinimumSize(QSize(0, 0));
        gridLayout_4 = new QGridLayout(widget_17);
        gridLayout_4->setSpacing(7);
        gridLayout_4->setObjectName(QString::fromUtf8("gridLayout_4"));
        gridLayout_4->setContentsMargins(11, 2, 11, 2);
        widget_29 = new QWidget(widget_17);
        widget_29->setObjectName(QString::fromUtf8("widget_29"));
        verticalLayout_30 = new QVBoxLayout(widget_29);
        verticalLayout_30->setSpacing(0);
        verticalLayout_30->setObjectName(QString::fromUtf8("verticalLayout_30"));
        verticalLayout_30->setContentsMargins(0, 0, 0, 0);
        label_30 = new QLabel(widget_29);
        label_30->setObjectName(QString::fromUtf8("label_30"));
        label_30->setMaximumSize(QSize(200, 16777215));
        label_30->setFont(font);

        verticalLayout_30->addWidget(label_30);

        level_step_pointslineEdit = new QLineEdit(widget_29);
        level_step_pointslineEdit->setObjectName(QString::fromUtf8("level_step_pointslineEdit"));
        level_step_pointslineEdit->setMinimumSize(QSize(0, 0));
        level_step_pointslineEdit->setFont(font);

        verticalLayout_30->addWidget(level_step_pointslineEdit);


        gridLayout_4->addWidget(widget_29, 0, 1, 1, 1);

        widget_25 = new QWidget(widget_17);
        widget_25->setObjectName(QString::fromUtf8("widget_25"));
        verticalLayout_19 = new QVBoxLayout(widget_25);
        verticalLayout_19->setSpacing(0);
        verticalLayout_19->setObjectName(QString::fromUtf8("verticalLayout_19"));
        verticalLayout_19->setContentsMargins(0, 0, 0, 0);
        label_19 = new QLabel(widget_25);
        label_19->setObjectName(QString::fromUtf8("label_19"));
        label_19->setMaximumSize(QSize(120, 16777215));
        label_19->setFont(font);

        verticalLayout_19->addWidget(label_19);

        level_step_start_levellineEdit = new QLineEdit(widget_25);
        level_step_start_levellineEdit->setObjectName(QString::fromUtf8("level_step_start_levellineEdit"));
        level_step_start_levellineEdit->setMinimumSize(QSize(0, 0));
        level_step_start_levellineEdit->setFont(font);

        verticalLayout_19->addWidget(level_step_start_levellineEdit);


        gridLayout_4->addWidget(widget_25, 1, 0, 1, 1);

        widget_26 = new QWidget(widget_17);
        widget_26->setObjectName(QString::fromUtf8("widget_26"));
        verticalLayout_21 = new QVBoxLayout(widget_26);
        verticalLayout_21->setSpacing(0);
        verticalLayout_21->setObjectName(QString::fromUtf8("verticalLayout_21"));
        verticalLayout_21->setContentsMargins(0, 0, 0, 0);
        label_21 = new QLabel(widget_26);
        label_21->setObjectName(QString::fromUtf8("label_21"));
        label_21->setMaximumSize(QSize(120, 16777215));
        label_21->setFont(font);

        verticalLayout_21->addWidget(label_21);

        level_step_stop_levellineEdit = new QLineEdit(widget_26);
        level_step_stop_levellineEdit->setObjectName(QString::fromUtf8("level_step_stop_levellineEdit"));
        level_step_stop_levellineEdit->setMinimumSize(QSize(0, 0));
        level_step_stop_levellineEdit->setFont(font);

        verticalLayout_21->addWidget(level_step_stop_levellineEdit);


        gridLayout_4->addWidget(widget_26, 1, 1, 1, 2);

        widget_23 = new QWidget(widget_17);
        widget_23->setObjectName(QString::fromUtf8("widget_23"));
        widget_23->setMinimumSize(QSize(120, 0));
        widget_23->setMaximumSize(QSize(220, 16777215));
        verticalLayout_14 = new QVBoxLayout(widget_23);
        verticalLayout_14->setSpacing(0);
        verticalLayout_14->setObjectName(QString::fromUtf8("verticalLayout_14"));
        verticalLayout_14->setContentsMargins(0, 0, 0, 0);
        label_14 = new QLabel(widget_23);
        label_14->setObjectName(QString::fromUtf8("label_14"));
        sizePolicy.setHeightForWidth(label_14->sizePolicy().hasHeightForWidth());
        label_14->setSizePolicy(sizePolicy);
        label_14->setMinimumSize(QSize(0, 0));
        label_14->setMaximumSize(QSize(16777215, 30));

        verticalLayout_14->addWidget(label_14);

        level_step_amplitdeTypeSampcomboBox = new QComboBox(widget_23);
        level_step_amplitdeTypeSampcomboBox->addItem(QString());
        level_step_amplitdeTypeSampcomboBox->addItem(QString());
        level_step_amplitdeTypeSampcomboBox->setObjectName(QString::fromUtf8("level_step_amplitdeTypeSampcomboBox"));
        level_step_amplitdeTypeSampcomboBox->setMinimumSize(QSize(0, 0));
        level_step_amplitdeTypeSampcomboBox->setMaximumSize(QSize(210, 16777215));
        level_step_amplitdeTypeSampcomboBox->setFont(font);

        verticalLayout_14->addWidget(level_step_amplitdeTypeSampcomboBox);


        gridLayout_4->addWidget(widget_23, 0, 0, 1, 1);


        gridLayout_8->addWidget(widget_17, 2, 0, 1, 1);

        widget_30 = new QWidget(page_3);
        widget_30->setObjectName(QString::fromUtf8("widget_30"));
        widget_30->setMaximumSize(QSize(100, 16777215));
        verticalLayout_25 = new QVBoxLayout(widget_30);
        verticalLayout_25->setSpacing(0);
        verticalLayout_25->setObjectName(QString::fromUtf8("verticalLayout_25"));
        verticalLayout_25->setContentsMargins(0, 0, 0, 0);
        label_25 = new QLabel(widget_30);
        label_25->setObjectName(QString::fromUtf8("label_25"));
        label_25->setMaximumSize(QSize(120, 16777215));
        label_25->setFont(font);

        verticalLayout_25->addWidget(label_25);

        level_step_frequencylineEdit = new QLineEdit(widget_30);
        level_step_frequencylineEdit->setObjectName(QString::fromUtf8("level_step_frequencylineEdit"));
        level_step_frequencylineEdit->setMinimumSize(QSize(0, 0));
        level_step_frequencylineEdit->setFont(font);

        verticalLayout_25->addWidget(level_step_frequencylineEdit);


        gridLayout_8->addWidget(widget_30, 0, 0, 1, 1);

        widget_18 = new QWidget(page_3);
        widget_18->setObjectName(QString::fromUtf8("widget_18"));
        widget_18->setMinimumSize(QSize(120, 0));
        widget_18->setMaximumSize(QSize(220, 16777215));
        verticalLayout_13 = new QVBoxLayout(widget_18);
        verticalLayout_13->setSpacing(0);
        verticalLayout_13->setObjectName(QString::fromUtf8("verticalLayout_13"));
        verticalLayout_13->setContentsMargins(0, 0, 0, 0);
        label_13 = new QLabel(widget_18);
        label_13->setObjectName(QString::fromUtf8("label_13"));
        sizePolicy.setHeightForWidth(label_13->sizePolicy().hasHeightForWidth());
        label_13->setSizePolicy(sizePolicy);
        label_13->setMinimumSize(QSize(0, 0));
        label_13->setMaximumSize(QSize(16777215, 30));

        verticalLayout_13->addWidget(label_13);

        level_step_levelTypeSampcomboBox = new QComboBox(widget_18);
        level_step_levelTypeSampcomboBox->addItem(QString());
        level_step_levelTypeSampcomboBox->addItem(QString());
        level_step_levelTypeSampcomboBox->setObjectName(QString::fromUtf8("level_step_levelTypeSampcomboBox"));
        level_step_levelTypeSampcomboBox->setMinimumSize(QSize(0, 0));
        level_step_levelTypeSampcomboBox->setMaximumSize(QSize(210, 16777215));
        level_step_levelTypeSampcomboBox->setFont(font);

        verticalLayout_13->addWidget(level_step_levelTypeSampcomboBox);


        gridLayout_8->addWidget(widget_18, 1, 0, 1, 1);

        verticalSpacer_4 = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        gridLayout_8->addItem(verticalSpacer_4, 5, 0, 1, 1);

        stackedWidget->addWidget(page_3);
        page_4 = new QWidget();
        page_4->setObjectName(QString::fromUtf8("page_4"));
        verticalLayout_32 = new QVBoxLayout(page_4);
        verticalLayout_32->setObjectName(QString::fromUtf8("verticalLayout_32"));
        widget_43 = new QWidget(page_4);
        widget_43->setObjectName(QString::fromUtf8("widget_43"));
        widget_43->setMinimumSize(QSize(120, 0));
        widget_43->setMaximumSize(QSize(220, 16777215));
        verticalLayout_39 = new QVBoxLayout(widget_43);
        verticalLayout_39->setSpacing(0);
        verticalLayout_39->setObjectName(QString::fromUtf8("verticalLayout_39"));
        verticalLayout_39->setContentsMargins(0, 0, 0, 0);
        label_37 = new QLabel(widget_43);
        label_37->setObjectName(QString::fromUtf8("label_37"));
        sizePolicy.setHeightForWidth(label_37->sizePolicy().hasHeightForWidth());
        label_37->setSizePolicy(sizePolicy);
        label_37->setMinimumSize(QSize(0, 0));
        label_37->setMaximumSize(QSize(16777215, 30));

        verticalLayout_39->addWidget(label_37);

        PS_stepped_freq_typecomboBox = new QComboBox(widget_43);
        PS_stepped_freq_typecomboBox->addItem(QString());
        PS_stepped_freq_typecomboBox->addItem(QString());
        PS_stepped_freq_typecomboBox->setObjectName(QString::fromUtf8("PS_stepped_freq_typecomboBox"));
        PS_stepped_freq_typecomboBox->setMinimumSize(QSize(0, 0));
        PS_stepped_freq_typecomboBox->setMaximumSize(QSize(210, 16777215));
        PS_stepped_freq_typecomboBox->setFont(font);

        verticalLayout_39->addWidget(PS_stepped_freq_typecomboBox);


        verticalLayout_32->addWidget(widget_43);

        widget_36 = new QWidget(page_4);
        widget_36->setObjectName(QString::fromUtf8("widget_36"));
        widget_36->setMinimumSize(QSize(0, 0));
        gridLayout_6 = new QGridLayout(widget_36);
        gridLayout_6->setObjectName(QString::fromUtf8("gridLayout_6"));
        widget_38 = new QWidget(widget_36);
        widget_38->setObjectName(QString::fromUtf8("widget_38"));
        verticalLayout_31 = new QVBoxLayout(widget_38);
        verticalLayout_31->setSpacing(0);
        verticalLayout_31->setObjectName(QString::fromUtf8("verticalLayout_31"));
        verticalLayout_31->setContentsMargins(0, 0, 0, 0);
        label_32 = new QLabel(widget_38);
        label_32->setObjectName(QString::fromUtf8("label_32"));
        label_32->setMaximumSize(QSize(200, 16777215));
        label_32->setFont(font);

        verticalLayout_31->addWidget(label_32);

        PS_stepped_pointslineEdit = new QLineEdit(widget_38);
        PS_stepped_pointslineEdit->setObjectName(QString::fromUtf8("PS_stepped_pointslineEdit"));
        PS_stepped_pointslineEdit->setMinimumSize(QSize(0, 0));
        PS_stepped_pointslineEdit->setFont(font);

        verticalLayout_31->addWidget(PS_stepped_pointslineEdit);


        gridLayout_6->addWidget(widget_38, 0, 1, 1, 1);

        widget_39 = new QWidget(widget_36);
        widget_39->setObjectName(QString::fromUtf8("widget_39"));
        widget_39->setMinimumSize(QSize(0, 0));
        widget_39->setMaximumSize(QSize(16777215, 16777215));
        verticalLayout_35 = new QVBoxLayout(widget_39);
        verticalLayout_35->setSpacing(0);
        verticalLayout_35->setObjectName(QString::fromUtf8("verticalLayout_35"));
        verticalLayout_35->setContentsMargins(0, 0, 0, 0);
        label_33 = new QLabel(widget_39);
        label_33->setObjectName(QString::fromUtf8("label_33"));
        sizePolicy.setHeightForWidth(label_33->sizePolicy().hasHeightForWidth());
        label_33->setSizePolicy(sizePolicy);
        label_33->setMinimumSize(QSize(0, 0));
        label_33->setMaximumSize(QSize(16777215, 30));

        verticalLayout_35->addWidget(label_33);

        PS_stepped_step_typecomboBox = new QComboBox(widget_39);
        PS_stepped_step_typecomboBox->addItem(QString());
        PS_stepped_step_typecomboBox->addItem(QString());
        PS_stepped_step_typecomboBox->setObjectName(QString::fromUtf8("PS_stepped_step_typecomboBox"));
        PS_stepped_step_typecomboBox->setMinimumSize(QSize(0, 0));
        PS_stepped_step_typecomboBox->setMaximumSize(QSize(210, 16777215));
        PS_stepped_step_typecomboBox->setFont(font);

        verticalLayout_35->addWidget(PS_stepped_step_typecomboBox);


        gridLayout_6->addWidget(widget_39, 0, 0, 1, 1);

        widget_40 = new QWidget(widget_36);
        widget_40->setObjectName(QString::fromUtf8("widget_40"));
        verticalLayout_36 = new QVBoxLayout(widget_40);
        verticalLayout_36->setSpacing(0);
        verticalLayout_36->setObjectName(QString::fromUtf8("verticalLayout_36"));
        verticalLayout_36->setContentsMargins(0, 0, 0, 0);
        label_34 = new QLabel(widget_40);
        label_34->setObjectName(QString::fromUtf8("label_34"));
        label_34->setMaximumSize(QSize(120, 16777215));
        label_34->setFont(font);

        verticalLayout_36->addWidget(label_34);

        PS_stepped_start_freqlineEdit = new QLineEdit(widget_40);
        PS_stepped_start_freqlineEdit->setObjectName(QString::fromUtf8("PS_stepped_start_freqlineEdit"));
        PS_stepped_start_freqlineEdit->setMinimumSize(QSize(0, 0));
        PS_stepped_start_freqlineEdit->setFont(font);

        verticalLayout_36->addWidget(PS_stepped_start_freqlineEdit);


        gridLayout_6->addWidget(widget_40, 1, 0, 1, 1);

        widget_37 = new QWidget(widget_36);
        widget_37->setObjectName(QString::fromUtf8("widget_37"));
        verticalLayout_33 = new QVBoxLayout(widget_37);
        verticalLayout_33->setSpacing(0);
        verticalLayout_33->setObjectName(QString::fromUtf8("verticalLayout_33"));
        verticalLayout_33->setContentsMargins(0, 0, 0, 0);
        label_31 = new QLabel(widget_37);
        label_31->setObjectName(QString::fromUtf8("label_31"));
        label_31->setMaximumSize(QSize(120, 16777215));
        label_31->setFont(font);

        verticalLayout_33->addWidget(label_31);

        PS_stepped_stop_freqlineEdit = new QLineEdit(widget_37);
        PS_stepped_stop_freqlineEdit->setObjectName(QString::fromUtf8("PS_stepped_stop_freqlineEdit"));
        PS_stepped_stop_freqlineEdit->setMinimumSize(QSize(0, 0));
        PS_stepped_stop_freqlineEdit->setFont(font);

        verticalLayout_33->addWidget(PS_stepped_stop_freqlineEdit);


        gridLayout_6->addWidget(widget_37, 1, 1, 1, 1);

        horizontalSpacer_2 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        gridLayout_6->addItem(horizontalSpacer_2, 1, 2, 1, 1);


        verticalLayout_32->addWidget(widget_36);

        PS_steppedButtonwidget = new QWidget(page_4);
        PS_steppedButtonwidget->setObjectName(QString::fromUtf8("PS_steppedButtonwidget"));
        horizontalLayout_3 = new QHBoxLayout(PS_steppedButtonwidget);
        horizontalLayout_3->setObjectName(QString::fromUtf8("horizontalLayout_3"));
        PS_steppedInputButton = new QPushButton(PS_steppedButtonwidget);
        PS_steppedInputButton->setObjectName(QString::fromUtf8("PS_steppedInputButton"));

        horizontalLayout_3->addWidget(PS_steppedInputButton);

        PS_steppedOutputButton = new QPushButton(PS_steppedButtonwidget);
        PS_steppedOutputButton->setObjectName(QString::fromUtf8("PS_steppedOutputButton"));

        horizontalLayout_3->addWidget(PS_steppedOutputButton);


        verticalLayout_32->addWidget(PS_steppedButtonwidget);

        verticalSpacer_2 = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout_32->addItem(verticalSpacer_2);

        stackedWidget->addWidget(page_4);

        verticalLayout_37->addWidget(stackedWidget);


        horizontalLayout_4->addWidget(widget_control);

        FreManagetableWidget = new QTableWidget(StimulusSignalForm);
        FreManagetableWidget->setObjectName(QString::fromUtf8("FreManagetableWidget"));

        horizontalLayout_4->addWidget(FreManagetableWidget);


        verticalLayout_34->addLayout(horizontalLayout_4);


        horizontalLayout_6->addLayout(verticalLayout_34);


        verticalLayout_40->addLayout(horizontalLayout_6);


        retranslateUi(StimulusSignalForm);

        stackedWidget->setCurrentIndex(2);


        QMetaObject::connectSlotsByName(StimulusSignalForm);
    } // setupUi

    void retranslateUi(QWidget *StimulusSignalForm)
    {
        StimulusSignalForm->setWindowTitle(QCoreApplication::translate("StimulusSignalForm", "Form", nullptr));
        pushButton_7->setText(QCoreApplication::translate("StimulusSignalForm", "\345\210\233\345\273\272WAV\346\226\207\344\273\266", nullptr));
        pushButton_8->setText(QCoreApplication::translate("StimulusSignalForm", "\344\277\235\345\255\230", nullptr));
        pushButton_9->setText(QCoreApplication::translate("StimulusSignalForm", "\351\200\200\345\207\272", nullptr));
        label_5->setText(QCoreApplication::translate("StimulusSignalForm", "<html><head/><body><p>\344\277\241\345\217\267\345\215\225\344\275\215</p></body></html>", nullptr));
        signal_unitcomboBox->setItemText(0, QCoreApplication::translate("StimulusSignalForm", "Vrms", nullptr));
        signal_unitcomboBox->setItemText(1, QCoreApplication::translate("StimulusSignalForm", "Vp", nullptr));
        signal_unitcomboBox->setItemText(2, QCoreApplication::translate("StimulusSignalForm", "Vpp", nullptr));
        signal_unitcomboBox->setItemText(3, QCoreApplication::translate("StimulusSignalForm", "dBV", nullptr));
        signal_unitcomboBox->setItemText(4, QCoreApplication::translate("StimulusSignalForm", "dBu", nullptr));
        signal_unitcomboBox->setItemText(5, QCoreApplication::translate("StimulusSignalForm", "dBFS", nullptr));

        label_2->setText(QCoreApplication::translate("StimulusSignalForm", "\346\224\276\345\244\247\345\242\236\347\233\212dB", nullptr));
        Gain_dBlineEdit->setText(QCoreApplication::translate("StimulusSignalForm", "0", nullptr));
        label_4->setText(QCoreApplication::translate("StimulusSignalForm", "\346\224\276\345\244\247\345\200\215\346\225\260", nullptr));
        Gain_constantlineEdit->setText(QCoreApplication::translate("StimulusSignalForm", "1", nullptr));
        label_3->setText(QCoreApplication::translate("StimulusSignalForm", "<html><head/><body><p><span style=\" font-size:12pt;\">\351\207\207\346\240\267\347\216\207</span></p></body></html>", nullptr));
        sample_rate_combobox->setItemText(0, QCoreApplication::translate("StimulusSignalForm", "8000 Hz", nullptr));
        sample_rate_combobox->setItemText(1, QCoreApplication::translate("StimulusSignalForm", "11025 Hz", nullptr));
        sample_rate_combobox->setItemText(2, QCoreApplication::translate("StimulusSignalForm", "16000 Hz", nullptr));
        sample_rate_combobox->setItemText(3, QCoreApplication::translate("StimulusSignalForm", "22050 Hz", nullptr));
        sample_rate_combobox->setItemText(4, QCoreApplication::translate("StimulusSignalForm", "44100 Hz", nullptr));
        sample_rate_combobox->setItemText(5, QCoreApplication::translate("StimulusSignalForm", "48000 Hz", nullptr));
        sample_rate_combobox->setItemText(6, QCoreApplication::translate("StimulusSignalForm", "88200 Hz", nullptr));
        sample_rate_combobox->setItemText(7, QCoreApplication::translate("StimulusSignalForm", "96000 Hz", nullptr));
        sample_rate_combobox->setItemText(8, QCoreApplication::translate("StimulusSignalForm", "176400 Hz", nullptr));
        sample_rate_combobox->setItemText(9, QCoreApplication::translate("StimulusSignalForm", "192000 Hz", nullptr));

        label_8->setText(QCoreApplication::translate("StimulusSignalForm", "\344\277\241\345\217\267\345\271\205\345\200\274", nullptr));
        lineEdit_3->setText(QCoreApplication::translate("StimulusSignalForm", "1", nullptr));
        label->setText(QCoreApplication::translate("StimulusSignalForm", "<html><head/><body><p><span style=\" font-size:12pt;\">\344\277\241\345\217\267\347\261\273\345\236\213</span></p></body></html>", nullptr));
        stimulus_signal_type_combobox->setItemText(0, QCoreApplication::translate("StimulusSignalForm", "\346\255\245\350\277\233\346\211\253\351\242\221", nullptr));
        stimulus_signal_type_combobox->setItemText(1, QCoreApplication::translate("StimulusSignalForm", "\350\277\236\347\273\255\345\257\271\346\225\260\346\211\253\351\242\221", nullptr));
        stimulus_signal_type_combobox->setItemText(2, QCoreApplication::translate("StimulusSignalForm", "\346\255\245\350\277\233\346\211\253\345\271\205\345\200\274", nullptr));
        stimulus_signal_type_combobox->setItemText(3, QCoreApplication::translate("StimulusSignalForm", "\345\212\237\347\216\207\345\271\263\346\273\221", nullptr));

        label_6->setText(QCoreApplication::translate("StimulusSignalForm", "<html><head/><body><p><span style=\" font-size:12pt;\">\351\242\221\347\202\271\347\261\273\345\236\213</span></p></body></html>", nullptr));
        Step_sweep_fre_point_typecomboBox->setItemText(0, QCoreApplication::translate("StimulusSignalForm", "\345\200\215\351\242\221\347\250\213", nullptr));
        Step_sweep_fre_point_typecomboBox->setItemText(1, QCoreApplication::translate("StimulusSignalForm", "\350\207\252\345\256\232\344\271\211", nullptr));

        label_9->setText(QCoreApplication::translate("StimulusSignalForm", "\345\274\200\345\247\213\351\242\221\347\216\207(Hz)", nullptr));
        Step_sweep_start_freqlineEdit->setText(QCoreApplication::translate("StimulusSignalForm", "20", nullptr));
        label_12->setText(QCoreApplication::translate("StimulusSignalForm", "\346\234\200\345\260\221\345\221\250\346\234\237\346\225\260", nullptr));
        Step_sweep_min_circleslineEdit->setText(QCoreApplication::translate("StimulusSignalForm", "10", nullptr));
        label_11->setText(QCoreApplication::translate("StimulusSignalForm", "\345\221\250\346\234\237\346\214\201\347\273\255\346\234\200\345\260\221\346\227\266\351\227\264(S)", nullptr));
        Step_sweep_min_durationlineEdit->setText(QCoreApplication::translate("StimulusSignalForm", "0.01", nullptr));
        label_10->setText(QCoreApplication::translate("StimulusSignalForm", "\347\273\223\346\235\237\351\242\221\347\216\207(Hz)", nullptr));
        Step_sweep_stop_freqlineEdit->setText(QCoreApplication::translate("StimulusSignalForm", "20", nullptr));
        label_7->setText(QCoreApplication::translate("StimulusSignalForm", "<html><head/><body><p><span style=\" font-size:12pt;\">\345\200\215\351\242\221\347\250\213</span></p></body></html>", nullptr));
        Step_sweep_octave_typecomboBox->setItemText(0, QCoreApplication::translate("StimulusSignalForm", "1/3", nullptr));
        Step_sweep_octave_typecomboBox->setItemText(1, QCoreApplication::translate("StimulusSignalForm", "1/6", nullptr));
        Step_sweep_octave_typecomboBox->setItemText(2, QCoreApplication::translate("StimulusSignalForm", "1/12", nullptr));
        Step_sweep_octave_typecomboBox->setItemText(3, QCoreApplication::translate("StimulusSignalForm", "1/24", nullptr));

        Step_sweepInputButton->setText(QCoreApplication::translate("StimulusSignalForm", "\345\257\274\345\205\245", nullptr));
        Step_sweepOutputButton->setText(QCoreApplication::translate("StimulusSignalForm", "\345\257\274\345\207\272", nullptr));
        label_20->setText(QCoreApplication::translate("StimulusSignalForm", "\345\273\266\347\273\255\346\227\266\351\225\277(S)", nullptr));
        conlogsweep_delay_durationlineEdit->setText(QCoreApplication::translate("StimulusSignalForm", "0.01", nullptr));
        label_18->setText(QCoreApplication::translate("StimulusSignalForm", "\345\211\215\347\275\256\346\227\266\351\225\277(S)", nullptr));
        conlogsweep_pre_durationlineEdit->setText(QCoreApplication::translate("StimulusSignalForm", "0.01", nullptr));
        label_16->setText(QCoreApplication::translate("StimulusSignalForm", "\347\273\223\346\235\237\351\242\221\347\216\207(Hz)", nullptr));
        conlogsweep_stop_freqlineEdit->setText(QCoreApplication::translate("StimulusSignalForm", "20", nullptr));
        label_17->setText(QCoreApplication::translate("StimulusSignalForm", "\346\200\273\346\227\266\351\225\277(S)", nullptr));
        conlogsweep_durationlineEdit->setText(QCoreApplication::translate("StimulusSignalForm", "1", nullptr));
        label_15->setText(QCoreApplication::translate("StimulusSignalForm", "\345\274\200\345\247\213\351\242\221\347\216\207(Hz)", nullptr));
        conlogsweep_start_freqlineEdit->setText(QCoreApplication::translate("StimulusSignalForm", "20", nullptr));
        level_stepInputButton->setText(QCoreApplication::translate("StimulusSignalForm", "\345\257\274\345\205\245", nullptr));
        level_stepOutPutButton->setText(QCoreApplication::translate("StimulusSignalForm", "\345\257\274\345\207\272", nullptr));
        label_22->setText(QCoreApplication::translate("StimulusSignalForm", "\346\257\217\346\255\245\346\227\266\351\225\277", nullptr));
        level_step_step_durationlineEdit->setText(QCoreApplication::translate("StimulusSignalForm", "0.01", nullptr));
        label_23->setText(QCoreApplication::translate("StimulusSignalForm", "\346\257\217\346\255\245\345\221\250\346\234\237\346\225\260", nullptr));
        level_step_step_circleslineEdit->setText(QCoreApplication::translate("StimulusSignalForm", "10", nullptr));
        label_30->setText(QCoreApplication::translate("StimulusSignalForm", "\346\255\245\350\277\233\346\254\241\346\225\260", nullptr));
        level_step_pointslineEdit->setText(QCoreApplication::translate("StimulusSignalForm", "10", nullptr));
        label_19->setText(QCoreApplication::translate("StimulusSignalForm", "\345\274\200\345\247\213\345\271\205\345\200\274", nullptr));
        level_step_start_levellineEdit->setText(QCoreApplication::translate("StimulusSignalForm", "20", nullptr));
        label_21->setText(QCoreApplication::translate("StimulusSignalForm", "\347\273\223\346\235\237\345\271\205\345\200\274", nullptr));
        level_step_stop_levellineEdit->setText(QCoreApplication::translate("StimulusSignalForm", "20", nullptr));
        label_14->setText(QCoreApplication::translate("StimulusSignalForm", "<html><head/><body><p>\346\255\245\350\277\233\347\261\273\345\236\213</p></body></html>", nullptr));
        level_step_amplitdeTypeSampcomboBox->setItemText(0, QCoreApplication::translate("StimulusSignalForm", "\347\272\277\346\200\247", nullptr));
        level_step_amplitdeTypeSampcomboBox->setItemText(1, QCoreApplication::translate("StimulusSignalForm", "\345\257\271\346\225\260", nullptr));

        label_25->setText(QCoreApplication::translate("StimulusSignalForm", "\351\242\221\347\216\207", nullptr));
        level_step_frequencylineEdit->setText(QCoreApplication::translate("StimulusSignalForm", "20", nullptr));
        label_13->setText(QCoreApplication::translate("StimulusSignalForm", "<html><head/><body><p><span style=\" font-size:12pt;\">\345\271\205\345\200\274\347\261\273\345\236\213</span></p></body></html>", nullptr));
        level_step_levelTypeSampcomboBox->setItemText(0, QCoreApplication::translate("StimulusSignalForm", "\347\272\277\346\200\247/\345\257\271\346\225\260", nullptr));
        level_step_levelTypeSampcomboBox->setItemText(1, QCoreApplication::translate("StimulusSignalForm", "\350\207\252\345\256\232\344\271\211", nullptr));

        label_37->setText(QCoreApplication::translate("StimulusSignalForm", "<html><head/><body><p><span style=\" font-size:12pt;\">\351\242\221\347\216\207\347\261\273\345\236\213</span></p></body></html>", nullptr));
        PS_stepped_freq_typecomboBox->setItemText(0, QCoreApplication::translate("StimulusSignalForm", "\347\272\277\346\200\247/\345\257\271\346\225\260", nullptr));
        PS_stepped_freq_typecomboBox->setItemText(1, QCoreApplication::translate("StimulusSignalForm", "\350\207\252\345\256\232\344\271\211", nullptr));

        label_32->setText(QCoreApplication::translate("StimulusSignalForm", "\346\255\245\350\277\233\346\254\241\346\225\260", nullptr));
        PS_stepped_pointslineEdit->setText(QCoreApplication::translate("StimulusSignalForm", "10", nullptr));
        label_33->setText(QCoreApplication::translate("StimulusSignalForm", "<html><head/><body><p><span style=\" font-size:12pt;\">\346\255\245\350\277\233\347\261\273\345\236\213</span></p></body></html>", nullptr));
        PS_stepped_step_typecomboBox->setItemText(0, QCoreApplication::translate("StimulusSignalForm", "\345\257\271\346\225\260", nullptr));
        PS_stepped_step_typecomboBox->setItemText(1, QCoreApplication::translate("StimulusSignalForm", "\347\272\277\346\200\247", nullptr));

        label_34->setText(QCoreApplication::translate("StimulusSignalForm", "\345\274\200\345\247\213\351\242\221\347\216\207(Hz)", nullptr));
        PS_stepped_start_freqlineEdit->setText(QCoreApplication::translate("StimulusSignalForm", "20", nullptr));
        label_31->setText(QCoreApplication::translate("StimulusSignalForm", "\347\273\223\346\235\237\351\242\221\347\216\207(Hz)", nullptr));
        PS_stepped_stop_freqlineEdit->setText(QCoreApplication::translate("StimulusSignalForm", "20", nullptr));
        PS_steppedInputButton->setText(QCoreApplication::translate("StimulusSignalForm", "\345\257\274\345\205\245", nullptr));
        PS_steppedOutputButton->setText(QCoreApplication::translate("StimulusSignalForm", "\345\257\274\345\207\272", nullptr));
    } // retranslateUi

};

namespace Ui {
    class StimulusSignalForm: public Ui_StimulusSignalForm {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_STIMULUSSIGNALFORM_H
