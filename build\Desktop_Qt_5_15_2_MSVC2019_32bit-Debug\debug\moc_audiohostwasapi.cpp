/****************************************************************************
** Meta object code from reading C++ file 'audiohostwasapi.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.15.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../AudioModu/audiohostwasapi.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'audiohostwasapi.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.15.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_AudioHostWasapi_t {
    QByteArrayData data[10];
    char stringdata0[121];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_AudioHostWasapi_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_AudioHostWasapi_t qt_meta_stringdata_AudioHostWasapi = {
    {
QT_MOC_LITERAL(0, 0, 15), // "AudioHostWasapi"
QT_MOC_LITERAL(1, 16, 17), // "requestOutputData"
QT_MOC_LITERAL(2, 34, 0), // ""
QT_MOC_LITERAL(3, 35, 7), // "nFrames"
QT_MOC_LITERAL(4, 43, 9), // "nChannels"
QT_MOC_LITERAL(5, 53, 18), // "inputDataAvailable"
QT_MOC_LITERAL(6, 72, 20), // "std::vector<int32_t>"
QT_MOC_LITERAL(7, 93, 4), // "data"
QT_MOC_LITERAL(8, 98, 14), // "timeoutWarning"
QT_MOC_LITERAL(9, 113, 7) // "message"

    },
    "AudioHostWasapi\0requestOutputData\0\0"
    "nFrames\0nChannels\0inputDataAvailable\0"
    "std::vector<int32_t>\0data\0timeoutWarning\0"
    "message"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_AudioHostWasapi[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       3,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       3,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    2,   29,    2, 0x06 /* Public */,
       5,    2,   34,    2, 0x06 /* Public */,
       8,    1,   39,    2, 0x06 /* Public */,

 // signals: parameters
    QMetaType::Void, QMetaType::UInt, QMetaType::UInt,    3,    4,
    QMetaType::Void, 0x80000000 | 6, QMetaType::UInt,    7,    4,
    QMetaType::Void, QMetaType::QString,    9,

       0        // eod
};

void AudioHostWasapi::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<AudioHostWasapi *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->requestOutputData((*reinterpret_cast< uint(*)>(_a[1])),(*reinterpret_cast< uint(*)>(_a[2]))); break;
        case 1: _t->inputDataAvailable((*reinterpret_cast< const std::vector<int32_t>(*)>(_a[1])),(*reinterpret_cast< uint(*)>(_a[2]))); break;
        case 2: _t->timeoutWarning((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (AudioHostWasapi::*)(unsigned int , unsigned int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&AudioHostWasapi::requestOutputData)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (AudioHostWasapi::*)(const std::vector<int32_t> & , unsigned int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&AudioHostWasapi::inputDataAvailable)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (AudioHostWasapi::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&AudioHostWasapi::timeoutWarning)) {
                *result = 2;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject AudioHostWasapi::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_AudioHostWasapi.data,
    qt_meta_data_AudioHostWasapi,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *AudioHostWasapi::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *AudioHostWasapi::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_AudioHostWasapi.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int AudioHostWasapi::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 3)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 3;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 3)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 3;
    }
    return _id;
}

// SIGNAL 0
void AudioHostWasapi::requestOutputData(unsigned int _t1, unsigned int _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void AudioHostWasapi::inputDataAvailable(const std::vector<int32_t> & _t1, unsigned int _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void AudioHostWasapi::timeoutWarning(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
