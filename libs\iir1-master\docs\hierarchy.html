<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>iir1: Class Hierarchy</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">iir1
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">Class Hierarchy</div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock">This inheritance list is sorted roughly, but not completely, alphabetically:</div><div class="directory">
<div class="levels">[detail level <span onclick="javascript:toggleLevel(1);">1</span><span onclick="javascript:toggleLevel(2);">2</span><span onclick="javascript:toggleLevel(3);">3</span><span onclick="javascript:toggleLevel(4);">4</span>]</div><table class="directory">
<tr id="row_0_" class="even"><td class="entry"><span style="width:0px;display:inline-block;">&#160;</span><span id="arr_0_" class="arrow" onclick="toggleFolder('0_')">&#9660;</span><span class="icona"><span class="icon">C</span></span><b>BandPassBase</b></td><td class="desc"></td></tr>
<tr id="row_0_0_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span id="arr_0_0_" class="arrow" onclick="toggleFolder('0_0_')">&#9660;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1PoleFilter.html" target="_self">Iir::PoleFilter&lt; BandPassBase, DirectFormII, 4, 4 *2 &gt;</a></td><td class="desc"></td></tr>
<tr id="row_0_0_0_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1Butterworth_1_1BandPass.html" target="_self">Iir::Butterworth::BandPass&lt; FilterOrder, StateType &gt;</a></td><td class="desc"></td></tr>
<tr id="row_0_0_1_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevI_1_1BandPass.html" target="_self">Iir::ChebyshevI::BandPass&lt; FilterOrder, StateType &gt;</a></td><td class="desc"></td></tr>
<tr id="row_0_0_2_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevII_1_1BandPass.html" target="_self">Iir::ChebyshevII::BandPass&lt; FilterOrder, StateType &gt;</a></td><td class="desc"></td></tr>
<tr id="row_1_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classIir_1_1BandPassTransform.html" target="_self">Iir::BandPassTransform</a></td><td class="desc"></td></tr>
<tr id="row_2_" class="even"><td class="entry"><span style="width:0px;display:inline-block;">&#160;</span><span id="arr_2_" class="arrow" onclick="toggleFolder('2_')">&#9660;</span><span class="icona"><span class="icon">C</span></span><b>BandShelfBase</b></td><td class="desc"></td></tr>
<tr id="row_2_0_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span id="arr_2_0_" class="arrow" onclick="toggleFolder('2_0_')">&#9660;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1PoleFilter.html" target="_self">Iir::PoleFilter&lt; BandShelfBase, DirectFormII, 4, 4 *2 &gt;</a></td><td class="desc"></td></tr>
<tr id="row_2_0_0_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1Butterworth_1_1BandShelf.html" target="_self">Iir::Butterworth::BandShelf&lt; FilterOrder, StateType &gt;</a></td><td class="desc"></td></tr>
<tr id="row_2_0_1_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevI_1_1BandShelf.html" target="_self">Iir::ChebyshevI::BandShelf&lt; FilterOrder, StateType &gt;</a></td><td class="desc"></td></tr>
<tr id="row_2_0_2_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevII_1_1BandShelf.html" target="_self">Iir::ChebyshevII::BandShelf&lt; FilterOrder, StateType &gt;</a></td><td class="desc"></td></tr>
<tr id="row_3_"><td class="entry"><span style="width:0px;display:inline-block;">&#160;</span><span id="arr_3_" class="arrow" onclick="toggleFolder('3_')">&#9660;</span><span class="icona"><span class="icon">C</span></span><b>BandStopBase</b></td><td class="desc"></td></tr>
<tr id="row_3_0_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span id="arr_3_0_" class="arrow" onclick="toggleFolder('3_0_')">&#9660;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1PoleFilter.html" target="_self">Iir::PoleFilter&lt; BandStopBase, DirectFormII, 4, 4 *2 &gt;</a></td><td class="desc"></td></tr>
<tr id="row_3_0_0_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1Butterworth_1_1BandStop.html" target="_self">Iir::Butterworth::BandStop&lt; FilterOrder, StateType &gt;</a></td><td class="desc"></td></tr>
<tr id="row_3_0_1_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevI_1_1BandStop.html" target="_self">Iir::ChebyshevI::BandStop&lt; FilterOrder, StateType &gt;</a></td><td class="desc"></td></tr>
<tr id="row_3_0_2_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevII_1_1BandStop.html" target="_self">Iir::ChebyshevII::BandStop&lt; FilterOrder, StateType &gt;</a></td><td class="desc"></td></tr>
<tr id="row_4_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classIir_1_1BandStopTransform.html" target="_self">Iir::BandStopTransform</a></td><td class="desc"></td></tr>
<tr id="row_5_"><td class="entry"><span style="width:0px;display:inline-block;">&#160;</span><span id="arr_5_" class="arrow" onclick="toggleFolder('5_')">&#9660;</span><span class="icona"><span class="icon">C</span></span><b>BaseClass</b></td><td class="desc"></td></tr>
<tr id="row_5_0_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1PoleFilter.html" target="_self">Iir::PoleFilter&lt; BaseClass, StateType, MaxAnalogPoles, MaxDigitalPoles &gt;</a></td><td class="desc"></td></tr>
<tr id="row_6_"><td class="entry"><span style="width:0px;display:inline-block;">&#160;</span><span id="arr_6_" class="arrow" onclick="toggleFolder('6_')">&#9660;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classIir_1_1Biquad.html" target="_self">Iir::Biquad</a></td><td class="desc"></td></tr>
<tr id="row_6_0_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1Custom_1_1OnePole.html" target="_self">Iir::Custom::OnePole</a></td><td class="desc"></td></tr>
<tr id="row_6_1_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1Custom_1_1TwoPole.html" target="_self">Iir::Custom::TwoPole</a></td><td class="desc"></td></tr>
<tr id="row_6_2_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span id="arr_6_2_" class="arrow" onclick="toggleFolder('6_2_')">&#9660;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1RBJ_1_1RBJbase.html" target="_self">Iir::RBJ::RBJbase</a></td><td class="desc"></td></tr>
<tr id="row_6_2_0_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1RBJ_1_1AllPass.html" target="_self">Iir::RBJ::AllPass</a></td><td class="desc"></td></tr>
<tr id="row_6_2_1_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1RBJ_1_1BandPass1.html" target="_self">Iir::RBJ::BandPass1</a></td><td class="desc"></td></tr>
<tr id="row_6_2_2_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1RBJ_1_1BandPass2.html" target="_self">Iir::RBJ::BandPass2</a></td><td class="desc"></td></tr>
<tr id="row_6_2_3_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1RBJ_1_1BandShelf.html" target="_self">Iir::RBJ::BandShelf</a></td><td class="desc"></td></tr>
<tr id="row_6_2_4_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1RBJ_1_1BandStop.html" target="_self">Iir::RBJ::BandStop</a></td><td class="desc"></td></tr>
<tr id="row_6_2_5_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1RBJ_1_1HighPass.html" target="_self">Iir::RBJ::HighPass</a></td><td class="desc"></td></tr>
<tr id="row_6_2_6_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1RBJ_1_1HighShelf.html" target="_self">Iir::RBJ::HighShelf</a></td><td class="desc"></td></tr>
<tr id="row_6_2_7_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1RBJ_1_1IIRNotch.html" target="_self">Iir::RBJ::IIRNotch</a></td><td class="desc"></td></tr>
<tr id="row_6_2_8_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1RBJ_1_1LowPass.html" target="_self">Iir::RBJ::LowPass</a></td><td class="desc"></td></tr>
<tr id="row_6_2_9_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1RBJ_1_1LowShelf.html" target="_self">Iir::RBJ::LowShelf</a></td><td class="desc"></td></tr>
<tr id="row_7_"><td class="entry"><span style="width:0px;display:inline-block;">&#160;</span><span id="arr_7_" class="arrow" onclick="toggleFolder('7_')">&#9660;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classIir_1_1Cascade.html" target="_self">Iir::Cascade</a></td><td class="desc"></td></tr>
<tr id="row_7_0_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span id="arr_7_0_" class="arrow" onclick="toggleFolder('7_0_')">&#9660;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classIir_1_1PoleFilterBase2.html" target="_self">Iir::PoleFilterBase2</a></td><td class="desc"></td></tr>
<tr id="row_7_0_0_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span id="arr_7_0_0_" class="arrow" onclick="toggleFolder('7_0_0_')">&#9658;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classIir_1_1PoleFilterBase.html" target="_self">Iir::PoleFilterBase&lt; AnalogLowPass &gt;</a></td><td class="desc"></td></tr>
<tr id="row_7_0_0_0_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1Butterworth_1_1BandPassBase.html" target="_self">Iir::Butterworth::BandPassBase</a></td><td class="desc"></td></tr>
<tr id="row_7_0_0_1_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1Butterworth_1_1BandStopBase.html" target="_self">Iir::Butterworth::BandStopBase</a></td><td class="desc"></td></tr>
<tr id="row_7_0_0_2_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1Butterworth_1_1HighPassBase.html" target="_self">Iir::Butterworth::HighPassBase</a></td><td class="desc"></td></tr>
<tr id="row_7_0_0_3_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1Butterworth_1_1LowPassBase.html" target="_self">Iir::Butterworth::LowPassBase</a></td><td class="desc"></td></tr>
<tr id="row_7_0_0_4_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevI_1_1BandPassBase.html" target="_self">Iir::ChebyshevI::BandPassBase</a></td><td class="desc"></td></tr>
<tr id="row_7_0_0_5_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevI_1_1BandStopBase.html" target="_self">Iir::ChebyshevI::BandStopBase</a></td><td class="desc"></td></tr>
<tr id="row_7_0_0_6_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevI_1_1HighPassBase.html" target="_self">Iir::ChebyshevI::HighPassBase</a></td><td class="desc"></td></tr>
<tr id="row_7_0_0_7_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevI_1_1LowPassBase.html" target="_self">Iir::ChebyshevI::LowPassBase</a></td><td class="desc"></td></tr>
<tr id="row_7_0_0_8_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevII_1_1BandPassBase.html" target="_self">Iir::ChebyshevII::BandPassBase</a></td><td class="desc"></td></tr>
<tr id="row_7_0_0_9_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevII_1_1BandStopBase.html" target="_self">Iir::ChebyshevII::BandStopBase</a></td><td class="desc"></td></tr>
<tr id="row_7_0_0_10_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevII_1_1HighPassBase.html" target="_self">Iir::ChebyshevII::HighPassBase</a></td><td class="desc"></td></tr>
<tr id="row_7_0_0_11_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevII_1_1LowPassBase.html" target="_self">Iir::ChebyshevII::LowPassBase</a></td><td class="desc"></td></tr>
<tr id="row_7_0_1_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span id="arr_7_0_1_" class="arrow" onclick="toggleFolder('7_0_1_')">&#9658;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classIir_1_1PoleFilterBase.html" target="_self">Iir::PoleFilterBase&lt; AnalogLowShelf &gt;</a></td><td class="desc"></td></tr>
<tr id="row_7_0_1_0_" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1Butterworth_1_1BandShelfBase.html" target="_self">Iir::Butterworth::BandShelfBase</a></td><td class="desc"></td></tr>
<tr id="row_7_0_1_1_" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1Butterworth_1_1HighShelfBase.html" target="_self">Iir::Butterworth::HighShelfBase</a></td><td class="desc"></td></tr>
<tr id="row_7_0_1_2_" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1Butterworth_1_1LowShelfBase.html" target="_self">Iir::Butterworth::LowShelfBase</a></td><td class="desc"></td></tr>
<tr id="row_7_0_1_3_" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevI_1_1BandShelfBase.html" target="_self">Iir::ChebyshevI::BandShelfBase</a></td><td class="desc"></td></tr>
<tr id="row_7_0_1_4_" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevI_1_1HighShelfBase.html" target="_self">Iir::ChebyshevI::HighShelfBase</a></td><td class="desc"></td></tr>
<tr id="row_7_0_1_5_" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevI_1_1LowShelfBase.html" target="_self">Iir::ChebyshevI::LowShelfBase</a></td><td class="desc"></td></tr>
<tr id="row_7_0_1_6_" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevII_1_1BandShelfBase.html" target="_self">Iir::ChebyshevII::BandShelfBase</a></td><td class="desc"></td></tr>
<tr id="row_7_0_1_7_" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevII_1_1HighShelfBase.html" target="_self">Iir::ChebyshevII::HighShelfBase</a></td><td class="desc"></td></tr>
<tr id="row_7_0_1_8_" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevII_1_1LowShelfBase.html" target="_self">Iir::ChebyshevII::LowShelfBase</a></td><td class="desc"></td></tr>
<tr id="row_7_0_2_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classIir_1_1PoleFilterBase.html" target="_self">Iir::PoleFilterBase&lt; AnalogPrototype &gt;</a></td><td class="desc"></td></tr>
<tr id="row_8_" class="even"><td class="entry"><span style="width:0px;display:inline-block;">&#160;</span><span id="arr_8_" class="arrow" onclick="toggleFolder('8_')">&#9660;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classIir_1_1CascadeStages.html" target="_self">Iir::CascadeStages&lt; MaxStages, StateType &gt;</a></td><td class="desc"></td></tr>
<tr id="row_8_0_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1PoleFilter.html" target="_self">Iir::PoleFilter&lt; BandPassBase, DirectFormII, 4, 4 *2 &gt;</a></td><td class="desc"></td></tr>
<tr id="row_8_1_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1PoleFilter.html" target="_self">Iir::PoleFilter&lt; BandShelfBase, DirectFormII, 4, 4 *2 &gt;</a></td><td class="desc"></td></tr>
<tr id="row_8_2_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span id="arr_8_2_" class="arrow" onclick="toggleFolder('8_2_')">&#9660;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1PoleFilter.html" target="_self">Iir::PoleFilter&lt; LowPassBase, DirectFormII, 4 &gt;</a></td><td class="desc"></td></tr>
<tr id="row_8_2_0_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1Butterworth_1_1LowPass.html" target="_self">Iir::Butterworth::LowPass&lt; FilterOrder, StateType &gt;</a></td><td class="desc"></td></tr>
<tr id="row_8_2_1_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevI_1_1LowPass.html" target="_self">Iir::ChebyshevI::LowPass&lt; FilterOrder, StateType &gt;</a></td><td class="desc"></td></tr>
<tr id="row_8_2_2_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevII_1_1LowPass.html" target="_self">Iir::ChebyshevII::LowPass&lt; FilterOrder, StateType &gt;</a></td><td class="desc"></td></tr>
<tr id="row_8_3_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span id="arr_8_3_" class="arrow" onclick="toggleFolder('8_3_')">&#9660;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1PoleFilter.html" target="_self">Iir::PoleFilter&lt; LowShelfBase, DirectFormII, 4 &gt;</a></td><td class="desc"></td></tr>
<tr id="row_8_3_0_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1Butterworth_1_1LowShelf.html" target="_self">Iir::Butterworth::LowShelf&lt; FilterOrder, StateType &gt;</a></td><td class="desc"></td></tr>
<tr id="row_8_3_1_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevI_1_1LowShelf.html" target="_self">Iir::ChebyshevI::LowShelf&lt; FilterOrder, StateType &gt;</a></td><td class="desc"></td></tr>
<tr id="row_8_3_2_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevII_1_1LowShelf.html" target="_self">Iir::ChebyshevII::LowShelf&lt; FilterOrder, StateType &gt;</a></td><td class="desc"></td></tr>
<tr id="row_8_4_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1PoleFilter.html" target="_self">Iir::PoleFilter&lt; BandStopBase, DirectFormII, 4, 4 *2 &gt;</a></td><td class="desc"></td></tr>
<tr id="row_8_5_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span id="arr_8_5_" class="arrow" onclick="toggleFolder('8_5_')">&#9660;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1PoleFilter.html" target="_self">Iir::PoleFilter&lt; HighShelfBase, DirectFormII, 4 &gt;</a></td><td class="desc"></td></tr>
<tr id="row_8_5_0_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1Butterworth_1_1HighShelf.html" target="_self">Iir::Butterworth::HighShelf&lt; FilterOrder, StateType &gt;</a></td><td class="desc"></td></tr>
<tr id="row_8_5_1_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevI_1_1HighShelf.html" target="_self">Iir::ChebyshevI::HighShelf&lt; FilterOrder, StateType &gt;</a></td><td class="desc"></td></tr>
<tr id="row_8_5_2_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevII_1_1HighShelf.html" target="_self">Iir::ChebyshevII::HighShelf&lt; FilterOrder, StateType &gt;</a></td><td class="desc"></td></tr>
<tr id="row_8_6_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span id="arr_8_6_" class="arrow" onclick="toggleFolder('8_6_')">&#9660;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1PoleFilter.html" target="_self">Iir::PoleFilter&lt; HighPassBase, DirectFormII, 4 &gt;</a></td><td class="desc"></td></tr>
<tr id="row_8_6_0_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1Butterworth_1_1HighPass.html" target="_self">Iir::Butterworth::HighPass&lt; FilterOrder, StateType &gt;</a></td><td class="desc"></td></tr>
<tr id="row_8_6_1_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevI_1_1HighPass.html" target="_self">Iir::ChebyshevI::HighPass&lt; FilterOrder, StateType &gt;</a></td><td class="desc"></td></tr>
<tr id="row_8_6_2_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevII_1_1HighPass.html" target="_self">Iir::ChebyshevII::HighPass&lt; FilterOrder, StateType &gt;</a></td><td class="desc"></td></tr>
<tr id="row_9_" class="even"><td class="entry"><span style="width:0px;display:inline-block;">&#160;</span><span id="arr_9_" class="arrow" onclick="toggleFolder('9_')">&#9660;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classIir_1_1CascadeStages.html" target="_self">Iir::CascadeStages&lt; NSOS, DirectFormII &gt;</a></td><td class="desc"></td></tr>
<tr id="row_9_0_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1Custom_1_1SOSCascade.html" target="_self">Iir::Custom::SOSCascade&lt; NSOS, StateType &gt;</a></td><td class="desc"></td></tr>
<tr id="row_10_" class="even"><td class="entry"><span style="width:0px;display:inline-block;">&#160;</span><span id="arr_10_" class="arrow" onclick="toggleFolder('10_')">&#9660;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classIir_1_1CascadeStages.html" target="_self">Iir::CascadeStages&lt;(MaxAnalogPoles+1)/2, StateType &gt;</a></td><td class="desc"></td></tr>
<tr id="row_10_0_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1PoleFilter.html" target="_self">Iir::PoleFilter&lt; BaseClass, StateType, MaxAnalogPoles, MaxDigitalPoles &gt;</a></td><td class="desc"></td></tr>
<tr id="row_11_" class="even"><td class="entry"><span style="width:0px;display:inline-block;">&#160;</span><span id="arr_11_" class="arrow" onclick="toggleFolder('11_')">&#9660;</span><span class="icona"><span class="icon">C</span></span><b>complex_pair_t</b></td><td class="desc"></td></tr>
<tr id="row_11_0_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ComplexPair.html" target="_self">Iir::ComplexPair</a></td><td class="desc"></td></tr>
<tr id="row_12_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classIir_1_1DirectFormI.html" target="_self">Iir::DirectFormI</a></td><td class="desc"></td></tr>
<tr id="row_13_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classIir_1_1DirectFormII.html" target="_self">Iir::DirectFormII</a></td><td class="desc"></td></tr>
<tr id="row_14_" class="even"><td class="entry"><span style="width:0px;display:inline-block;">&#160;</span><span id="arr_14_" class="arrow" onclick="toggleFolder('14_')">&#9660;</span><span class="icona"><span class="icon">C</span></span><b>HighPassBase</b></td><td class="desc"></td></tr>
<tr id="row_14_0_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1PoleFilter.html" target="_self">Iir::PoleFilter&lt; HighPassBase, DirectFormII, 4 &gt;</a></td><td class="desc"></td></tr>
<tr id="row_15_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classIir_1_1HighPassTransform.html" target="_self">Iir::HighPassTransform</a></td><td class="desc"></td></tr>
<tr id="row_16_"><td class="entry"><span style="width:0px;display:inline-block;">&#160;</span><span id="arr_16_" class="arrow" onclick="toggleFolder('16_')">&#9660;</span><span class="icona"><span class="icon">C</span></span><b>HighShelfBase</b></td><td class="desc"></td></tr>
<tr id="row_16_0_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1PoleFilter.html" target="_self">Iir::PoleFilter&lt; HighShelfBase, DirectFormII, 4 &gt;</a></td><td class="desc"></td></tr>
<tr id="row_17_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classIir_1_1Layout.html" target="_self">Iir::Layout&lt; MaxPoles &gt;</a></td><td class="desc"></td></tr>
<tr id="row_18_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classIir_1_1Layout.html" target="_self">Iir::Layout&lt; MaxAnalogPoles &gt;</a></td><td class="desc"></td></tr>
<tr id="row_19_"><td class="entry"><span style="width:0px;display:inline-block;">&#160;</span><span id="arr_19_" class="arrow" onclick="toggleFolder('19_')">&#9660;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classIir_1_1LayoutBase.html" target="_self">Iir::LayoutBase</a></td><td class="desc"></td></tr>
<tr id="row_19_0_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classIir_1_1Butterworth_1_1AnalogLowPass.html" target="_self">Iir::Butterworth::AnalogLowPass</a></td><td class="desc"></td></tr>
<tr id="row_19_1_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classIir_1_1Butterworth_1_1AnalogLowShelf.html" target="_self">Iir::Butterworth::AnalogLowShelf</a></td><td class="desc"></td></tr>
<tr id="row_19_2_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classIir_1_1ChebyshevI_1_1AnalogLowPass.html" target="_self">Iir::ChebyshevI::AnalogLowPass</a></td><td class="desc"></td></tr>
<tr id="row_19_3_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classIir_1_1ChebyshevI_1_1AnalogLowShelf.html" target="_self">Iir::ChebyshevI::AnalogLowShelf</a></td><td class="desc"></td></tr>
<tr id="row_19_4_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classIir_1_1ChebyshevII_1_1AnalogLowPass.html" target="_self">Iir::ChebyshevII::AnalogLowPass</a></td><td class="desc"></td></tr>
<tr id="row_19_5_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classIir_1_1ChebyshevII_1_1AnalogLowShelf.html" target="_self">Iir::ChebyshevII::AnalogLowShelf</a></td><td class="desc"></td></tr>
<tr id="row_20_" class="even"><td class="entry"><span style="width:0px;display:inline-block;">&#160;</span><span id="arr_20_" class="arrow" onclick="toggleFolder('20_')">&#9660;</span><span class="icona"><span class="icon">C</span></span><b>LowPassBase</b></td><td class="desc"></td></tr>
<tr id="row_20_0_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1PoleFilter.html" target="_self">Iir::PoleFilter&lt; LowPassBase, DirectFormII, 4 &gt;</a></td><td class="desc"></td></tr>
<tr id="row_21_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classIir_1_1LowPassTransform.html" target="_self">Iir::LowPassTransform</a></td><td class="desc"></td></tr>
<tr id="row_22_"><td class="entry"><span style="width:0px;display:inline-block;">&#160;</span><span id="arr_22_" class="arrow" onclick="toggleFolder('22_')">&#9660;</span><span class="icona"><span class="icon">C</span></span><b>LowShelfBase</b></td><td class="desc"></td></tr>
<tr id="row_22_0_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1PoleFilter.html" target="_self">Iir::PoleFilter&lt; LowShelfBase, DirectFormII, 4 &gt;</a></td><td class="desc"></td></tr>
<tr id="row_23_"><td class="entry"><span style="width:0px;display:inline-block;">&#160;</span><span id="arr_23_" class="arrow" onclick="toggleFolder('23_')">&#9660;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1PoleZeroPair.html" target="_self">Iir::PoleZeroPair</a></td><td class="desc"></td></tr>
<tr id="row_23_0_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1BiquadPoleState.html" target="_self">Iir::BiquadPoleState</a></td><td class="desc"></td></tr>
<tr id="row_24_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1Cascade_1_1Storage.html" target="_self">Iir::Cascade::Storage</a></td><td class="desc"></td></tr>
<tr id="row_25_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classIir_1_1TransposedDirectFormII.html" target="_self">Iir::TransposedDirectFormII</a></td><td class="desc"></td></tr>
</table>
</div><!-- directory -->
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Fri Jan 6 2023 23:53:47 for iir1 by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
