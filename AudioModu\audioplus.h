﻿#ifndef AUDIOPLUS_H  // 如果未定义 AUDIOPLUS_H，则定义以防止重复包含
#define AUDIOPLUS_H  // 定义 AUDIOPLUS_H 宏

// 核心功能（Core）：提供 Qt 对象模型基础
#include <QObject>

// 数据结构（Data Structures）：支持各种容器和智能指针
#include <QMap>
#include <QSet>
#include <QList>
#include <QSharedPointer>
#include <QStringList>
#include <vector>
#include <memory>

// 线程与时间（Threading and Timing）：处理同步、定时和时间测量
#include <QMutex>
#include <QTimer>
#include <QElapsedTimer>
#include <QDateTime>

// 文件与 I/O（File and I/O）：处理文件操作
#include <QFile>

// 数学与算法（Math and Algorithms）：提供数学计算和算法支持
#include <cmath>
#include <random>
#include <cstring>
#include <algorithm>

// 调试与工具（Debugging and Utilities）：调试输出和函数对象支持
#include <QDebug>
#include <functional>

// 音频和并发（Audio and Concurrency）：实时音频处理、无锁队列和通道配置管理
#include "RtAudio.h"  // 包含 RtAudio 头文件，用于实时音频处理
#include "concurrentqueue.h"  // 包含 concurrentqueue 头文件，用于无锁队列
#include "audiochannelconfigmanager.h"  // 包含 AudioChannelConfigManager 头文件，用于通道配置管理

class AudioPlus : public QObject {  // 定义 AudioPlus 类，继承自 QObject
    Q_OBJECT  // 启用 Qt 的元对象编译器，用于信号和槽

public:  // 公共成员
    enum ChannelType { isInput, isOutput };  // 定义通道类型枚举：输入或输出
    enum SignalType { Sine, Sweep, Noise, Wav };  // 定义信号类型枚举：正弦波、扫频、噪声、WAV

    struct ChannelInfo {  // 定义通道信息结构体
        QString deviceName;  // 通道名称（例如 InChannel_0）
        unsigned int deviceId;  // 设备 ID
        RtAudio::Api api;  // 音频 API（ASIO 或 WASAPI）
        unsigned int channelIndex;  // 通道索引（从 0 开始）
        float gain;  // 通道增益
        bool active;  // 通道是否活跃
        unsigned int sampleRate;  // 采样率
        QSharedPointer<moodycamel::ConcurrentQueue<float>> inputQueue;  // 输入无锁队列
        QSharedPointer<moodycamel::ConcurrentQueue<float>> outputQueue;  // 输出无锁队列
        SignalType signalType;  // 信号类型
        std::vector<float> sineWaveData;  // 正弦波数据
        unsigned int sampleOffset;  // 样本偏移（用于正弦波）
        std::vector<float> tempData;  // 临时信号数据（用于扫频/WAV/噪声）
        unsigned int tempDataIndex;  // 临时数据索引
        unsigned int validSamples;  // 有效样本数
        unsigned int totalSamples;  // 总样本数
    };

    struct DeviceStream {  // 定义设备流结构体
        RtAudio* dac = nullptr;  // 音频设备指针，初始化为空
        bool isRunning;  // 流是否正在运行
        unsigned int sampleRate;  // 采样率
        unsigned int streamLatency;  // 流延迟（样本数）
        double lastStreamTime;  // 上次流时间，用于健康监控
        unsigned int errorCount;  // 错误计数，用于超时检测
        double lastErrorTime;  // 上次错误时间，限制警告频率
        QList<QSharedPointer<ChannelInfo>> inputChannels;  // 输入通道列表
        QList<QSharedPointer<ChannelInfo>> outputChannels;  // 输出通道列表
        int activeChannelCount = 0;  // 活跃通道计数，初始化为 0

        DeviceStream() : dac(nullptr), isRunning(false), sampleRate(0), streamLatency(0),  // 构造函数
                         lastStreamTime(-1.0), errorCount(0), lastErrorTime(0.0), activeChannelCount(0) {}  // 初始化默认值
    };

    class AsioHost {  // 定义 ASIO 声卡主机类
    public:
        explicit AsioHost(const QString &key, AudioPlus *parent);  // 构造函数，接收设备键和 AudioPlus 指针
        bool addChannel(const QString &channelName, ChannelType type, const AudioChannalInfoConfig &config,  // 添加通道方法
                        unsigned int sampleRate, QString &errorMessage);
        bool startStream(unsigned int sampleRate, QString &errorMessage);  // 启动流方法
        void stopStream();  // 停止流方法
        bool isRunning() const { return stream.isRunning; }  // 检查流是否运行
        DeviceStream& getStream() { return stream; }  // 获取设备流
        QString getKey() const { return key; }  // 获取设备键

    private:
        QString key;  // 设备键
        AudioPlus *parent;  // AudioPlus 指针，用于信号传递
        QSharedPointer<RtAudio> rtAudio;  // RtAudio 实例，智能指针管理
        DeviceStream stream;  // 设备流
        void errorCallback(RtAudioErrorType type, const std::string &errorText);  // 错误回调函数，处理 RtAudio 错误
        static int callback(void *outputBuffer, void *inputBuffer, unsigned int nFrames,  // 静态回调函数，处理音频数据
                            double streamTime, RtAudioStreamStatus status, void *userData);
    };

    class WasapiHost {  // 定义 WASAPI 声卡主机类
    public:
        explicit WasapiHost(const QString &key, AudioPlus *parent);  // 构造函数
        bool addChannel(const QString &channelName, ChannelType type, const AudioChannalInfoConfig &config,  // 添加通道方法
                        unsigned int sampleRate, QString &errorMessage);
        bool startStream(unsigned int sampleRate, QString &errorMessage);  // 启动流方法
        void stopStream();  // 停止流方法
        bool isRunning() const { return stream.isRunning; }  // 检查流是否运行
        DeviceStream& getStream() { return stream; }  // 获取设备流
        QString getKey() const { return key; }  // 获取设备键

    private:
        QString key;  // 设备键
        AudioPlus *parent;  // AudioPlus 指针
        QSharedPointer<RtAudio> rtAudio;  // RtAudio 实例
        DeviceStream stream;  // 设备流
        void errorCallback(RtAudioErrorType type, const std::string &errorText);  // 错误回调函数
        static int callback(void *outputBuffer, void *inputBuffer, unsigned int nFrames,  // 静态回调函数
                            double streamTime, RtAudioStreamStatus status, void *userData);
    };

    explicit AudioPlus(QObject *parent = nullptr);  // 构造函数，接收可选父对象
    ~AudioPlus();  // 析构函数

    bool addChannels(const QStringList &inputChannels, const QStringList &outputChannels,  // 添加通道方法
                     unsigned int sampleRate, QString &errorMessage);
    bool startStream(const QStringList &inputChannels, const QStringList &outputChannels,  // 启动流方法
                     unsigned int sampleRate, QString &errorMessage);
    void stopStream(const QStringList &inputChannels, const QStringList &outputChannels);  // 停止流方法
    bool configureSignal(const QString &channelName, SignalType type, const QVariantMap &params);  // 配置信号方法
    bool getDisplayData(ChannelType type, const QString &channelName, std::vector<double> &data);  // 获取显示数据方法
    bool isDeviceRunning(const QString &deviceName) const;  // 检查设备是否运行方法
    QMap<QString, AudioChannalInfoConfig> getInputChannels() const;  // 获取输入通道列表方法
    QMap<QString, AudioChannalInfoConfig> getOutputChannels() const;  // 获取输出通道列表方法

signals:  // 信号定义
    void errorOccurred(const QString &msg);  // 错误发生信号，传递错误消息
    void dataUpdated(ChannelType type, const QString &channelName);  // 数据更新信号，通知显示数据更新
    void playbackFinished(ChannelType type, const QString &channelName);  // 播放完成信号，通知通道播放结束

private:  // 私有成员
    QMap<QString, QSharedPointer<AsioHost>> asioHosts;  // ASIO 声卡主机映射
    QMap<QString, QSharedPointer<WasapiHost>> wasapiHosts;  // WASAPI 声卡主机映射
    QMap<ChannelType, QMap<QString, std::vector<float>>> displayBuffer1_, displayBuffer2_;  // 显示缓冲区，双缓冲机制
    QMutex mutex_, displayMutex;  // 互斥锁，保护 hosts 和显示缓冲区
    QElapsedTimer timer;  // 计时器，监控回调性能
    QTimer *displayTimer;  // 显示定时器，定期更新显示数据
    unsigned int bufferFrames_ = 512;  // 音频缓冲区帧数，默认 512
    bool useBuffer1_ = true;  // 显示缓冲区选择标志，true 表示使用 buffer1
    unsigned int displayPoints_ = static_cast<unsigned int>(44100 * 0.2);  // 显示点数，0.2 秒数据（8820 点）
    AudioChannelConfigManager configManager_;  // 通道配置管理器实例

    bool validateChannels(const QMap<AudioChannelConfigManager::ChannelType, QMap<QString, AudioChannalInfoConfig>> &inputs,  // 验证通道方法
                          const QMap<AudioChannelConfigManager::ChannelType, QMap<QString, AudioChannalInfoConfig>> &outputs,
                          unsigned int sampleRate, QString &errorMessage);
    void updateAllDisplayBuffers();  // 更新所有显示缓冲区方法
    static QString apiToString(RtAudio::Api api);  // 将 API 转换为字符串方法
};

#endif // AUDIOPLUS_H  // 结束 AUDIOPLUS_H 宏定义
