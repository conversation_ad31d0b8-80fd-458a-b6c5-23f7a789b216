\hypertarget{structIir_1_1RBJ_1_1HighPass}{}\doxysubsection{Iir\+::RBJ\+::High\+Pass Struct Reference}
\label{structIir_1_1RBJ_1_1HighPass}\index{Iir::RBJ::HighPass@{Iir::RBJ::HighPass}}


{\ttfamily \#include $<$RBJ.\+h$>$}

Inheritance diagram for Iir\+::RBJ\+::High\+Pass\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=3.000000cm]{structIir_1_1RBJ_1_1HighPass}
\end{center}
\end{figure}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
void \mbox{\hyperlink{structIir_1_1RBJ_1_1HighPass_aa990e5f563666cd2d3cdc48cd77d5f2b}{setupN}} (double cutoff\+Frequency, double q=(1/sqrt(2)))
\item 
void \mbox{\hyperlink{structIir_1_1RBJ_1_1HighPass_a445c9bba06d82c972e389fbae45962e9}{setup}} (double sample\+Rate, double cutoff\+Frequency, double q=(1/sqrt(2)))
\end{DoxyCompactItemize}


\doxysubsubsection{Detailed Description}
Highpass. 

\doxysubsubsection{Member Function Documentation}
\mbox{\Hypertarget{structIir_1_1RBJ_1_1HighPass_a445c9bba06d82c972e389fbae45962e9}\label{structIir_1_1RBJ_1_1HighPass_a445c9bba06d82c972e389fbae45962e9}} 
\index{Iir::RBJ::HighPass@{Iir::RBJ::HighPass}!setup@{setup}}
\index{setup@{setup}!Iir::RBJ::HighPass@{Iir::RBJ::HighPass}}
\doxyparagraph{\texorpdfstring{setup()}{setup()}}
{\footnotesize\ttfamily void Iir\+::\+RBJ\+::\+High\+Pass\+::setup (\begin{DoxyParamCaption}\item[{double}]{sample\+Rate,  }\item[{double}]{cutoff\+Frequency,  }\item[{double}]{q = {\ttfamily (1/sqrt(2))} }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients 
\begin{DoxyParams}{Parameters}
{\em sample\+Rate} & Sampling rate \\
\hline
{\em cutoff\+Frequency} & Cutoff frequency \\
\hline
{\em q} & Q factor determines the resonance peak at the cutoff. \\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{structIir_1_1RBJ_1_1HighPass_aa990e5f563666cd2d3cdc48cd77d5f2b}\label{structIir_1_1RBJ_1_1HighPass_aa990e5f563666cd2d3cdc48cd77d5f2b}} 
\index{Iir::RBJ::HighPass@{Iir::RBJ::HighPass}!setupN@{setupN}}
\index{setupN@{setupN}!Iir::RBJ::HighPass@{Iir::RBJ::HighPass}}
\doxyparagraph{\texorpdfstring{setupN()}{setupN()}}
{\footnotesize\ttfamily void Iir\+::\+RBJ\+::\+High\+Pass\+::setupN (\begin{DoxyParamCaption}\item[{double}]{cutoff\+Frequency,  }\item[{double}]{q = {\ttfamily (1/sqrt(2))} }\end{DoxyParamCaption})}

Calculates the coefficients 
\begin{DoxyParams}{Parameters}
{\em cutoff\+Frequency} & Normalised cutoff frequency (0..1/2) \\
\hline
{\em q} & Q factor determines the resonance peak at the cutoff. \\
\hline
\end{DoxyParams}


The documentation for this struct was generated from the following files\+:\begin{DoxyCompactItemize}
\item 
iir/RBJ.\+h\item 
iir/RBJ.\+cpp\end{DoxyCompactItemize}
