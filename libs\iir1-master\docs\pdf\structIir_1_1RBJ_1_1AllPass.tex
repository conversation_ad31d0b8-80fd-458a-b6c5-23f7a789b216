\hypertarget{structIir_1_1RBJ_1_1AllPass}{}\doxysubsection{Iir\+::RBJ\+::All\+Pass Struct Reference}
\label{structIir_1_1RBJ_1_1AllPass}\index{Iir::RBJ::AllPass@{Iir::RBJ::AllPass}}


{\ttfamily \#include $<$RBJ.\+h$>$}

Inheritance diagram for Iir\+::RBJ\+::All\+Pass\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=3.000000cm]{structIir_1_1RBJ_1_1AllPass}
\end{center}
\end{figure}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
void \mbox{\hyperlink{structIir_1_1RBJ_1_1AllPass_ab0ca96fe3066c0bc2206a47d51c6bf5a}{setupN}} (double phase\+Frequency, double q=(1/sqrt(2)))
\item 
void \mbox{\hyperlink{structIir_1_1RBJ_1_1AllPass_ae68e5b4a22b3256b52798cddc3ff653c}{setup}} (double sample\+Rate, double phase\+Frequency, double q=(1/sqrt(2)))
\end{DoxyCompactItemize}


\doxysubsubsection{Detailed Description}
Allpass filter 

\doxysubsubsection{Member Function Documentation}
\mbox{\Hypertarget{structIir_1_1RBJ_1_1AllPass_ae68e5b4a22b3256b52798cddc3ff653c}\label{structIir_1_1RBJ_1_1AllPass_ae68e5b4a22b3256b52798cddc3ff653c}} 
\index{Iir::RBJ::AllPass@{Iir::RBJ::AllPass}!setup@{setup}}
\index{setup@{setup}!Iir::RBJ::AllPass@{Iir::RBJ::AllPass}}
\doxyparagraph{\texorpdfstring{setup()}{setup()}}
{\footnotesize\ttfamily void Iir\+::\+RBJ\+::\+All\+Pass\+::setup (\begin{DoxyParamCaption}\item[{double}]{sample\+Rate,  }\item[{double}]{phase\+Frequency,  }\item[{double}]{q = {\ttfamily (1/sqrt(2))} }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients 
\begin{DoxyParams}{Parameters}
{\em sample\+Rate} & Sampling rate \\
\hline
{\em phase\+Frequency} & Frequency where the phase flips \\
\hline
{\em q} & Q-\/factor \\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{structIir_1_1RBJ_1_1AllPass_ab0ca96fe3066c0bc2206a47d51c6bf5a}\label{structIir_1_1RBJ_1_1AllPass_ab0ca96fe3066c0bc2206a47d51c6bf5a}} 
\index{Iir::RBJ::AllPass@{Iir::RBJ::AllPass}!setupN@{setupN}}
\index{setupN@{setupN}!Iir::RBJ::AllPass@{Iir::RBJ::AllPass}}
\doxyparagraph{\texorpdfstring{setupN()}{setupN()}}
{\footnotesize\ttfamily void Iir\+::\+RBJ\+::\+All\+Pass\+::setupN (\begin{DoxyParamCaption}\item[{double}]{phase\+Frequency,  }\item[{double}]{q = {\ttfamily (1/sqrt(2))} }\end{DoxyParamCaption})}

Calculates the coefficients 
\begin{DoxyParams}{Parameters}
{\em phase\+Frequency} & Normalised frequency where the phase flips \\
\hline
{\em q} & Q-\/factor \\
\hline
\end{DoxyParams}


The documentation for this struct was generated from the following files\+:\begin{DoxyCompactItemize}
\item 
iir/RBJ.\+h\item 
iir/RBJ.\+cpp\end{DoxyCompactItemize}
