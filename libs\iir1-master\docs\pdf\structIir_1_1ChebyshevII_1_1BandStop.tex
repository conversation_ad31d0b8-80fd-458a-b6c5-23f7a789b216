\hypertarget{structIir_1_1ChebyshevII_1_1BandStop}{}\doxysubsection{Iir\+::Chebyshev\+II\+::Band\+Stop$<$ Filter\+Order, State\+Type $>$ Struct Template Reference}
\label{structIir_1_1ChebyshevII_1_1BandStop}\index{Iir::ChebyshevII::BandStop$<$ FilterOrder, StateType $>$@{Iir::ChebyshevII::BandStop$<$ FilterOrder, StateType $>$}}


{\ttfamily \#include $<$Chebyshev\+II.\+h$>$}

Inheritance diagram for Iir\+::Chebyshev\+II\+::Band\+Stop$<$ Filter\+Order, State\+Type $>$\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=2.434783cm]{structIir_1_1ChebyshevII_1_1BandStop}
\end{center}
\end{figure}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
void \mbox{\hyperlink{structIir_1_1ChebyshevII_1_1BandStop_a2cced9fbf0ac69ac0f20192bc6d8375c}{setup}} (double sample\+Rate, double center\+Frequency, double width\+Frequency, double stop\+Band\+Db)
\item 
void \mbox{\hyperlink{structIir_1_1ChebyshevII_1_1BandStop_abb424ebd2398df91406042580b25a173}{setup}} (int req\+Order, double sample\+Rate, double center\+Frequency, double width\+Frequency, double stop\+Band\+Db)
\item 
void \mbox{\hyperlink{structIir_1_1ChebyshevII_1_1BandStop_ab91192c2d3bf48f69849aba72c0c1c31}{setupN}} (double center\+Frequency, double width\+Frequency, double stop\+Band\+Db)
\item 
void \mbox{\hyperlink{structIir_1_1ChebyshevII_1_1BandStop_a4abf9f03fa285f15001d8d99f0f8fab4}{setupN}} (int req\+Order, double center\+Frequency, double width\+Frequency, double stop\+Band\+Db)
\end{DoxyCompactItemize}


\doxysubsubsection{Detailed Description}
\subsubsection*{template$<$int Filter\+Order = 4, class State\+Type = Direct\+Form\+II$>$\newline
struct Iir\+::\+Chebyshev\+II\+::\+Band\+Stop$<$ Filter\+Order, State\+Type $>$}

\mbox{\hyperlink{namespaceIir_1_1ChebyshevII}{Chebyshev\+II}} bandstop filter. 
\begin{DoxyParams}{Parameters}
{\em Filter\+Order} & Reserves memory for a filter of the order Filter\+Order \\
\hline
{\em State\+Type} & The filter topology\+: \mbox{\hyperlink{classIir_1_1DirectFormI}{Direct\+FormI}}, \mbox{\hyperlink{classIir_1_1DirectFormII}{Direct\+Form\+II}}, ... \\
\hline
\end{DoxyParams}


\doxysubsubsection{Member Function Documentation}
\mbox{\Hypertarget{structIir_1_1ChebyshevII_1_1BandStop_a2cced9fbf0ac69ac0f20192bc6d8375c}\label{structIir_1_1ChebyshevII_1_1BandStop_a2cced9fbf0ac69ac0f20192bc6d8375c}} 
\index{Iir::ChebyshevII::BandStop$<$ FilterOrder, StateType $>$@{Iir::ChebyshevII::BandStop$<$ FilterOrder, StateType $>$}!setup@{setup}}
\index{setup@{setup}!Iir::ChebyshevII::BandStop$<$ FilterOrder, StateType $>$@{Iir::ChebyshevII::BandStop$<$ FilterOrder, StateType $>$}}
\doxyparagraph{\texorpdfstring{setup()}{setup()}\hspace{0.1cm}{\footnotesize\ttfamily [1/2]}}
{\footnotesize\ttfamily template$<$int Filter\+Order = 4, class State\+Type  = Direct\+Form\+II$>$ \\
void \mbox{\hyperlink{structIir_1_1ChebyshevII_1_1BandStop}{Iir\+::\+Chebyshev\+II\+::\+Band\+Stop}}$<$ Filter\+Order, State\+Type $>$\+::setup (\begin{DoxyParamCaption}\item[{double}]{sample\+Rate,  }\item[{double}]{center\+Frequency,  }\item[{double}]{width\+Frequency,  }\item[{double}]{stop\+Band\+Db }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients of the filter 
\begin{DoxyParams}{Parameters}
{\em sample\+Rate} & Sampling rate \\
\hline
{\em center\+Frequency} & Center frequency of the bandstop \\
\hline
{\em width\+Frequency} & Width of the bandstop \\
\hline
{\em stop\+Band\+Db} & Permitted ripples in dB in the stopband \\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{structIir_1_1ChebyshevII_1_1BandStop_abb424ebd2398df91406042580b25a173}\label{structIir_1_1ChebyshevII_1_1BandStop_abb424ebd2398df91406042580b25a173}} 
\index{Iir::ChebyshevII::BandStop$<$ FilterOrder, StateType $>$@{Iir::ChebyshevII::BandStop$<$ FilterOrder, StateType $>$}!setup@{setup}}
\index{setup@{setup}!Iir::ChebyshevII::BandStop$<$ FilterOrder, StateType $>$@{Iir::ChebyshevII::BandStop$<$ FilterOrder, StateType $>$}}
\doxyparagraph{\texorpdfstring{setup()}{setup()}\hspace{0.1cm}{\footnotesize\ttfamily [2/2]}}
{\footnotesize\ttfamily template$<$int Filter\+Order = 4, class State\+Type  = Direct\+Form\+II$>$ \\
void \mbox{\hyperlink{structIir_1_1ChebyshevII_1_1BandStop}{Iir\+::\+Chebyshev\+II\+::\+Band\+Stop}}$<$ Filter\+Order, State\+Type $>$\+::setup (\begin{DoxyParamCaption}\item[{int}]{req\+Order,  }\item[{double}]{sample\+Rate,  }\item[{double}]{center\+Frequency,  }\item[{double}]{width\+Frequency,  }\item[{double}]{stop\+Band\+Db }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients of the filter 
\begin{DoxyParams}{Parameters}
{\em req\+Order} & Requested order which can be less than the instantiated one \\
\hline
{\em sample\+Rate} & Sampling rate \\
\hline
{\em center\+Frequency} & Center frequency of the bandstop \\
\hline
{\em width\+Frequency} & Width of the bandstop \\
\hline
{\em stop\+Band\+Db} & Permitted ripples in dB in the stopband \\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{structIir_1_1ChebyshevII_1_1BandStop_ab91192c2d3bf48f69849aba72c0c1c31}\label{structIir_1_1ChebyshevII_1_1BandStop_ab91192c2d3bf48f69849aba72c0c1c31}} 
\index{Iir::ChebyshevII::BandStop$<$ FilterOrder, StateType $>$@{Iir::ChebyshevII::BandStop$<$ FilterOrder, StateType $>$}!setupN@{setupN}}
\index{setupN@{setupN}!Iir::ChebyshevII::BandStop$<$ FilterOrder, StateType $>$@{Iir::ChebyshevII::BandStop$<$ FilterOrder, StateType $>$}}
\doxyparagraph{\texorpdfstring{setupN()}{setupN()}\hspace{0.1cm}{\footnotesize\ttfamily [1/2]}}
{\footnotesize\ttfamily template$<$int Filter\+Order = 4, class State\+Type  = Direct\+Form\+II$>$ \\
void \mbox{\hyperlink{structIir_1_1ChebyshevII_1_1BandStop}{Iir\+::\+Chebyshev\+II\+::\+Band\+Stop}}$<$ Filter\+Order, State\+Type $>$\+::setupN (\begin{DoxyParamCaption}\item[{double}]{center\+Frequency,  }\item[{double}]{width\+Frequency,  }\item[{double}]{stop\+Band\+Db }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients of the filter 
\begin{DoxyParams}{Parameters}
{\em center\+Frequency} & Normalised centre frequency (0..1/2) of the bandstop \\
\hline
{\em width\+Frequency} & Width of the bandstop \\
\hline
{\em stop\+Band\+Db} & Permitted ripples in dB in the stopband \\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{structIir_1_1ChebyshevII_1_1BandStop_a4abf9f03fa285f15001d8d99f0f8fab4}\label{structIir_1_1ChebyshevII_1_1BandStop_a4abf9f03fa285f15001d8d99f0f8fab4}} 
\index{Iir::ChebyshevII::BandStop$<$ FilterOrder, StateType $>$@{Iir::ChebyshevII::BandStop$<$ FilterOrder, StateType $>$}!setupN@{setupN}}
\index{setupN@{setupN}!Iir::ChebyshevII::BandStop$<$ FilterOrder, StateType $>$@{Iir::ChebyshevII::BandStop$<$ FilterOrder, StateType $>$}}
\doxyparagraph{\texorpdfstring{setupN()}{setupN()}\hspace{0.1cm}{\footnotesize\ttfamily [2/2]}}
{\footnotesize\ttfamily template$<$int Filter\+Order = 4, class State\+Type  = Direct\+Form\+II$>$ \\
void \mbox{\hyperlink{structIir_1_1ChebyshevII_1_1BandStop}{Iir\+::\+Chebyshev\+II\+::\+Band\+Stop}}$<$ Filter\+Order, State\+Type $>$\+::setupN (\begin{DoxyParamCaption}\item[{int}]{req\+Order,  }\item[{double}]{center\+Frequency,  }\item[{double}]{width\+Frequency,  }\item[{double}]{stop\+Band\+Db }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients of the filter 
\begin{DoxyParams}{Parameters}
{\em req\+Order} & Requested order which can be less than the instantiated one \\
\hline
{\em center\+Frequency} & Normalised centre frequency (0..1/2) of the bandstop \\
\hline
{\em width\+Frequency} & Width of the bandstop \\
\hline
{\em stop\+Band\+Db} & Permitted ripples in dB in the stopband \\
\hline
\end{DoxyParams}


The documentation for this struct was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
iir/Chebyshev\+II.\+h\end{DoxyCompactItemize}
