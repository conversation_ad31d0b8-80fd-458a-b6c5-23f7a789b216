/********************************************************************************
** Form generated from reading UI file 'seqedit.ui'
**
** Created by: Qt User Interface Compiler version 5.15.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_SEQEDIT_H
#define UI_SEQEDIT_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QFormLayout>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QTextBrowser>
#include <QtWidgets/QTextEdit>
#include <QtWidgets/QTreeWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_SeqEdit
{
public:
    QWidget *layoutWidget;
    QVBoxLayout *verticalLayout_9;
    QWidget *widget;
    QGroupBox *groupBox;
    QTextEdit *SeqFilePathEdit;
    QPushButton *SeqFileOpenBtn;
    QPushButton *RecentSeqFileBtn;
    QPushButton *SeqEditTemBtn;
    QPushButton *SeqFileNewBtn;
    QPushButton *SeqFileSaveBtn;
    QPushButton *SeqEditCloseBtn;
    QPushButton *SeqFileSaveAsBtn;
    QHBoxLayout *horizontalLayout_4;
    QWidget *widget_5;
    QVBoxLayout *verticalLayout_2;
    QVBoxLayout *verticalLayout;
    QComboBox *SeqcomboBox;
    QLineEdit *lineEdit;
    QTreeWidget *LSSTreeWidget;
    QTextBrowser *textBrowser;
    QSpacerItem *verticalSpacer;
    QWidget *widget_6;
    QVBoxLayout *verticalLayout_3;
    QFormLayout *formLayout;
    QSpacerItem *verticalSpacer_3;
    QPushButton *MoveInUserBtn;
    QPushButton *MoveOutUserBtn;
    QSpacerItem *verticalSpacer_2;
    QVBoxLayout *verticalLayout_7;
    QWidget *widget_4;
    QHBoxLayout *horizontalLayout_2;
    QHBoxLayout *horizontalLayout;
    QPushButton *SeqEditUpBtn;
    QPushButton *SeqEditDownBtn;
    QSpacerItem *horizontalSpacer;
    QHBoxLayout *horizontalLayout_3;
    QWidget *widget_3;
    QVBoxLayout *verticalLayout_8;
    QTreeWidget *UserTreeWidget;
    QWidget *widget_2;
    QVBoxLayout *verticalLayout_6;
    QGroupBox *groupBox_2;
    QVBoxLayout *verticalLayout_4;
    QTextEdit *textEdit_2;

    void setupUi(QWidget *SeqEdit)
    {
        if (SeqEdit->objectName().isEmpty())
            SeqEdit->setObjectName(QString::fromUtf8("SeqEdit"));
        SeqEdit->resize(1507, 844);
        layoutWidget = new QWidget(SeqEdit);
        layoutWidget->setObjectName(QString::fromUtf8("layoutWidget"));
        layoutWidget->setGeometry(QRect(0, 0, 1501, 841));
        verticalLayout_9 = new QVBoxLayout(layoutWidget);
        verticalLayout_9->setObjectName(QString::fromUtf8("verticalLayout_9"));
        verticalLayout_9->setContentsMargins(0, 0, 0, 0);
        widget = new QWidget(layoutWidget);
        widget->setObjectName(QString::fromUtf8("widget"));
        widget->setMinimumSize(QSize(0, 120));
        widget->setStyleSheet(QString::fromUtf8(""));
        groupBox = new QGroupBox(widget);
        groupBox->setObjectName(QString::fromUtf8("groupBox"));
        groupBox->setGeometry(QRect(10, 0, 380, 111));
        SeqFilePathEdit = new QTextEdit(groupBox);
        SeqFilePathEdit->setObjectName(QString::fromUtf8("SeqFilePathEdit"));
        SeqFilePathEdit->setGeometry(QRect(12, 27, 281, 72));
        SeqFileOpenBtn = new QPushButton(groupBox);
        SeqFileOpenBtn->setObjectName(QString::fromUtf8("SeqFileOpenBtn"));
        SeqFileOpenBtn->setGeometry(QRect(310, 30, 50, 28));
        SeqFileOpenBtn->setMaximumSize(QSize(50, 16777215));
        RecentSeqFileBtn = new QPushButton(widget);
        RecentSeqFileBtn->setObjectName(QString::fromUtf8("RecentSeqFileBtn"));
        RecentSeqFileBtn->setGeometry(QRect(410, 20, 93, 28));
        SeqEditTemBtn = new QPushButton(widget);
        SeqEditTemBtn->setObjectName(QString::fromUtf8("SeqEditTemBtn"));
        SeqEditTemBtn->setGeometry(QRect(520, 20, 93, 28));
        SeqFileNewBtn = new QPushButton(widget);
        SeqFileNewBtn->setObjectName(QString::fromUtf8("SeqFileNewBtn"));
        SeqFileNewBtn->setGeometry(QRect(650, 20, 93, 28));
        SeqFileSaveBtn = new QPushButton(widget);
        SeqFileSaveBtn->setObjectName(QString::fromUtf8("SeqFileSaveBtn"));
        SeqFileSaveBtn->setGeometry(QRect(750, 20, 93, 28));
        SeqEditCloseBtn = new QPushButton(widget);
        SeqEditCloseBtn->setObjectName(QString::fromUtf8("SeqEditCloseBtn"));
        SeqEditCloseBtn->setGeometry(QRect(1260, 30, 93, 28));
        SeqFileSaveAsBtn = new QPushButton(widget);
        SeqFileSaveAsBtn->setObjectName(QString::fromUtf8("SeqFileSaveAsBtn"));
        SeqFileSaveAsBtn->setGeometry(QRect(860, 20, 93, 28));

        verticalLayout_9->addWidget(widget);

        horizontalLayout_4 = new QHBoxLayout();
        horizontalLayout_4->setSpacing(1);
        horizontalLayout_4->setObjectName(QString::fromUtf8("horizontalLayout_4"));
        widget_5 = new QWidget(layoutWidget);
        widget_5->setObjectName(QString::fromUtf8("widget_5"));
        widget_5->setMinimumSize(QSize(400, 400));
        widget_5->setMaximumSize(QSize(400, 16777215));
        widget_5->setStyleSheet(QString::fromUtf8("background-color: rgb(255, 255, 255);"));
        verticalLayout_2 = new QVBoxLayout(widget_5);
        verticalLayout_2->setObjectName(QString::fromUtf8("verticalLayout_2"));
        verticalLayout = new QVBoxLayout();
        verticalLayout->setSpacing(1);
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        SeqcomboBox = new QComboBox(widget_5);
        SeqcomboBox->addItem(QString());
        SeqcomboBox->addItem(QString());
        SeqcomboBox->addItem(QString());
        SeqcomboBox->addItem(QString());
        SeqcomboBox->setObjectName(QString::fromUtf8("SeqcomboBox"));
        SeqcomboBox->setMinimumSize(QSize(0, 30));
        SeqcomboBox->setMaximumSize(QSize(16777215, 100));

        verticalLayout->addWidget(SeqcomboBox);

        lineEdit = new QLineEdit(widget_5);
        lineEdit->setObjectName(QString::fromUtf8("lineEdit"));
        lineEdit->setMinimumSize(QSize(0, 30));
        lineEdit->setMaximumSize(QSize(16777215, 30));

        verticalLayout->addWidget(lineEdit);

        LSSTreeWidget = new QTreeWidget(widget_5);
        QIcon icon;
        icon.addFile(QString::fromUtf8("../../../16412/Pictures/\350\201\224\346\203\263\351\224\201\345\261\217\345\243\201\347\272\270/8510685.jpg"), QSize(), QIcon::Normal, QIcon::Off);
        QTreeWidgetItem *__qtreewidgetitem = new QTreeWidgetItem(LSSTreeWidget);
        QTreeWidgetItem *__qtreewidgetitem1 = new QTreeWidgetItem(__qtreewidgetitem);
        __qtreewidgetitem1->setIcon(0, icon);
        new QTreeWidgetItem(__qtreewidgetitem);
        new QTreeWidgetItem(__qtreewidgetitem);
        new QTreeWidgetItem(__qtreewidgetitem);
        new QTreeWidgetItem(__qtreewidgetitem);
        QTreeWidgetItem *__qtreewidgetitem2 = new QTreeWidgetItem(LSSTreeWidget);
        new QTreeWidgetItem(__qtreewidgetitem2);
        new QTreeWidgetItem(__qtreewidgetitem2);
        QTreeWidgetItem *__qtreewidgetitem3 = new QTreeWidgetItem(LSSTreeWidget);
        new QTreeWidgetItem(__qtreewidgetitem3);
        QTreeWidgetItem *__qtreewidgetitem4 = new QTreeWidgetItem(LSSTreeWidget);
        new QTreeWidgetItem(__qtreewidgetitem4);
        QTreeWidgetItem *__qtreewidgetitem5 = new QTreeWidgetItem(LSSTreeWidget);
        new QTreeWidgetItem(__qtreewidgetitem5);
        LSSTreeWidget->setObjectName(QString::fromUtf8("LSSTreeWidget"));

        verticalLayout->addWidget(LSSTreeWidget);

        textBrowser = new QTextBrowser(widget_5);
        textBrowser->setObjectName(QString::fromUtf8("textBrowser"));
        textBrowser->setMinimumSize(QSize(0, 100));
        textBrowser->setMaximumSize(QSize(16777215, 100));

        verticalLayout->addWidget(textBrowser);


        verticalLayout_2->addLayout(verticalLayout);

        verticalSpacer = new QSpacerItem(20, 1, QSizePolicy::Minimum, QSizePolicy::Fixed);

        verticalLayout_2->addItem(verticalSpacer);


        horizontalLayout_4->addWidget(widget_5);

        widget_6 = new QWidget(layoutWidget);
        widget_6->setObjectName(QString::fromUtf8("widget_6"));
        widget_6->setMinimumSize(QSize(120, 0));
        widget_6->setMaximumSize(QSize(120, 16777215));
        widget_6->setStyleSheet(QString::fromUtf8("background-color: rgb(255, 255, 255);"));
        verticalLayout_3 = new QVBoxLayout(widget_6);
        verticalLayout_3->setObjectName(QString::fromUtf8("verticalLayout_3"));
        formLayout = new QFormLayout();
        formLayout->setObjectName(QString::fromUtf8("formLayout"));
        verticalSpacer_3 = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        formLayout->setItem(0, QFormLayout::LabelRole, verticalSpacer_3);

        MoveInUserBtn = new QPushButton(widget_6);
        MoveInUserBtn->setObjectName(QString::fromUtf8("MoveInUserBtn"));
        MoveInUserBtn->setMinimumSize(QSize(80, 30));

        formLayout->setWidget(1, QFormLayout::LabelRole, MoveInUserBtn);

        MoveOutUserBtn = new QPushButton(widget_6);
        MoveOutUserBtn->setObjectName(QString::fromUtf8("MoveOutUserBtn"));
        MoveOutUserBtn->setMinimumSize(QSize(80, 30));
        MoveOutUserBtn->setMaximumSize(QSize(100, 30));

        formLayout->setWidget(2, QFormLayout::LabelRole, MoveOutUserBtn);

        verticalSpacer_2 = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        formLayout->setItem(3, QFormLayout::LabelRole, verticalSpacer_2);


        verticalLayout_3->addLayout(formLayout);


        horizontalLayout_4->addWidget(widget_6);

        verticalLayout_7 = new QVBoxLayout();
        verticalLayout_7->setObjectName(QString::fromUtf8("verticalLayout_7"));
        widget_4 = new QWidget(layoutWidget);
        widget_4->setObjectName(QString::fromUtf8("widget_4"));
        widget_4->setStyleSheet(QString::fromUtf8("background-color: rgb(255, 255, 255);"));
        horizontalLayout_2 = new QHBoxLayout(widget_4);
        horizontalLayout_2->setObjectName(QString::fromUtf8("horizontalLayout_2"));
        horizontalLayout = new QHBoxLayout();
        horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));
        SeqEditUpBtn = new QPushButton(widget_4);
        SeqEditUpBtn->setObjectName(QString::fromUtf8("SeqEditUpBtn"));
        SeqEditUpBtn->setMaximumSize(QSize(50, 50));

        horizontalLayout->addWidget(SeqEditUpBtn);

        SeqEditDownBtn = new QPushButton(widget_4);
        SeqEditDownBtn->setObjectName(QString::fromUtf8("SeqEditDownBtn"));
        SeqEditDownBtn->setMaximumSize(QSize(50, 50));

        horizontalLayout->addWidget(SeqEditDownBtn);


        horizontalLayout_2->addLayout(horizontalLayout);

        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_2->addItem(horizontalSpacer);


        verticalLayout_7->addWidget(widget_4);

        horizontalLayout_3 = new QHBoxLayout();
        horizontalLayout_3->setObjectName(QString::fromUtf8("horizontalLayout_3"));
        widget_3 = new QWidget(layoutWidget);
        widget_3->setObjectName(QString::fromUtf8("widget_3"));
        widget_3->setMinimumSize(QSize(500, 0));
        widget_3->setStyleSheet(QString::fromUtf8(""));
        verticalLayout_8 = new QVBoxLayout(widget_3);
        verticalLayout_8->setObjectName(QString::fromUtf8("verticalLayout_8"));
        UserTreeWidget = new QTreeWidget(widget_3);
        QTreeWidgetItem *__qtreewidgetitem6 = new QTreeWidgetItem(UserTreeWidget);
        new QTreeWidgetItem(__qtreewidgetitem6);
        QTreeWidgetItem *__qtreewidgetitem7 = new QTreeWidgetItem(UserTreeWidget);
        new QTreeWidgetItem(__qtreewidgetitem7);
        QTreeWidgetItem *__qtreewidgetitem8 = new QTreeWidgetItem(UserTreeWidget);
        new QTreeWidgetItem(__qtreewidgetitem8);
        UserTreeWidget->setObjectName(QString::fromUtf8("UserTreeWidget"));
        UserTreeWidget->setColumnCount(3);

        verticalLayout_8->addWidget(UserTreeWidget);


        horizontalLayout_3->addWidget(widget_3);

        widget_2 = new QWidget(layoutWidget);
        widget_2->setObjectName(QString::fromUtf8("widget_2"));
        widget_2->setMaximumSize(QSize(250, 16777215));
        widget_2->setStyleSheet(QString::fromUtf8("background-color: rgb(255, 255, 255);"));
        verticalLayout_6 = new QVBoxLayout(widget_2);
        verticalLayout_6->setObjectName(QString::fromUtf8("verticalLayout_6"));
        groupBox_2 = new QGroupBox(widget_2);
        groupBox_2->setObjectName(QString::fromUtf8("groupBox_2"));
        verticalLayout_4 = new QVBoxLayout(groupBox_2);
        verticalLayout_4->setObjectName(QString::fromUtf8("verticalLayout_4"));
        textEdit_2 = new QTextEdit(groupBox_2);
        textEdit_2->setObjectName(QString::fromUtf8("textEdit_2"));

        verticalLayout_4->addWidget(textEdit_2);


        verticalLayout_6->addWidget(groupBox_2);


        horizontalLayout_3->addWidget(widget_2);


        verticalLayout_7->addLayout(horizontalLayout_3);


        horizontalLayout_4->addLayout(verticalLayout_7);


        verticalLayout_9->addLayout(horizontalLayout_4);


        retranslateUi(SeqEdit);

        QMetaObject::connectSlotsByName(SeqEdit);
    } // setupUi

    void retranslateUi(QWidget *SeqEdit)
    {
        SeqEdit->setWindowTitle(QCoreApplication::translate("SeqEdit", "\345\272\217\345\210\227\347\274\226\350\276\221", nullptr));
        groupBox->setTitle(QCoreApplication::translate("SeqEdit", "\345\272\217\345\210\227\350\267\257\345\276\204", nullptr));
        SeqFileOpenBtn->setText(QCoreApplication::translate("SeqEdit", "\346\211\223\345\274\200", nullptr));
        RecentSeqFileBtn->setText(QCoreApplication::translate("SeqEdit", "\346\234\200\350\277\221\346\226\207\344\273\266", nullptr));
        SeqEditTemBtn->setText(QCoreApplication::translate("SeqEdit", "\346\250\241\346\235\277", nullptr));
        SeqFileNewBtn->setText(QCoreApplication::translate("SeqEdit", "\346\226\260\345\273\272", nullptr));
        SeqFileSaveBtn->setText(QCoreApplication::translate("SeqEdit", "\344\277\235\345\255\230", nullptr));
        SeqEditCloseBtn->setText(QCoreApplication::translate("SeqEdit", "\351\200\200\345\207\272", nullptr));
        SeqFileSaveAsBtn->setText(QCoreApplication::translate("SeqEdit", "\345\217\246\345\255\230\344\270\272", nullptr));
        SeqcomboBox->setItemText(0, QCoreApplication::translate("SeqEdit", "\345\267\245\345\205\267", nullptr));
        SeqcomboBox->setItemText(1, QCoreApplication::translate("SeqEdit", "\346\277\200\345\212\261", nullptr));
        SeqcomboBox->setItemText(2, QCoreApplication::translate("SeqEdit", "\351\207\207\351\233\206", nullptr));
        SeqcomboBox->setItemText(3, QCoreApplication::translate("SeqEdit", "\351\207\207\351\233\206\344\270\216\346\277\200\345\212\261", nullptr));

        lineEdit->setText(QCoreApplication::translate("SeqEdit", "\350\276\223\345\205\245\346\237\245\346\211\276", nullptr));
        QTreeWidgetItem *___qtreewidgetitem = LSSTreeWidget->headerItem();
        ___qtreewidgetitem->setText(0, QCoreApplication::translate("SeqEdit", "\346\255\245\351\252\244\344\277\241\346\201\257", nullptr));

        const bool __sortingEnabled = LSSTreeWidget->isSortingEnabled();
        LSSTreeWidget->setSortingEnabled(false);
        QTreeWidgetItem *___qtreewidgetitem1 = LSSTreeWidget->topLevelItem(0);
        ___qtreewidgetitem1->setText(0, QCoreApplication::translate("SeqEdit", "\345\267\245\345\205\267", nullptr));
        QTreeWidgetItem *___qtreewidgetitem2 = ___qtreewidgetitem1->child(0);
        ___qtreewidgetitem2->setText(0, QCoreApplication::translate("SeqEdit", "\344\270\215\346\216\210\346\235\203\344\275\277\347\224\250", nullptr));
        QTreeWidgetItem *___qtreewidgetitem3 = ___qtreewidgetitem1->child(1);
        ___qtreewidgetitem3->setText(0, QCoreApplication::translate("SeqEdit", "\344\270\215\346\216\210\346\235\203\344\275\277\347\224\250", nullptr));
        QTreeWidgetItem *___qtreewidgetitem4 = ___qtreewidgetitem1->child(2);
        ___qtreewidgetitem4->setText(0, QCoreApplication::translate("SeqEdit", "\344\270\215\346\216\210\346\235\203\344\275\277\347\224\250", nullptr));
        QTreeWidgetItem *___qtreewidgetitem5 = ___qtreewidgetitem1->child(3);
        ___qtreewidgetitem5->setText(0, QCoreApplication::translate("SeqEdit", "\344\270\215\346\216\210\346\235\203\344\275\277\347\224\250", nullptr));
        QTreeWidgetItem *___qtreewidgetitem6 = ___qtreewidgetitem1->child(4);
        ___qtreewidgetitem6->setText(0, QCoreApplication::translate("SeqEdit", "\344\270\215\346\216\210\346\235\203\344\275\277\347\224\250", nullptr));
        QTreeWidgetItem *___qtreewidgetitem7 = LSSTreeWidget->topLevelItem(1);
        ___qtreewidgetitem7->setText(0, QCoreApplication::translate("SeqEdit", "\346\277\200\345\212\261", nullptr));
        QTreeWidgetItem *___qtreewidgetitem8 = ___qtreewidgetitem7->child(0);
        ___qtreewidgetitem8->setText(0, QCoreApplication::translate("SeqEdit", "\345\215\225\351\200\232\351\201\223\346\222\255\346\224\276\346\211\253\351\242\221", nullptr));
        QTreeWidgetItem *___qtreewidgetitem9 = ___qtreewidgetitem7->child(1);
        ___qtreewidgetitem9->setText(0, QCoreApplication::translate("SeqEdit", "\345\217\214\351\200\232\351\201\223\346\222\255\346\224\276\346\211\253\351\242\221", nullptr));
        QTreeWidgetItem *___qtreewidgetitem10 = LSSTreeWidget->topLevelItem(2);
        ___qtreewidgetitem10->setText(0, QCoreApplication::translate("SeqEdit", "\351\207\207\351\233\206", nullptr));
        QTreeWidgetItem *___qtreewidgetitem11 = ___qtreewidgetitem10->child(0);
        ___qtreewidgetitem11->setText(0, QCoreApplication::translate("SeqEdit", "\345\215\225\351\200\232\351\201\223\351\207\207\351\233\206", nullptr));
        QTreeWidgetItem *___qtreewidgetitem12 = LSSTreeWidget->topLevelItem(3);
        ___qtreewidgetitem12->setText(0, QCoreApplication::translate("SeqEdit", "\351\207\207\351\233\206\344\270\216\346\277\200\345\212\261", nullptr));
        QTreeWidgetItem *___qtreewidgetitem13 = ___qtreewidgetitem12->child(0);
        ___qtreewidgetitem13->setText(0, QCoreApplication::translate("SeqEdit", "\350\276\223\345\205\245&\350\276\223\345\207\272", nullptr));
        QTreeWidgetItem *___qtreewidgetitem14 = LSSTreeWidget->topLevelItem(4);
        ___qtreewidgetitem14->setText(0, QCoreApplication::translate("SeqEdit", "\346\265\213\350\257\225\344\270\216\345\210\206\346\236\220", nullptr));
        QTreeWidgetItem *___qtreewidgetitem15 = ___qtreewidgetitem14->child(0);
        ___qtreewidgetitem15->setText(0, QCoreApplication::translate("SeqEdit", "\351\242\221\345\223\215\346\265\213\350\257\225\357\274\210\345\215\225\351\200\232\351\201\223\357\274\211", nullptr));
        LSSTreeWidget->setSortingEnabled(__sortingEnabled);

        textBrowser->setHtml(QCoreApplication::translate("SeqEdit", "<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.0//EN\" \"http://www.w3.org/TR/REC-html40/strict.dtd\">\n"
"<html><head><meta name=\"qrichtext\" content=\"1\" /><style type=\"text/css\">\n"
"p, li { white-space: pre-wrap; }\n"
"</style></head><body style=\" font-family:'SimSun'; font-size:9pt; font-weight:400; font-style:normal;\">\n"
"<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\">\350\257\264\346\230\216</p></body></html>", nullptr));
        MoveInUserBtn->setText(QCoreApplication::translate("SeqEdit", "\343\200\213\343\200\213\343\200\213", nullptr));
        MoveOutUserBtn->setText(QCoreApplication::translate("SeqEdit", "\343\200\212\343\200\212\343\200\212", nullptr));
        SeqEditUpBtn->setText(QCoreApplication::translate("SeqEdit", "\344\270\212\347\247\273", nullptr));
        SeqEditDownBtn->setText(QCoreApplication::translate("SeqEdit", "\344\270\213\347\247\273", nullptr));
        QTreeWidgetItem *___qtreewidgetitem16 = UserTreeWidget->headerItem();
        ___qtreewidgetitem16->setText(2, QCoreApplication::translate("SeqEdit", "\345\244\207\346\263\250", nullptr));
        ___qtreewidgetitem16->setText(1, QCoreApplication::translate("SeqEdit", "\346\255\245\351\252\244\344\277\241\346\201\257", nullptr));
        ___qtreewidgetitem16->setText(0, QCoreApplication::translate("SeqEdit", "\346\230\276\347\244\272\345\220\215\347\247\260", nullptr));

        const bool __sortingEnabled1 = UserTreeWidget->isSortingEnabled();
        UserTreeWidget->setSortingEnabled(false);
        QTreeWidgetItem *___qtreewidgetitem17 = UserTreeWidget->topLevelItem(0);
        ___qtreewidgetitem17->setText(0, QCoreApplication::translate("SeqEdit", "\345\210\235\345\247\213\345\214\226\345\272\217\345\210\227", nullptr));
        QTreeWidgetItem *___qtreewidgetitem18 = ___qtreewidgetitem17->child(0);
        ___qtreewidgetitem18->setText(0, QCoreApplication::translate("SeqEdit", "\345\210\235\345\247\213\345\214\226\345\272\217\345\210\227\347\273\223\346\235\237", nullptr));
        QTreeWidgetItem *___qtreewidgetitem19 = UserTreeWidget->topLevelItem(1);
        ___qtreewidgetitem19->setText(0, QCoreApplication::translate("SeqEdit", "\344\270\273\345\272\217\345\210\227", nullptr));
        QTreeWidgetItem *___qtreewidgetitem20 = ___qtreewidgetitem19->child(0);
        ___qtreewidgetitem20->setText(0, QCoreApplication::translate("SeqEdit", "\344\270\273\345\272\217\345\210\227\347\273\223\346\235\237", nullptr));
        QTreeWidgetItem *___qtreewidgetitem21 = UserTreeWidget->topLevelItem(2);
        ___qtreewidgetitem21->setText(0, QCoreApplication::translate("SeqEdit", "\347\273\223\346\235\237\345\272\217\345\210\227", nullptr));
        QTreeWidgetItem *___qtreewidgetitem22 = ___qtreewidgetitem21->child(0);
        ___qtreewidgetitem22->setText(0, QCoreApplication::translate("SeqEdit", "\347\273\223\346\235\237\345\272\217\345\210\227\347\273\223\346\235\237", nullptr));
        UserTreeWidget->setSortingEnabled(__sortingEnabled1);

        groupBox_2->setTitle(QCoreApplication::translate("SeqEdit", "\345\272\217\345\210\227\346\217\217\350\277\260", nullptr));
    } // retranslateUi

};

namespace Ui {
    class SeqEdit: public Ui_SeqEdit {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_SEQEDIT_H
