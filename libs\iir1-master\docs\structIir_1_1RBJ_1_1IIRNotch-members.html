<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>iir1: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">iir1
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespaceIir.html">Iir</a></li><li class="navelem"><b>RBJ</b></li><li class="navelem"><a class="el" href="structIir_1_1RBJ_1_1IIRNotch.html">IIRNotch</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">Iir::RBJ::IIRNotch Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="structIir_1_1RBJ_1_1IIRNotch.html">Iir::RBJ::IIRNotch</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="classIir_1_1Biquad.html#a38c8e327dca53dbfa9a25758b7be8227">applyScale</a>(double scale)</td><td class="entry"><a class="el" href="classIir_1_1Biquad.html">Iir::Biquad</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="structIir_1_1RBJ_1_1RBJbase.html#a5dd179c8491e29966d5c33036cabe151">filter</a>(Sample s)</td><td class="entry"><a class="el" href="structIir_1_1RBJ_1_1RBJbase.html">Iir::RBJ::RBJbase</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classIir_1_1Biquad.html#a527b9666d6aef0e576193fdce7e19750">Iir::Biquad::filter</a>(double s, StateType &amp;state) const</td><td class="entry"><a class="el" href="classIir_1_1Biquad.html">Iir::Biquad</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classIir_1_1Biquad.html#a7debc9f6ef5e64622c710b8c9ba96056">getA0</a>() const</td><td class="entry"><a class="el" href="classIir_1_1Biquad.html">Iir::Biquad</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classIir_1_1Biquad.html#a459116db7aa381281997f428f15334cf">getA1</a>() const</td><td class="entry"><a class="el" href="classIir_1_1Biquad.html">Iir::Biquad</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classIir_1_1Biquad.html#a061e2402d528312fc6095db6551c9141">getA2</a>() const</td><td class="entry"><a class="el" href="classIir_1_1Biquad.html">Iir::Biquad</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classIir_1_1Biquad.html#a84686da988e160a216f3e7057682ffbb">getB0</a>() const</td><td class="entry"><a class="el" href="classIir_1_1Biquad.html">Iir::Biquad</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classIir_1_1Biquad.html#af418a5f260baadbcffe5a7029f089937">getB1</a>() const</td><td class="entry"><a class="el" href="classIir_1_1Biquad.html">Iir::Biquad</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classIir_1_1Biquad.html#a8f3d697bb7c2def508da648938f6afc3">getB2</a>() const</td><td class="entry"><a class="el" href="classIir_1_1Biquad.html">Iir::Biquad</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classIir_1_1Biquad.html#a63c78d766bc40be10004a34aaebfb8e7">getPoleZeros</a>() const</td><td class="entry"><a class="el" href="classIir_1_1Biquad.html">Iir::Biquad</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="structIir_1_1RBJ_1_1RBJbase.html#a8093409edfce007a4972fa2992d69670">getState</a>()</td><td class="entry"><a class="el" href="structIir_1_1RBJ_1_1RBJbase.html">Iir::RBJ::RBJbase</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="structIir_1_1RBJ_1_1RBJbase.html#a0bd9a0b60e60b9326a14e43acbb430ab">reset</a>()</td><td class="entry"><a class="el" href="structIir_1_1RBJ_1_1RBJbase.html">Iir::RBJ::RBJbase</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classIir_1_1Biquad.html#ace03653f66fe65eddc55d48840458440">response</a>(double normalizedFrequency) const</td><td class="entry"><a class="el" href="classIir_1_1Biquad.html">Iir::Biquad</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classIir_1_1Biquad.html#a7f005089c194c68aeecdc66a3e6c6a78">setCoefficients</a>(double a0, double a1, double a2, double b0, double b1, double b2)</td><td class="entry"><a class="el" href="classIir_1_1Biquad.html">Iir::Biquad</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classIir_1_1Biquad.html#a53b066d077ce559a91f26fc3c4201c2c">setIdentity</a>()</td><td class="entry"><a class="el" href="classIir_1_1Biquad.html">Iir::Biquad</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classIir_1_1Biquad.html#a455fd42a2e99ac84b09184becf2d047f">setOnePole</a>(complex_t pole, complex_t zero)</td><td class="entry"><a class="el" href="classIir_1_1Biquad.html">Iir::Biquad</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classIir_1_1Biquad.html#a69b4a2eaedb4b51aea9fb46a99c3d3c2">setPoleZeroPair</a>(const PoleZeroPair &amp;pair)</td><td class="entry"><a class="el" href="classIir_1_1Biquad.html">Iir::Biquad</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classIir_1_1Biquad.html#a6c614e84db8493b8495b314dd860d0bc">setTwoPole</a>(complex_t pole1, complex_t zero1, complex_t pole2, complex_t zero2)</td><td class="entry"><a class="el" href="classIir_1_1Biquad.html">Iir::Biquad</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="structIir_1_1RBJ_1_1IIRNotch.html#aedecb6358bc0a907742c56d5235d35df">setup</a>(double sampleRate, double centerFrequency, double q_factor=10)</td><td class="entry"><a class="el" href="structIir_1_1RBJ_1_1IIRNotch.html">Iir::RBJ::IIRNotch</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="structIir_1_1RBJ_1_1IIRNotch.html#a824ce85d420097081e71b3888acb7862">setupN</a>(double centerFrequency, double q_factor=10)</td><td class="entry"><a class="el" href="structIir_1_1RBJ_1_1IIRNotch.html">Iir::RBJ::IIRNotch</a></td><td class="entry"></td></tr>
</table></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Fri Jan 6 2023 23:53:46 for iir1 by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
