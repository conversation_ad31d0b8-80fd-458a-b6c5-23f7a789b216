/********************************************************************************
** Form generated from reading UI file 'mainsetform.ui'
**
** Created by: Qt User Interface Compiler version 5.15.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_MAINSETFORM_H
#define UI_MAINSETFORM_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_MainSetForm
{
public:
    QWidget *layoutWidget;
    QHBoxLayout *horizontalLayout;
    QGridLayout *gridLayout;
    QPushButton *ChanalSetBtn;
    QPushButton *pushButton_11;
    QPushButton *pushButton_12;
    QPushButton *pushButton_14;
    QPushButton *pushButton_15;
    QPushButton *pushButton_13;
    QPushButton *pushButton_17;
    QPushButton *pushButton_16;
    QPushButton *pushButton_18;
    QSpacerItem *horizontalSpacer;
    QVBoxLayout *verticalLayout;
    QGroupBox *groupBox;
    QVBoxLayout *verticalLayout_3;
    QComboBox *comboBox;
    QVBoxLayout *verticalLayout_2;
    QLabel *label;
    QLabel *label_2;
    QSpacerItem *verticalSpacer;

    void setupUi(QWidget *MainSetForm)
    {
        if (MainSetForm->objectName().isEmpty())
            MainSetForm->setObjectName(QString::fromUtf8("MainSetForm"));
        MainSetForm->resize(961, 479);
        layoutWidget = new QWidget(MainSetForm);
        layoutWidget->setObjectName(QString::fromUtf8("layoutWidget"));
        layoutWidget->setGeometry(QRect(100, 110, 761, 251));
        horizontalLayout = new QHBoxLayout(layoutWidget);
        horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));
        horizontalLayout->setContentsMargins(0, 0, 0, 0);
        gridLayout = new QGridLayout();
        gridLayout->setSpacing(30);
        gridLayout->setObjectName(QString::fromUtf8("gridLayout"));
        ChanalSetBtn = new QPushButton(layoutWidget);
        ChanalSetBtn->setObjectName(QString::fromUtf8("ChanalSetBtn"));
        ChanalSetBtn->setMinimumSize(QSize(170, 60));
        ChanalSetBtn->setStyleSheet(QString::fromUtf8("QPushButton:enabled {\n"
"	font: 15pt \"\351\273\221\344\275\223\";\n"
"    border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 1px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"    background-color: white;  /* \351\273\230\350\256\244\350\203\214\346\231\257\350\211\262 */	\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"}\n"
"QPushButton:enabled:hover {\n"
"    border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 5px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"    background-color: lightblue; /* \346\202\254\346\265\256\346\227\266\350\203"
                        "\214\346\231\257\345\217\230\350\211\262 */\n"
"}\n"
"/* \347\246\201\347\224\250\347\212\266\346\200\201\344\270\213\347\232\204\346\240\267\345\274\217 */\n"
"    QPushButton:disabled {\n"
"        border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"        padding: 15px; /* \344\270\216\345\220\257\347\224\250\347\212\266\346\200\201\344\270\200\350\207\264 */\n"
"        background-color: rgb(229, 229, 229); /* \347\201\260\350\211\262\350\203\214\346\231\257 */\n"
"        border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"    }"));

        gridLayout->addWidget(ChanalSetBtn, 0, 0, 1, 1);

        pushButton_11 = new QPushButton(layoutWidget);
        pushButton_11->setObjectName(QString::fromUtf8("pushButton_11"));
        pushButton_11->setMinimumSize(QSize(170, 60));
        pushButton_11->setStyleSheet(QString::fromUtf8("QPushButton:enabled {\n"
"	font: 15pt \"\351\273\221\344\275\223\";\n"
"    border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 1px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"    background-color: white;  /* \351\273\230\350\256\244\350\203\214\346\231\257\350\211\262 */	\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"}\n"
"QPushButton:enabled:hover {\n"
"    border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 5px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"    background-color: lightblue; /* \346\202\254\346\265\256\346\227\266\350\203"
                        "\214\346\231\257\345\217\230\350\211\262 */\n"
"}\n"
"/* \347\246\201\347\224\250\347\212\266\346\200\201\344\270\213\347\232\204\346\240\267\345\274\217 */\n"
"    QPushButton:disabled {\n"
"        border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"        padding: 15px; /* \344\270\216\345\220\257\347\224\250\347\212\266\346\200\201\344\270\200\350\207\264 */\n"
"        background-color: rgb(229, 229, 229); /* \347\201\260\350\211\262\350\203\214\346\231\257 */\n"
"        border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"    }"));

        gridLayout->addWidget(pushButton_11, 0, 1, 1, 1);

        pushButton_12 = new QPushButton(layoutWidget);
        pushButton_12->setObjectName(QString::fromUtf8("pushButton_12"));
        pushButton_12->setMinimumSize(QSize(170, 60));
        pushButton_12->setStyleSheet(QString::fromUtf8("QPushButton:enabled {\n"
"	font: 15pt \"\351\273\221\344\275\223\";\n"
"    border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 1px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"    background-color: white;  /* \351\273\230\350\256\244\350\203\214\346\231\257\350\211\262 */	\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"}\n"
"QPushButton:enabled:hover {\n"
"    border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 5px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"    background-color: lightblue; /* \346\202\254\346\265\256\346\227\266\350\203"
                        "\214\346\231\257\345\217\230\350\211\262 */\n"
"}\n"
"/* \347\246\201\347\224\250\347\212\266\346\200\201\344\270\213\347\232\204\346\240\267\345\274\217 */\n"
"    QPushButton:disabled {\n"
"        border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"        padding: 15px; /* \344\270\216\345\220\257\347\224\250\347\212\266\346\200\201\344\270\200\350\207\264 */\n"
"        background-color: rgb(229, 229, 229); /* \347\201\260\350\211\262\350\203\214\346\231\257 */\n"
"        border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"    }"));

        gridLayout->addWidget(pushButton_12, 0, 2, 1, 1);

        pushButton_14 = new QPushButton(layoutWidget);
        pushButton_14->setObjectName(QString::fromUtf8("pushButton_14"));
        pushButton_14->setMinimumSize(QSize(170, 60));
        pushButton_14->setStyleSheet(QString::fromUtf8("QPushButton:enabled {\n"
"	font: 15pt \"\351\273\221\344\275\223\";\n"
"    border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 1px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"    background-color: white;  /* \351\273\230\350\256\244\350\203\214\346\231\257\350\211\262 */	\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"}\n"
"QPushButton:enabled:hover {\n"
"    border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 5px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"    background-color: lightblue; /* \346\202\254\346\265\256\346\227\266\350\203"
                        "\214\346\231\257\345\217\230\350\211\262 */\n"
"}\n"
"/* \347\246\201\347\224\250\347\212\266\346\200\201\344\270\213\347\232\204\346\240\267\345\274\217 */\n"
"    QPushButton:disabled {\n"
"        border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"        padding: 15px; /* \344\270\216\345\220\257\347\224\250\347\212\266\346\200\201\344\270\200\350\207\264 */\n"
"        background-color: rgb(229, 229, 229); /* \347\201\260\350\211\262\350\203\214\346\231\257 */\n"
"        border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"    }"));

        gridLayout->addWidget(pushButton_14, 1, 0, 1, 1);

        pushButton_15 = new QPushButton(layoutWidget);
        pushButton_15->setObjectName(QString::fromUtf8("pushButton_15"));
        pushButton_15->setMinimumSize(QSize(170, 60));
        pushButton_15->setStyleSheet(QString::fromUtf8("QPushButton:enabled {\n"
"	font: 15pt \"\351\273\221\344\275\223\";\n"
"    border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 1px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"    background-color: white;  /* \351\273\230\350\256\244\350\203\214\346\231\257\350\211\262 */	\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"}\n"
"QPushButton:enabled:hover {\n"
"    border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 5px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"    background-color: lightblue; /* \346\202\254\346\265\256\346\227\266\350\203"
                        "\214\346\231\257\345\217\230\350\211\262 */\n"
"}\n"
"/* \347\246\201\347\224\250\347\212\266\346\200\201\344\270\213\347\232\204\346\240\267\345\274\217 */\n"
"    QPushButton:disabled {\n"
"        border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"        padding: 15px; /* \344\270\216\345\220\257\347\224\250\347\212\266\346\200\201\344\270\200\350\207\264 */\n"
"        background-color: rgb(229, 229, 229); /* \347\201\260\350\211\262\350\203\214\346\231\257 */\n"
"        border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"    }"));

        gridLayout->addWidget(pushButton_15, 1, 1, 1, 1);

        pushButton_13 = new QPushButton(layoutWidget);
        pushButton_13->setObjectName(QString::fromUtf8("pushButton_13"));
        pushButton_13->setMinimumSize(QSize(170, 60));
        pushButton_13->setStyleSheet(QString::fromUtf8("QPushButton:enabled {\n"
"	font: 15pt \"\351\273\221\344\275\223\";\n"
"    border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 1px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"    background-color: white;  /* \351\273\230\350\256\244\350\203\214\346\231\257\350\211\262 */	\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"}\n"
"QPushButton:enabled:hover {\n"
"    border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 5px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"    background-color: lightblue; /* \346\202\254\346\265\256\346\227\266\350\203"
                        "\214\346\231\257\345\217\230\350\211\262 */\n"
"}\n"
"/* \347\246\201\347\224\250\347\212\266\346\200\201\344\270\213\347\232\204\346\240\267\345\274\217 */\n"
"    QPushButton:disabled {\n"
"        border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"        padding: 15px; /* \344\270\216\345\220\257\347\224\250\347\212\266\346\200\201\344\270\200\350\207\264 */\n"
"        background-color: rgb(229, 229, 229); /* \347\201\260\350\211\262\350\203\214\346\231\257 */\n"
"        border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"    }"));

        gridLayout->addWidget(pushButton_13, 1, 2, 1, 1);

        pushButton_17 = new QPushButton(layoutWidget);
        pushButton_17->setObjectName(QString::fromUtf8("pushButton_17"));
        pushButton_17->setMinimumSize(QSize(170, 60));
        pushButton_17->setStyleSheet(QString::fromUtf8("QPushButton:enabled {\n"
"	font: 15pt \"\351\273\221\344\275\223\";\n"
"    border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 1px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"    background-color: white;  /* \351\273\230\350\256\244\350\203\214\346\231\257\350\211\262 */	\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"}\n"
"QPushButton:enabled:hover {\n"
"    border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 5px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"    background-color: lightblue; /* \346\202\254\346\265\256\346\227\266\350\203"
                        "\214\346\231\257\345\217\230\350\211\262 */\n"
"}\n"
"/* \347\246\201\347\224\250\347\212\266\346\200\201\344\270\213\347\232\204\346\240\267\345\274\217 */\n"
"    QPushButton:disabled {\n"
"        border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"        padding: 15px; /* \344\270\216\345\220\257\347\224\250\347\212\266\346\200\201\344\270\200\350\207\264 */\n"
"        background-color: rgb(229, 229, 229); /* \347\201\260\350\211\262\350\203\214\346\231\257 */\n"
"        border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"    }"));

        gridLayout->addWidget(pushButton_17, 2, 0, 1, 1);

        pushButton_16 = new QPushButton(layoutWidget);
        pushButton_16->setObjectName(QString::fromUtf8("pushButton_16"));
        pushButton_16->setMinimumSize(QSize(170, 60));
        pushButton_16->setStyleSheet(QString::fromUtf8("QPushButton:enabled {\n"
"	font: 15pt \"\351\273\221\344\275\223\";\n"
"    border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 1px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"    background-color: white;  /* \351\273\230\350\256\244\350\203\214\346\231\257\350\211\262 */	\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"}\n"
"QPushButton:enabled:hover {\n"
"    border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 5px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"    background-color: lightblue; /* \346\202\254\346\265\256\346\227\266\350\203"
                        "\214\346\231\257\345\217\230\350\211\262 */\n"
"}\n"
"/* \347\246\201\347\224\250\347\212\266\346\200\201\344\270\213\347\232\204\346\240\267\345\274\217 */\n"
"    QPushButton:disabled {\n"
"        border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"        padding: 15px; /* \344\270\216\345\220\257\347\224\250\347\212\266\346\200\201\344\270\200\350\207\264 */\n"
"        background-color: rgb(229, 229, 229); /* \347\201\260\350\211\262\350\203\214\346\231\257 */\n"
"        border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"    }"));

        gridLayout->addWidget(pushButton_16, 2, 1, 1, 1);

        pushButton_18 = new QPushButton(layoutWidget);
        pushButton_18->setObjectName(QString::fromUtf8("pushButton_18"));
        pushButton_18->setMinimumSize(QSize(170, 60));
        pushButton_18->setStyleSheet(QString::fromUtf8("QPushButton:enabled {\n"
"	font: 15pt \"\351\273\221\344\275\223\";\n"
"    border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 1px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"    background-color: white;  /* \351\273\230\350\256\244\350\203\214\346\231\257\350\211\262 */	\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"}\n"
"QPushButton:enabled:hover {\n"
"    border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 5px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"    background-color: lightblue; /* \346\202\254\346\265\256\346\227\266\350\203"
                        "\214\346\231\257\345\217\230\350\211\262 */\n"
"}\n"
"/* \347\246\201\347\224\250\347\212\266\346\200\201\344\270\213\347\232\204\346\240\267\345\274\217 */\n"
"    QPushButton:disabled {\n"
"        border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"        padding: 15px; /* \344\270\216\345\220\257\347\224\250\347\212\266\346\200\201\344\270\200\350\207\264 */\n"
"        background-color: rgb(229, 229, 229); /* \347\201\260\350\211\262\350\203\214\346\231\257 */\n"
"        border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"    }"));

        gridLayout->addWidget(pushButton_18, 2, 2, 1, 1);


        horizontalLayout->addLayout(gridLayout);

        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout->addItem(horizontalSpacer);

        verticalLayout = new QVBoxLayout();
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        groupBox = new QGroupBox(layoutWidget);
        groupBox->setObjectName(QString::fromUtf8("groupBox"));
        verticalLayout_3 = new QVBoxLayout(groupBox);
        verticalLayout_3->setObjectName(QString::fromUtf8("verticalLayout_3"));
        comboBox = new QComboBox(groupBox);
        comboBox->addItem(QString());
        comboBox->addItem(QString());
        comboBox->setObjectName(QString::fromUtf8("comboBox"));

        verticalLayout_3->addWidget(comboBox);


        verticalLayout->addWidget(groupBox);

        verticalLayout_2 = new QVBoxLayout();
        verticalLayout_2->setObjectName(QString::fromUtf8("verticalLayout_2"));
        label = new QLabel(layoutWidget);
        label->setObjectName(QString::fromUtf8("label"));

        verticalLayout_2->addWidget(label);

        label_2 = new QLabel(layoutWidget);
        label_2->setObjectName(QString::fromUtf8("label_2"));

        verticalLayout_2->addWidget(label_2);


        verticalLayout->addLayout(verticalLayout_2);

        verticalSpacer = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout->addItem(verticalSpacer);


        horizontalLayout->addLayout(verticalLayout);


        retranslateUi(MainSetForm);

        QMetaObject::connectSlotsByName(MainSetForm);
    } // setupUi

    void retranslateUi(QWidget *MainSetForm)
    {
        MainSetForm->setWindowTitle(QCoreApplication::translate("MainSetForm", "\350\256\276\347\275\256", nullptr));
        ChanalSetBtn->setText(QCoreApplication::translate("MainSetForm", "\351\200\232\351\201\223\350\256\276\347\275\256", nullptr));
        pushButton_11->setText(QCoreApplication::translate("MainSetForm", "\344\274\240\346\204\237\345\231\250\350\256\276\347\275\256", nullptr));
        pushButton_12->setText(QCoreApplication::translate("MainSetForm", "NULL", nullptr));
        pushButton_14->setText(QCoreApplication::translate("MainSetForm", "\346\277\200\345\212\261\344\277\241\345\217\267\350\256\276\347\275\256", nullptr));
        pushButton_15->setText(QCoreApplication::translate("MainSetForm", "NULL", nullptr));
        pushButton_13->setText(QCoreApplication::translate("MainSetForm", "NULL", nullptr));
        pushButton_17->setText(QCoreApplication::translate("MainSetForm", "NULL", nullptr));
        pushButton_16->setText(QCoreApplication::translate("MainSetForm", "NULL", nullptr));
        pushButton_18->setText(QCoreApplication::translate("MainSetForm", "NULL", nullptr));
        groupBox->setTitle(QCoreApplication::translate("MainSetForm", "Language/\350\257\255\350\250\200", nullptr));
        comboBox->setItemText(0, QCoreApplication::translate("MainSetForm", "\344\270\255\346\226\207", nullptr));
        comboBox->setItemText(1, QCoreApplication::translate("MainSetForm", "English", nullptr));

        label->setText(QCoreApplication::translate("MainSetForm", "\350\275\257\344\273\266\347\211\210\346\234\254", nullptr));
        label_2->setText(QCoreApplication::translate("MainSetForm", "1.0.0.0", nullptr));
    } // retranslateUi

};

namespace Ui {
    class MainSetForm: public Ui_MainSetForm {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_MAINSETFORM_H
