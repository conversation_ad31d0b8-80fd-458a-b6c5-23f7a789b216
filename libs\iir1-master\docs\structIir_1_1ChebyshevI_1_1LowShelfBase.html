<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>iir1: Iir::ChebyshevI::LowShelfBase Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">iir1
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespaceIir.html">Iir</a></li><li class="navelem"><a class="el" href="namespaceIir_1_1ChebyshevI.html">ChebyshevI</a></li><li class="navelem"><a class="el" href="structIir_1_1ChebyshevI_1_1LowShelfBase.html">LowShelfBase</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="structIir_1_1ChebyshevI_1_1LowShelfBase-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">Iir::ChebyshevI::LowShelfBase Struct Reference</div>  </div>
</div><!--header-->
<div class="contents">
<div class="dynheader">
Inheritance diagram for Iir::ChebyshevI::LowShelfBase:</div>
<div class="dyncontent">
 <div class="center">
  <img src="structIir_1_1ChebyshevI_1_1LowShelfBase.png" usemap="#Iir::ChebyshevI::LowShelfBase_map" alt=""/>
  <map id="Iir::ChebyshevI::LowShelfBase_map" name="Iir::ChebyshevI::LowShelfBase_map">
<area href="classIir_1_1PoleFilterBase.html" alt="Iir::PoleFilterBase&lt; AnalogLowShelf &gt;" shape="rect" coords="0,112,227,136"/>
<area href="classIir_1_1PoleFilterBase2.html" alt="Iir::PoleFilterBase2" shape="rect" coords="0,56,227,80"/>
<area href="classIir_1_1Cascade.html" alt="Iir::Cascade" shape="rect" coords="0,0,227,24"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="inherited"></a>
Additional Inherited Members</h2></td></tr>
<tr class="inherit_header pub_methods_classIir_1_1Cascade"><td colspan="2" onclick="javascript:toggleInherit('pub_methods_classIir_1_1Cascade')"><img src="closed.png" alt="-"/>&#160;Public Member Functions inherited from <a class="el" href="classIir_1_1Cascade.html">Iir::Cascade</a></td></tr>
<tr class="memitem:a9694b85c160e3689a4d71fd51ca6175d inherit pub_methods_classIir_1_1Cascade"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classIir_1_1Cascade.html#a9694b85c160e3689a4d71fd51ca6175d">getNumStages</a> () const</td></tr>
<tr class="separator:a9694b85c160e3689a4d71fd51ca6175d inherit pub_methods_classIir_1_1Cascade"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afc58e4b464b2cdef11e77b01e1a80668 inherit pub_methods_classIir_1_1Cascade"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="classIir_1_1Biquad.html">Biquad</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classIir_1_1Cascade.html#afc58e4b464b2cdef11e77b01e1a80668">operator[]</a> (int index)</td></tr>
<tr class="separator:afc58e4b464b2cdef11e77b01e1a80668 inherit pub_methods_classIir_1_1Cascade"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa76e09e3868829a80e954d0444390f90 inherit pub_methods_classIir_1_1Cascade"><td class="memItemLeft" align="right" valign="top">complex_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classIir_1_1Cascade.html#aa76e09e3868829a80e954d0444390f90">response</a> (double normalizedFrequency) const</td></tr>
<tr class="separator:aa76e09e3868829a80e954d0444390f90 inherit pub_methods_classIir_1_1Cascade"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a18df8bebec4a5e8e3ddc28c35b6bb2f8 inherit pub_methods_classIir_1_1Cascade"><td class="memItemLeft" align="right" valign="top">std::vector&lt; <a class="el" href="structIir_1_1PoleZeroPair.html">PoleZeroPair</a> &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classIir_1_1Cascade.html#a18df8bebec4a5e8e3ddc28c35b6bb2f8">getPoleZeros</a> () const</td></tr>
<tr class="separator:a18df8bebec4a5e8e3ddc28c35b6bb2f8 inherit pub_methods_classIir_1_1Cascade"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<hr/>The documentation for this struct was generated from the following files:<ul>
<li>iir/<a class="el" href="ChebyshevI_8h_source.html">ChebyshevI.h</a></li>
<li>iir/ChebyshevI.cpp</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Fri Jan 6 2023 23:53:39 for iir1 by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
