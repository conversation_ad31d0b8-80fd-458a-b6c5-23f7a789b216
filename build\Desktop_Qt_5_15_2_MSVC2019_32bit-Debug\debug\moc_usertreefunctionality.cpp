/****************************************************************************
** Meta object code from reading C++ file 'usertreefunctionality.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.15.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../SeqEditALL/UserTreeMouse/usertreefunctionality.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'usertreefunctionality.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.15.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_UserTreeFunctionality_t {
    QByteArrayData data[19];
    char stringdata0[300];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_UserTreeFunctionality_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_UserTreeFunctionality_t qt_meta_stringdata_UserTreeFunctionality = {
    {
QT_MOC_LITERAL(0, 0, 21), // "UserTreeFunctionality"
QT_MOC_LITERAL(1, 22, 15), // "SendTreeCharged"
QT_MOC_LITERAL(2, 38, 0), // ""
QT_MOC_LITERAL(3, 39, 15), // "showContextMenu"
QT_MOC_LITERAL(4, 55, 3), // "pos"
QT_MOC_LITERAL(5, 59, 15), // "onEditTriggered"
QT_MOC_LITERAL(6, 75, 24), // "onEnableDisableTriggered"
QT_MOC_LITERAL(7, 100, 17), // "onConfigTriggered"
QT_MOC_LITERAL(8, 118, 15), // "onCopyTriggered"
QT_MOC_LITERAL(9, 134, 16), // "onPasteTriggered"
QT_MOC_LITERAL(10, 151, 26), // "onEditNameCommentTriggered"
QT_MOC_LITERAL(11, 178, 17), // "onRemoveTriggered"
QT_MOC_LITERAL(12, 196, 16), // "onDialogAccepted"
QT_MOC_LITERAL(13, 213, 23), // "onItemCheckStateChanged"
QT_MOC_LITERAL(14, 237, 16), // "QTreeWidgetItem*"
QT_MOC_LITERAL(15, 254, 4), // "item"
QT_MOC_LITERAL(16, 259, 6), // "column"
QT_MOC_LITERAL(17, 266, 19), // "onItemDoubleClicked"
QT_MOC_LITERAL(18, 286, 13) // "onItemChanged"

    },
    "UserTreeFunctionality\0SendTreeCharged\0"
    "\0showContextMenu\0pos\0onEditTriggered\0"
    "onEnableDisableTriggered\0onConfigTriggered\0"
    "onCopyTriggered\0onPasteTriggered\0"
    "onEditNameCommentTriggered\0onRemoveTriggered\0"
    "onDialogAccepted\0onItemCheckStateChanged\0"
    "QTreeWidgetItem*\0item\0column\0"
    "onItemDoubleClicked\0onItemChanged"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_UserTreeFunctionality[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      13,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       1,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    0,   79,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       3,    1,   80,    2, 0x08 /* Private */,
       5,    0,   83,    2, 0x08 /* Private */,
       6,    0,   84,    2, 0x08 /* Private */,
       7,    0,   85,    2, 0x08 /* Private */,
       8,    0,   86,    2, 0x08 /* Private */,
       9,    0,   87,    2, 0x08 /* Private */,
      10,    0,   88,    2, 0x08 /* Private */,
      11,    0,   89,    2, 0x08 /* Private */,
      12,    0,   90,    2, 0x08 /* Private */,
      13,    2,   91,    2, 0x08 /* Private */,
      17,    2,   96,    2, 0x08 /* Private */,
      18,    2,  101,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void,

 // slots: parameters
    QMetaType::Void, QMetaType::QPoint,    4,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 14, QMetaType::Int,   15,   16,
    QMetaType::Void, 0x80000000 | 14, QMetaType::Int,   15,   16,
    QMetaType::Void, 0x80000000 | 14, QMetaType::Int,   15,   16,

       0        // eod
};

void UserTreeFunctionality::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<UserTreeFunctionality *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->SendTreeCharged(); break;
        case 1: _t->showContextMenu((*reinterpret_cast< const QPoint(*)>(_a[1]))); break;
        case 2: _t->onEditTriggered(); break;
        case 3: _t->onEnableDisableTriggered(); break;
        case 4: _t->onConfigTriggered(); break;
        case 5: _t->onCopyTriggered(); break;
        case 6: _t->onPasteTriggered(); break;
        case 7: _t->onEditNameCommentTriggered(); break;
        case 8: _t->onRemoveTriggered(); break;
        case 9: _t->onDialogAccepted(); break;
        case 10: _t->onItemCheckStateChanged((*reinterpret_cast< QTreeWidgetItem*(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 11: _t->onItemDoubleClicked((*reinterpret_cast< QTreeWidgetItem*(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 12: _t->onItemChanged((*reinterpret_cast< QTreeWidgetItem*(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (UserTreeFunctionality::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&UserTreeFunctionality::SendTreeCharged)) {
                *result = 0;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject UserTreeFunctionality::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_UserTreeFunctionality.data,
    qt_meta_data_UserTreeFunctionality,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *UserTreeFunctionality::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *UserTreeFunctionality::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_UserTreeFunctionality.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int UserTreeFunctionality::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 13)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 13;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 13)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 13;
    }
    return _id;
}

// SIGNAL 0
void UserTreeFunctionality::SendTreeCharged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
