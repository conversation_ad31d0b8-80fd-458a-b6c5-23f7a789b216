2025-08-09 17:53:36.213 [WARNING] QWidget::setLayout: Attempting to set QLayout "horizontalLayout" on ChanalSetForm "ChanalSetForm", which already has a layout (kernel\qwidget.cpp:9978)
2025-08-09 17:53:46.435 [WARNING] Config not found: "null" (C:\Users\<USER>\Desktop\aaa\LSSAudioProj\MainSet\signal\stim_signal_manager.cpp:134)
2025-08-09 17:54:32.413 [WARNING] QWindowsWindow::setGeometry: Unable to set geometry 120x30+449+442 (frame: 136x69+441+411) on QWidgetWindow/"QDialogClassWindow" on "\\.\DISPLAY1". Resulting geometry: 120x118+449+442 (frame: 136x157+441+411) margins: 8, 31, 8, 8 minimum size: 97x118 MINMAXINFO maxSize=0,0 maxpos=0,0 mintrack=113,157 maxtrack=0,0) (qwindowswindow.cpp:1891)
