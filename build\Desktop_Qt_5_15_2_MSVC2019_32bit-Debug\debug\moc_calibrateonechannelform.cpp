/****************************************************************************
** Meta object code from reading C++ file 'calibrateonechannelform.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.15.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../MainSet/channel/calibrateonechannelform.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'calibrateonechannelform.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.15.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_CalibrateOneChannelForm_t {
    QByteArrayData data[21];
    char stringdata0[295];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_CalibrateOneChannelForm_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_CalibrateOneChannelForm_t qt_meta_stringdata_CalibrateOneChannelForm = {
    {
QT_MOC_LITERAL(0, 0, 23), // "CalibrateOneChannelForm"
QT_MOC_LITERAL(1, 24, 10), // "SendResult"
QT_MOC_LITERAL(2, 35, 0), // ""
QT_MOC_LITERAL(3, 36, 11), // "ChannelMode"
QT_MOC_LITERAL(4, 48, 3), // "Mod"
QT_MOC_LITERAL(5, 52, 9), // "inchannel"
QT_MOC_LITERAL(6, 62, 10), // "outchannel"
QT_MOC_LITERAL(7, 73, 7), // "OutGain"
QT_MOC_LITERAL(8, 81, 6), // "InGain"
QT_MOC_LITERAL(9, 88, 34), // "on_MultimeterValue_editingFin..."
QT_MOC_LITERAL(10, 123, 22), // "on_StarStopBtn_clicked"
QT_MOC_LITERAL(11, 146, 18), // "on_SaveBtn_clicked"
QT_MOC_LITERAL(12, 165, 18), // "on_ExitBtn_clicked"
QT_MOC_LITERAL(13, 184, 22), // "handlePlaybackFinished"
QT_MOC_LITERAL(14, 207, 27), // "AudioProcessor::ChannelType"
QT_MOC_LITERAL(15, 235, 4), // "type"
QT_MOC_LITERAL(16, 240, 11), // "channelName"
QT_MOC_LITERAL(17, 252, 10), // "updatePlot"
QT_MOC_LITERAL(18, 263, 8), // "isOutput"
QT_MOC_LITERAL(19, 272, 7), // "isInput"
QT_MOC_LITERAL(20, 280, 14) // "isOutpuAndInpu"

    },
    "CalibrateOneChannelForm\0SendResult\0\0"
    "ChannelMode\0Mod\0inchannel\0outchannel\0"
    "OutGain\0InGain\0on_MultimeterValue_editingFinished\0"
    "on_StarStopBtn_clicked\0on_SaveBtn_clicked\0"
    "on_ExitBtn_clicked\0handlePlaybackFinished\0"
    "AudioProcessor::ChannelType\0type\0"
    "channelName\0updatePlot\0isOutput\0isInput\0"
    "isOutpuAndInpu"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_CalibrateOneChannelForm[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       7,   14, // methods
       0,    0, // properties
       1,   74, // enums/sets
       0,    0, // constructors
       0,       // flags
       1,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    5,   49,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       9,    0,   60,    2, 0x08 /* Private */,
      10,    0,   61,    2, 0x08 /* Private */,
      11,    0,   62,    2, 0x08 /* Private */,
      12,    0,   63,    2, 0x08 /* Private */,
      13,    2,   64,    2, 0x08 /* Private */,
      17,    2,   69,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, 0x80000000 | 3, QMetaType::QString, QMetaType::QString, QMetaType::Double, QMetaType::Double,    4,    5,    6,    7,    8,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 14, QMetaType::QString,   15,   16,
    QMetaType::Void, 0x80000000 | 14, QMetaType::QString,   15,   16,

 // enums: name, alias, flags, count, data
       3,    3, 0x2,    3,   79,

 // enum data: key, value
      18, uint(CalibrateOneChannelForm::ChannelMode::isOutput),
      19, uint(CalibrateOneChannelForm::ChannelMode::isInput),
      20, uint(CalibrateOneChannelForm::ChannelMode::isOutpuAndInpu),

       0        // eod
};

void CalibrateOneChannelForm::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<CalibrateOneChannelForm *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->SendResult((*reinterpret_cast< ChannelMode(*)>(_a[1])),(*reinterpret_cast< QString(*)>(_a[2])),(*reinterpret_cast< QString(*)>(_a[3])),(*reinterpret_cast< double(*)>(_a[4])),(*reinterpret_cast< double(*)>(_a[5]))); break;
        case 1: _t->on_MultimeterValue_editingFinished(); break;
        case 2: _t->on_StarStopBtn_clicked(); break;
        case 3: _t->on_SaveBtn_clicked(); break;
        case 4: _t->on_ExitBtn_clicked(); break;
        case 5: _t->handlePlaybackFinished((*reinterpret_cast< AudioProcessor::ChannelType(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 6: _t->updatePlot((*reinterpret_cast< AudioProcessor::ChannelType(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (CalibrateOneChannelForm::*)(ChannelMode , QString , QString , double , double );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&CalibrateOneChannelForm::SendResult)) {
                *result = 0;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject CalibrateOneChannelForm::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_meta_stringdata_CalibrateOneChannelForm.data,
    qt_meta_data_CalibrateOneChannelForm,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *CalibrateOneChannelForm::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *CalibrateOneChannelForm::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CalibrateOneChannelForm.stringdata0))
        return static_cast<void*>(this);
    if (!strcmp(_clname, "base_lib"))
        return static_cast< base_lib*>(this);
    return QWidget::qt_metacast(_clname);
}

int CalibrateOneChannelForm::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 7)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 7;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 7)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 7;
    }
    return _id;
}

// SIGNAL 0
void CalibrateOneChannelForm::SendResult(ChannelMode _t1, QString _t2, QString _t3, double _t4, double _t5)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t3))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t4))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t5))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
