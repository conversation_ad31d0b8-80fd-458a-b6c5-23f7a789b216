<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>iir1: iir/Custom.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">iir1
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_5231e4e0b49b19c482755f817f3ac47e.html">iir</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">Custom.h</div>  </div>
</div><!--header-->
<div class="contents">
<div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160; </div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;<span class="preprocessor">#ifndef IIR1_CUSTOM_H</span></div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;<span class="preprocessor">#define IIR1_CUSTOM_H</span></div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160; </div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;<span class="preprocessor">#include &quot;Common.h&quot;</span></div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;<span class="preprocessor">#include &quot;Biquad.h&quot;</span></div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;<span class="preprocessor">#include &quot;Cascade.h&quot;</span></div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;<span class="preprocessor">#include &quot;PoleFilter.h&quot;</span></div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;<span class="preprocessor">#include &quot;State.h&quot;</span></div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160; </div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160; </div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespaceIir.html">Iir</a> {</div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160; </div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;<span class="keyword">namespace </span>Custom {</div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160; </div>
<div class="line"><a name="l00061"></a><span class="lineno"><a class="line" href="structIir_1_1Custom_1_1OnePole.html">   61</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structIir_1_1Custom_1_1OnePole.html">OnePole</a> : <span class="keyword">public</span> <a class="code" href="classIir_1_1Biquad.html">Biquad</a></div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;{</div>
<div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;        <span class="keywordtype">void</span> setup (<span class="keywordtype">double</span> scale,</div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;                    <span class="keywordtype">double</span> pole,</div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;                    <span class="keywordtype">double</span> zero);</div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;};</div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160; </div>
<div class="line"><a name="l00075"></a><span class="lineno"><a class="line" href="structIir_1_1Custom_1_1TwoPole.html">   75</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structIir_1_1Custom_1_1TwoPole.html">TwoPole</a> : <span class="keyword">public</span> <a class="code" href="classIir_1_1Biquad.html">Biquad</a></div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;{</div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;        <span class="keywordtype">void</span> setup (<span class="keywordtype">double</span> scale,</div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;                    <span class="keywordtype">double</span> poleRho,</div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;                    <span class="keywordtype">double</span> poleTheta,</div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;                    <span class="keywordtype">double</span> zeroRho,</div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;                    <span class="keywordtype">double</span> zeroTheta);</div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;};</div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160; </div>
<div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">int</span> NSOS, <span class="keyword">class</span> StateType = DEFAULT_STATE&gt;</div>
<div class="line"><a name="l00090"></a><span class="lineno"><a class="line" href="structIir_1_1Custom_1_1SOSCascade.html">   90</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structIir_1_1Custom_1_1SOSCascade.html">SOSCascade</a> : <a class="code" href="classIir_1_1CascadeStages.html">CascadeStages</a>&lt;NSOS,StateType&gt;</div>
<div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;{</div>
<div class="line"><a name="l00096"></a><span class="lineno"><a class="line" href="structIir_1_1Custom_1_1SOSCascade.html#a04671c1c84cacaf1c822adeb850ccc63">   96</a></span>&#160;        <a class="code" href="structIir_1_1Custom_1_1SOSCascade.html#a04671c1c84cacaf1c822adeb850ccc63">SOSCascade</a>() = <span class="keywordflow">default</span>;</div>
<div class="line"><a name="l00107"></a><span class="lineno"><a class="line" href="structIir_1_1Custom_1_1SOSCascade.html#a60654fe4bd861799dff82a6dce9dfd42">  107</a></span>&#160;        <a class="code" href="structIir_1_1Custom_1_1SOSCascade.html#a60654fe4bd861799dff82a6dce9dfd42">SOSCascade</a>(<span class="keyword">const</span> <span class="keywordtype">double</span> (&amp;sosCoefficients)[NSOS][6]) {</div>
<div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;                <a class="code" href="classIir_1_1CascadeStages.html#a56d24da4cf09f898b534b1b05578244d">CascadeStages&lt;NSOS,StateType&gt;::setup</a>(sosCoefficients);</div>
<div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;        }</div>
<div class="line"><a name="l00120"></a><span class="lineno"><a class="line" href="structIir_1_1Custom_1_1SOSCascade.html#aae603eb1bd4330411a6003e09ba36efc">  120</a></span>&#160;        <span class="keywordtype">void</span> <a class="code" href="structIir_1_1Custom_1_1SOSCascade.html#aae603eb1bd4330411a6003e09ba36efc">setup</a> (<span class="keyword">const</span> <span class="keywordtype">double</span> (&amp;sosCoefficients)[NSOS][6]) {</div>
<div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;                <a class="code" href="classIir_1_1CascadeStages.html#a56d24da4cf09f898b534b1b05578244d">CascadeStages&lt;NSOS,StateType&gt;::setup</a>(sosCoefficients);</div>
<div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;        }</div>
<div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;};</div>
<div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160; </div>
<div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;}</div>
<div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160; </div>
<div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;}</div>
<div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160; </div>
<div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="ttc" id="aclassIir_1_1Biquad_html"><div class="ttname"><a href="classIir_1_1Biquad.html">Iir::Biquad</a></div><div class="ttdef"><b>Definition:</b> Biquad.h:52</div></div>
<div class="ttc" id="aclassIir_1_1CascadeStages_html"><div class="ttname"><a href="classIir_1_1CascadeStages.html">Iir::CascadeStages</a></div><div class="ttdef"><b>Definition:</b> Cascade.h:114</div></div>
<div class="ttc" id="aclassIir_1_1CascadeStages_html_a56d24da4cf09f898b534b1b05578244d"><div class="ttname"><a href="classIir_1_1CascadeStages.html#a56d24da4cf09f898b534b1b05578244d">Iir::CascadeStages::setup</a></div><div class="ttdeci">void setup(const double(&amp;sosCoefficients)[MaxStages][6])</div><div class="ttdef"><b>Definition:</b> Cascade.h:136</div></div>
<div class="ttc" id="anamespaceIir_html"><div class="ttname"><a href="namespaceIir.html">Iir</a></div><div class="ttdef"><b>Definition:</b> Biquad.cpp:40</div></div>
<div class="ttc" id="astructIir_1_1Custom_1_1OnePole_html"><div class="ttname"><a href="structIir_1_1Custom_1_1OnePole.html">Iir::Custom::OnePole</a></div><div class="ttdef"><b>Definition:</b> Custom.h:62</div></div>
<div class="ttc" id="astructIir_1_1Custom_1_1SOSCascade_html"><div class="ttname"><a href="structIir_1_1Custom_1_1SOSCascade.html">Iir::Custom::SOSCascade</a></div><div class="ttdef"><b>Definition:</b> Custom.h:91</div></div>
<div class="ttc" id="astructIir_1_1Custom_1_1SOSCascade_html_a04671c1c84cacaf1c822adeb850ccc63"><div class="ttname"><a href="structIir_1_1Custom_1_1SOSCascade.html#a04671c1c84cacaf1c822adeb850ccc63">Iir::Custom::SOSCascade::SOSCascade</a></div><div class="ttdeci">SOSCascade()=default</div></div>
<div class="ttc" id="astructIir_1_1Custom_1_1SOSCascade_html_a60654fe4bd861799dff82a6dce9dfd42"><div class="ttname"><a href="structIir_1_1Custom_1_1SOSCascade.html#a60654fe4bd861799dff82a6dce9dfd42">Iir::Custom::SOSCascade::SOSCascade</a></div><div class="ttdeci">SOSCascade(const double(&amp;sosCoefficients)[NSOS][6])</div><div class="ttdef"><b>Definition:</b> Custom.h:107</div></div>
<div class="ttc" id="astructIir_1_1Custom_1_1SOSCascade_html_aae603eb1bd4330411a6003e09ba36efc"><div class="ttname"><a href="structIir_1_1Custom_1_1SOSCascade.html#aae603eb1bd4330411a6003e09ba36efc">Iir::Custom::SOSCascade::setup</a></div><div class="ttdeci">void setup(const double(&amp;sosCoefficients)[NSOS][6])</div><div class="ttdef"><b>Definition:</b> Custom.h:120</div></div>
<div class="ttc" id="astructIir_1_1Custom_1_1TwoPole_html"><div class="ttname"><a href="structIir_1_1Custom_1_1TwoPole.html">Iir::Custom::TwoPole</a></div><div class="ttdef"><b>Definition:</b> Custom.h:76</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Fri Jan 6 2023 23:53:32 for iir1 by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
