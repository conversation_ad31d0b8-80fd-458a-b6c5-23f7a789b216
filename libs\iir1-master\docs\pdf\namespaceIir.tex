\hypertarget{namespaceIir}{}\doxysubsection{Iir Namespace Reference}
\label{namespaceIir}\index{Iir@{Iir}}
\doxysubsubsection*{Namespaces}
\begin{DoxyCompactItemize}
\item 
 \mbox{\hyperlink{namespaceIir_1_1B<PERSON>worth}{Butterworth}}
\item 
 \mbox{\hyperlink{namespaceIir_1_1ChebyshevI}{ChebyshevI}}
\item 
 \mbox{\hyperlink{namespaceIir_1_1ChebyshevII}{Chebyshev\+II}}
\item 
 \mbox{\hyperlink{namespaceIir_1_1Custom}{Custom}}
\end{DoxyCompactItemize}
\doxysubsubsection*{Classes}
\begin{DoxyCompactItemize}
\item 
class \mbox{\hyperlink{classIir_1_1Biquad}{Biquad}}
\item 
struct \mbox{\hyperlink{structIir_1_1BiquadPoleState}{Biquad\+Pole\+State}}
\item 
class \mbox{\hyperlink{classIir_1_1Cascade}{Cascade}}
\item 
class \mbox{\hyperlink{classIir_1_1CascadeStages}{Cascade\+Stages}}
\item 
class \mbox{\hyperlink{classIir_1_1LayoutBase}{Layout\+Base}}
\item 
class \mbox{\hyperlink{classIir_1_1Layout}{Layout}}
\item 
class \mbox{\hyperlink{classIir_1_1PoleFilterBase2}{Pole\+Filter\+Base2}}
\item 
class \mbox{\hyperlink{classIir_1_1PoleFilterBase}{Pole\+Filter\+Base}}
\item 
struct \mbox{\hyperlink{structIir_1_1PoleFilter}{Pole\+Filter}}
\item 
class \mbox{\hyperlink{classIir_1_1LowPassTransform}{Low\+Pass\+Transform}}
\item 
class \mbox{\hyperlink{classIir_1_1HighPassTransform}{High\+Pass\+Transform}}
\item 
class \mbox{\hyperlink{classIir_1_1BandPassTransform}{Band\+Pass\+Transform}}
\item 
class \mbox{\hyperlink{classIir_1_1BandStopTransform}{Band\+Stop\+Transform}}
\item 
class \mbox{\hyperlink{classIir_1_1DirectFormI}{Direct\+FormI}}
\item 
class \mbox{\hyperlink{classIir_1_1DirectFormII}{Direct\+Form\+II}}
\item 
class \mbox{\hyperlink{classIir_1_1TransposedDirectFormII}{Transposed\+Direct\+Form\+II}}
\item 
struct \mbox{\hyperlink{structIir_1_1ComplexPair}{Complex\+Pair}}
\item 
struct \mbox{\hyperlink{structIir_1_1PoleZeroPair}{Pole\+Zero\+Pair}}
\end{DoxyCompactItemize}


\doxysubsubsection{Detailed Description}
\char`\"{}\+A Collection of Useful C++ Classes for Digital Signal Processing\char`\"{} By Vinnie Falco and Bernd Porr

Official project location\+: \href{https://github.com/berndporr/iir1}{\texttt{ https\+://github.\+com/berndporr/iir1}}

See Documentation.\+cpp for contact information, notes, and bibliography.

\DoxyHorRuler{0}


License\+: MIT License (\href{http://www.opensource.org/licenses/mit-license.php}{\texttt{ http\+://www.\+opensource.\+org/licenses/mit-\/license.\+php}}) Copyright (c) 2009 by Vinnie Falco Copyright (c) 2011 by Bernd Porr

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \char`\"{}\+Software\char`\"{}), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions\+:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED \char`\"{}\+AS IS\char`\"{}, WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

\char`\"{}\+A Collection of Useful C++ Classes for Digital Signal Processing\char`\"{} By Vinnie Falco and Bernd Porr

Official project location\+: \href{https://github.com/berndporr/iir1}{\texttt{ https\+://github.\+com/berndporr/iir1}}

See Documentation.\+txt for contact information, notes, and bibliography.

\DoxyHorRuler{0}


License\+: MIT License (\href{http://www.opensource.org/licenses/mit-license.php}{\texttt{ http\+://www.\+opensource.\+org/licenses/mit-\/license.\+php}}) Copyright (c) 2009 by Vinnie Falco Copyright (c) 2011-\/2021 by Bernd Porr

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \char`\"{}\+Software\char`\"{}), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions\+:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED \char`\"{}\+AS IS\char`\"{}, WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

\char`\"{}\+A Collection of Useful C++ Classes for Digital Signal Processing\char`\"{} By Vinnie Falco and Bernd Porr

Official project location\+: \href{https://github.com/berndporr/iir1}{\texttt{ https\+://github.\+com/berndporr/iir1}}

See Documentation.\+cpp for contact information, notes, and bibliography.

\DoxyHorRuler{0}


License\+: MIT License (\href{http://www.opensource.org/licenses/mit-license.php}{\texttt{ http\+://www.\+opensource.\+org/licenses/mit-\/license.\+php}}) Copyright (c) 2009 by Vinnie Falco Copyright (c) 2011-\/2021 by Bernd Porr

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \char`\"{}\+Software\char`\"{}), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions\+:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED \char`\"{}\+AS IS\char`\"{}, WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

\char`\"{}\+A Collection of Useful C++ Classes for Digital Signal Processing\char`\"{} By Vinnie Falco and Bernd Porr

Official project location\+: \href{https://github.com/berndporr/iir1}{\texttt{ https\+://github.\+com/berndporr/iir1}}

See Documentation.\+txt for contact information, notes, and bibliography.

\DoxyHorRuler{0}


License\+: MIT License (\href{http://www.opensource.org/licenses/mit-license.php}{\texttt{ http\+://www.\+opensource.\+org/licenses/mit-\/license.\+php}}) Copyright (c) 2009 by Vinnie Falco Copyright (c) 2011 by Bernd Porr

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \char`\"{}\+Software\char`\"{}), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions\+:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED \char`\"{}\+AS IS\char`\"{}, WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

\char`\"{}\+A Collection of Useful C++ Classes for Digital Signal Processing\char`\"{} By Vinnie Falco and Bernd Porr

Official project location\+: \href{https://github.com/berndporr/iir1}{\texttt{ https\+://github.\+com/berndporr/iir1}}

See Documentation.\+cpp for contact information, notes, and bibliography.

\DoxyHorRuler{0}


License\+: MIT License (\href{http://www.opensource.org/licenses/mit-license.php}{\texttt{ http\+://www.\+opensource.\+org/licenses/mit-\/license.\+php}}) Copyright (c) 2009 by Vinnie Falco Copyright (c) 2011 by Bernd Porr

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \char`\"{}\+Software\char`\"{}), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions\+:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED \char`\"{}\+AS IS\char`\"{}, WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE. Describes a filter as a collection of poles and zeros along with normalization information to achieve a specified gain at a specified frequency. The poles and zeros may lie either in the s or the z plane. 