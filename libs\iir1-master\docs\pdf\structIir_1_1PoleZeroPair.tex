\hypertarget{structIir_1_1PoleZeroPair}{}\doxysubsection{Iir\+::Pole\+Zero\+Pair Struct Reference}
\label{structIir_1_1PoleZeroPair}\index{Iir::PoleZeroPair@{Iir::PoleZeroPair}}


{\ttfamily \#include $<$Types.\+h$>$}

Inheritance diagram for Iir\+::Pole\+Zero\+Pair\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=2.000000cm]{structIir_1_1PoleZeroPair}
\end{center}
\end{figure}


\doxysubsubsection{Detailed Description}
A pair of pole/zeros. This fits in a biquad (but is missing the gain) 

The documentation for this struct was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
iir/Types.\+h\end{DoxyCompactItemize}
