<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>iir1: iir/RBJ.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">iir1
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_5231e4e0b49b19c482755f817f3ac47e.html">iir</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">RBJ.h</div>  </div>
</div><!--header-->
<div class="contents">
<div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160; </div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;<span class="preprocessor">#ifndef IIR1_RBJ_H</span></div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;<span class="preprocessor">#define IIR1_RBJ_H</span></div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160; </div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;<span class="preprocessor">#include &quot;Common.h&quot;</span></div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;<span class="preprocessor">#include &quot;Biquad.h&quot;</span></div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;<span class="preprocessor">#include &quot;State.h&quot;</span></div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160; </div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespaceIir.html">Iir</a> {</div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160; </div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;<span class="preprocessor">#define ONESQRT2 (1/sqrt(2))</span></div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;        </div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;<span class="keyword">namespace </span>RBJ {</div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160; </div>
<div class="line"><a name="l00066"></a><span class="lineno"><a class="line" href="structIir_1_1RBJ_1_1RBJbase.html">   66</a></span>&#160;        <span class="keyword">struct </span>IIR_EXPORT <a class="code" href="structIir_1_1RBJ_1_1RBJbase.html">RBJbase</a> : <a class="code" href="classIir_1_1Biquad.html">Biquad</a></div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;        {</div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;        <span class="keyword">public</span>:</div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;                <span class="keyword">template</span> &lt;<span class="keyword">typename</span> Sample&gt;</div>
<div class="line"><a name="l00071"></a><span class="lineno"><a class="line" href="structIir_1_1RBJ_1_1RBJbase.html#a5dd179c8491e29966d5c33036cabe151">   71</a></span>&#160;                        <span class="keyword">inline</span> Sample <a class="code" href="structIir_1_1RBJ_1_1RBJbase.html#a5dd179c8491e29966d5c33036cabe151">filter</a>(Sample s) {</div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;                        <span class="keywordflow">return</span> <span class="keyword">static_cast&lt;</span>Sample<span class="keyword">&gt;</span>(state.filter((<span class="keywordtype">double</span>)s,*<span class="keyword">this</span>));</div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;                }</div>
<div class="line"><a name="l00075"></a><span class="lineno"><a class="line" href="structIir_1_1RBJ_1_1RBJbase.html#a0bd9a0b60e60b9326a14e43acbb430ab">   75</a></span>&#160;                <span class="keywordtype">void</span> <a class="code" href="structIir_1_1RBJ_1_1RBJbase.html#a0bd9a0b60e60b9326a14e43acbb430ab">reset</a>() {</div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;                        state.reset();</div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;                }</div>
<div class="line"><a name="l00079"></a><span class="lineno"><a class="line" href="structIir_1_1RBJ_1_1RBJbase.html#a8093409edfce007a4972fa2992d69670">   79</a></span>&#160;                <span class="keyword">const</span> <a class="code" href="classIir_1_1DirectFormI.html">DirectFormI</a>&amp; <a class="code" href="structIir_1_1RBJ_1_1RBJbase.html#a8093409edfce007a4972fa2992d69670">getState</a>() {</div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;                        <span class="keywordflow">return</span> state;</div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;                }</div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;        <span class="keyword">private</span>:</div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;                <a class="code" href="classIir_1_1DirectFormI.html">DirectFormI</a> state = {};</div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;        };</div>
<div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160; </div>
<div class="line"><a name="l00089"></a><span class="lineno"><a class="line" href="structIir_1_1RBJ_1_1LowPass.html">   89</a></span>&#160;        <span class="keyword">struct </span>IIR_EXPORT <a class="code" href="structIir_1_1RBJ_1_1LowPass.html">LowPass</a> : <a class="code" href="structIir_1_1RBJ_1_1RBJbase.html">RBJbase</a></div>
<div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;        {</div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;                <span class="keywordtype">void</span> setupN(<span class="keywordtype">double</span> cutoffFrequency,</div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;                            <span class="keywordtype">double</span> q = ONESQRT2);</div>
<div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;                </div>
<div class="line"><a name="l00105"></a><span class="lineno"><a class="line" href="structIir_1_1RBJ_1_1LowPass.html#adc6ffd4bab0cdc4f665f667841b96cea">  105</a></span>&#160;                <span class="keywordtype">void</span> <a class="code" href="structIir_1_1RBJ_1_1LowPass.html#adc6ffd4bab0cdc4f665f667841b96cea">setup</a>(<span class="keywordtype">double</span> sampleRate,</div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;                           <span class="keywordtype">double</span> cutoffFrequency,</div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;                           <span class="keywordtype">double</span> q = ONESQRT2) {</div>
<div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;                        setupN(cutoffFrequency / sampleRate, q);</div>
<div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;                }</div>
<div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;        };</div>
<div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160; </div>
<div class="line"><a name="l00115"></a><span class="lineno"><a class="line" href="structIir_1_1RBJ_1_1HighPass.html">  115</a></span>&#160;        <span class="keyword">struct </span>IIR_EXPORT <a class="code" href="structIir_1_1RBJ_1_1HighPass.html">HighPass</a> : <a class="code" href="structIir_1_1RBJ_1_1RBJbase.html">RBJbase</a></div>
<div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;        {</div>
<div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;                <span class="keywordtype">void</span> setupN(<span class="keywordtype">double</span> cutoffFrequency,</div>
<div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;                            <span class="keywordtype">double</span> q = ONESQRT2);</div>
<div class="line"><a name="l00130"></a><span class="lineno"><a class="line" href="structIir_1_1RBJ_1_1HighPass.html#a445c9bba06d82c972e389fbae45962e9">  130</a></span>&#160;                <span class="keywordtype">void</span> <a class="code" href="structIir_1_1RBJ_1_1HighPass.html#a445c9bba06d82c972e389fbae45962e9">setup</a> (<span class="keywordtype">double</span> sampleRate,</div>
<div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;                            <span class="keywordtype">double</span> cutoffFrequency,</div>
<div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;                            <span class="keywordtype">double</span> q = ONESQRT2) {</div>
<div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;                        setupN(cutoffFrequency / sampleRate, q);</div>
<div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;                }</div>
<div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;        };</div>
<div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160; </div>
<div class="line"><a name="l00140"></a><span class="lineno"><a class="line" href="structIir_1_1RBJ_1_1BandPass1.html">  140</a></span>&#160;        <span class="keyword">struct </span>IIR_EXPORT <a class="code" href="structIir_1_1RBJ_1_1BandPass1.html">BandPass1</a> : <a class="code" href="structIir_1_1RBJ_1_1RBJbase.html">RBJbase</a></div>
<div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;        {</div>
<div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;                <span class="keywordtype">void</span> setupN(<span class="keywordtype">double</span> centerFrequency,</div>
<div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;                            <span class="keywordtype">double</span> bandWidth);</div>
<div class="line"><a name="l00155"></a><span class="lineno"><a class="line" href="structIir_1_1RBJ_1_1BandPass1.html#a82ba843c68435a5a09cc16fdbf358afc">  155</a></span>&#160;                <span class="keywordtype">void</span> <a class="code" href="structIir_1_1RBJ_1_1BandPass1.html#a82ba843c68435a5a09cc16fdbf358afc">setup</a> (<span class="keywordtype">double</span> sampleRate,</div>
<div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;                            <span class="keywordtype">double</span> centerFrequency,</div>
<div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;                            <span class="keywordtype">double</span> bandWidth) {</div>
<div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;                        setupN(centerFrequency / sampleRate, bandWidth);</div>
<div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;                }</div>
<div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;        };</div>
<div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160; </div>
<div class="line"><a name="l00165"></a><span class="lineno"><a class="line" href="structIir_1_1RBJ_1_1BandPass2.html">  165</a></span>&#160;        <span class="keyword">struct </span>IIR_EXPORT <a class="code" href="structIir_1_1RBJ_1_1BandPass2.html">BandPass2</a> : <a class="code" href="structIir_1_1RBJ_1_1RBJbase.html">RBJbase</a></div>
<div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;        {</div>
<div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;                <span class="keywordtype">void</span> setupN(<span class="keywordtype">double</span> centerFrequency,</div>
<div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;                            <span class="keywordtype">double</span> bandWidth);</div>
<div class="line"><a name="l00180"></a><span class="lineno"><a class="line" href="structIir_1_1RBJ_1_1BandPass2.html#a2173d305036d1e9d842c249aa34e4cde">  180</a></span>&#160;                <span class="keywordtype">void</span> <a class="code" href="structIir_1_1RBJ_1_1BandPass2.html#a2173d305036d1e9d842c249aa34e4cde">setup</a> (<span class="keywordtype">double</span> sampleRate,</div>
<div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;                            <span class="keywordtype">double</span> centerFrequency,</div>
<div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;                            <span class="keywordtype">double</span> bandWidth) {</div>
<div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;                        setupN(centerFrequency / sampleRate, bandWidth);</div>
<div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;                }</div>
<div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;        };</div>
<div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160; </div>
<div class="line"><a name="l00191"></a><span class="lineno"><a class="line" href="structIir_1_1RBJ_1_1BandStop.html">  191</a></span>&#160;        <span class="keyword">struct </span>IIR_EXPORT <a class="code" href="structIir_1_1RBJ_1_1BandStop.html">BandStop</a> : <a class="code" href="structIir_1_1RBJ_1_1RBJbase.html">RBJbase</a></div>
<div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;        {</div>
<div class="line"><a name="l00198"></a><span class="lineno">  198</span>&#160;                <span class="keywordtype">void</span> setupN(<span class="keywordtype">double</span> centerFrequency,</div>
<div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160;                            <span class="keywordtype">double</span> bandWidth);</div>
<div class="line"><a name="l00206"></a><span class="lineno"><a class="line" href="structIir_1_1RBJ_1_1BandStop.html#afe29086ad05ecfe2c0697a09bfa45c49">  206</a></span>&#160;                <span class="keywordtype">void</span> <a class="code" href="structIir_1_1RBJ_1_1BandStop.html#afe29086ad05ecfe2c0697a09bfa45c49">setup</a> (<span class="keywordtype">double</span> sampleRate,</div>
<div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160;                            <span class="keywordtype">double</span> centerFrequency,</div>
<div class="line"><a name="l00208"></a><span class="lineno">  208</span>&#160;                            <span class="keywordtype">double</span> bandWidth) {</div>
<div class="line"><a name="l00209"></a><span class="lineno">  209</span>&#160;                        setupN(centerFrequency / sampleRate, bandWidth);</div>
<div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160;                }</div>
<div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;        };</div>
<div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160; </div>
<div class="line"><a name="l00224"></a><span class="lineno"><a class="line" href="structIir_1_1RBJ_1_1IIRNotch.html">  224</a></span>&#160;        <span class="keyword">struct </span>IIR_EXPORT <a class="code" href="structIir_1_1RBJ_1_1IIRNotch.html">IIRNotch</a> : <a class="code" href="structIir_1_1RBJ_1_1RBJbase.html">RBJbase</a></div>
<div class="line"><a name="l00225"></a><span class="lineno">  225</span>&#160;        {</div>
<div class="line"><a name="l00231"></a><span class="lineno">  231</span>&#160;                <span class="keywordtype">void</span> setupN(<span class="keywordtype">double</span> centerFrequency,</div>
<div class="line"><a name="l00232"></a><span class="lineno">  232</span>&#160;                            <span class="keywordtype">double</span> q_factor = 10);</div>
<div class="line"><a name="l00239"></a><span class="lineno"><a class="line" href="structIir_1_1RBJ_1_1IIRNotch.html#aedecb6358bc0a907742c56d5235d35df">  239</a></span>&#160;                <span class="keywordtype">void</span> <a class="code" href="structIir_1_1RBJ_1_1IIRNotch.html#aedecb6358bc0a907742c56d5235d35df">setup</a> (<span class="keywordtype">double</span> sampleRate,</div>
<div class="line"><a name="l00240"></a><span class="lineno">  240</span>&#160;                            <span class="keywordtype">double</span> centerFrequency,</div>
<div class="line"><a name="l00241"></a><span class="lineno">  241</span>&#160;                            <span class="keywordtype">double</span> q_factor = 10) {</div>
<div class="line"><a name="l00242"></a><span class="lineno">  242</span>&#160;                        setupN(centerFrequency / sampleRate, q_factor);</div>
<div class="line"><a name="l00243"></a><span class="lineno">  243</span>&#160;                }</div>
<div class="line"><a name="l00244"></a><span class="lineno">  244</span>&#160;        };</div>
<div class="line"><a name="l00245"></a><span class="lineno">  245</span>&#160; </div>
<div class="line"><a name="l00249"></a><span class="lineno"><a class="line" href="structIir_1_1RBJ_1_1LowShelf.html">  249</a></span>&#160;        <span class="keyword">struct </span>IIR_EXPORT <a class="code" href="structIir_1_1RBJ_1_1LowShelf.html">LowShelf</a> : <a class="code" href="structIir_1_1RBJ_1_1RBJbase.html">RBJbase</a></div>
<div class="line"><a name="l00250"></a><span class="lineno">  250</span>&#160;        {</div>
<div class="line"><a name="l00257"></a><span class="lineno">  257</span>&#160;                <span class="keywordtype">void</span> setupN(<span class="keywordtype">double</span> cutoffFrequency,</div>
<div class="line"><a name="l00258"></a><span class="lineno">  258</span>&#160;                            <span class="keywordtype">double</span> gainDb,</div>
<div class="line"><a name="l00259"></a><span class="lineno">  259</span>&#160;                            <span class="keywordtype">double</span> shelfSlope = 1);</div>
<div class="line"><a name="l00267"></a><span class="lineno"><a class="line" href="structIir_1_1RBJ_1_1LowShelf.html#a9bac813cc8399f3c274d9d10dc3a5a1e">  267</a></span>&#160;                <span class="keywordtype">void</span> <a class="code" href="structIir_1_1RBJ_1_1LowShelf.html#a9bac813cc8399f3c274d9d10dc3a5a1e">setup</a> (<span class="keywordtype">double</span> sampleRate,</div>
<div class="line"><a name="l00268"></a><span class="lineno">  268</span>&#160;                            <span class="keywordtype">double</span> cutoffFrequency,</div>
<div class="line"><a name="l00269"></a><span class="lineno">  269</span>&#160;                            <span class="keywordtype">double</span> gainDb,</div>
<div class="line"><a name="l00270"></a><span class="lineno">  270</span>&#160;                            <span class="keywordtype">double</span> shelfSlope = 1) {</div>
<div class="line"><a name="l00271"></a><span class="lineno">  271</span>&#160;                        setupN( cutoffFrequency / sampleRate, gainDb, shelfSlope);</div>
<div class="line"><a name="l00272"></a><span class="lineno">  272</span>&#160;                }</div>
<div class="line"><a name="l00273"></a><span class="lineno">  273</span>&#160;        };</div>
<div class="line"><a name="l00274"></a><span class="lineno">  274</span>&#160; </div>
<div class="line"><a name="l00278"></a><span class="lineno"><a class="line" href="structIir_1_1RBJ_1_1HighShelf.html">  278</a></span>&#160;        <span class="keyword">struct </span>IIR_EXPORT <a class="code" href="structIir_1_1RBJ_1_1HighShelf.html">HighShelf</a> : <a class="code" href="structIir_1_1RBJ_1_1RBJbase.html">RBJbase</a></div>
<div class="line"><a name="l00279"></a><span class="lineno">  279</span>&#160;        {</div>
<div class="line"><a name="l00286"></a><span class="lineno">  286</span>&#160;                <span class="keywordtype">void</span> setupN(<span class="keywordtype">double</span> cutoffFrequency,</div>
<div class="line"><a name="l00287"></a><span class="lineno">  287</span>&#160;                            <span class="keywordtype">double</span> gainDb,</div>
<div class="line"><a name="l00288"></a><span class="lineno">  288</span>&#160;                            <span class="keywordtype">double</span> shelfSlope = 1);</div>
<div class="line"><a name="l00296"></a><span class="lineno"><a class="line" href="structIir_1_1RBJ_1_1HighShelf.html#a4887616ad520c27fce55d76d915d5bf6">  296</a></span>&#160;                <span class="keywordtype">void</span> <a class="code" href="structIir_1_1RBJ_1_1HighShelf.html#a4887616ad520c27fce55d76d915d5bf6">setup</a> (<span class="keywordtype">double</span> sampleRate,</div>
<div class="line"><a name="l00297"></a><span class="lineno">  297</span>&#160;                            <span class="keywordtype">double</span> cutoffFrequency,</div>
<div class="line"><a name="l00298"></a><span class="lineno">  298</span>&#160;                            <span class="keywordtype">double</span> gainDb,</div>
<div class="line"><a name="l00299"></a><span class="lineno">  299</span>&#160;                            <span class="keywordtype">double</span> shelfSlope = 1) {</div>
<div class="line"><a name="l00300"></a><span class="lineno">  300</span>&#160;                        setupN( cutoffFrequency / sampleRate, gainDb, shelfSlope);</div>
<div class="line"><a name="l00301"></a><span class="lineno">  301</span>&#160;                }</div>
<div class="line"><a name="l00302"></a><span class="lineno">  302</span>&#160;        };</div>
<div class="line"><a name="l00303"></a><span class="lineno">  303</span>&#160; </div>
<div class="line"><a name="l00307"></a><span class="lineno"><a class="line" href="structIir_1_1RBJ_1_1BandShelf.html">  307</a></span>&#160;        <span class="keyword">struct </span>IIR_EXPORT <a class="code" href="structIir_1_1RBJ_1_1BandShelf.html">BandShelf</a> : <a class="code" href="structIir_1_1RBJ_1_1RBJbase.html">RBJbase</a></div>
<div class="line"><a name="l00308"></a><span class="lineno">  308</span>&#160;        {</div>
<div class="line"><a name="l00315"></a><span class="lineno">  315</span>&#160;                <span class="keywordtype">void</span> setupN(<span class="keywordtype">double</span> centerFrequency,</div>
<div class="line"><a name="l00316"></a><span class="lineno">  316</span>&#160;                            <span class="keywordtype">double</span> gainDb,</div>
<div class="line"><a name="l00317"></a><span class="lineno">  317</span>&#160;                            <span class="keywordtype">double</span> bandWidth);</div>
<div class="line"><a name="l00325"></a><span class="lineno"><a class="line" href="structIir_1_1RBJ_1_1BandShelf.html#ae4b2c50b6cf6715d881f9c3681530870">  325</a></span>&#160;                <span class="keywordtype">void</span> <a class="code" href="structIir_1_1RBJ_1_1BandShelf.html#ae4b2c50b6cf6715d881f9c3681530870">setup</a> (<span class="keywordtype">double</span> sampleRate,</div>
<div class="line"><a name="l00326"></a><span class="lineno">  326</span>&#160;                            <span class="keywordtype">double</span> centerFrequency,</div>
<div class="line"><a name="l00327"></a><span class="lineno">  327</span>&#160;                            <span class="keywordtype">double</span> gainDb,</div>
<div class="line"><a name="l00328"></a><span class="lineno">  328</span>&#160;                            <span class="keywordtype">double</span> bandWidth) {</div>
<div class="line"><a name="l00329"></a><span class="lineno">  329</span>&#160;                        setupN(centerFrequency / sampleRate, gainDb, bandWidth);</div>
<div class="line"><a name="l00330"></a><span class="lineno">  330</span>&#160;                }</div>
<div class="line"><a name="l00331"></a><span class="lineno">  331</span>&#160;        };</div>
<div class="line"><a name="l00332"></a><span class="lineno">  332</span>&#160; </div>
<div class="line"><a name="l00336"></a><span class="lineno"><a class="line" href="structIir_1_1RBJ_1_1AllPass.html">  336</a></span>&#160;        <span class="keyword">struct </span>IIR_EXPORT <a class="code" href="structIir_1_1RBJ_1_1AllPass.html">AllPass</a> : <a class="code" href="structIir_1_1RBJ_1_1RBJbase.html">RBJbase</a></div>
<div class="line"><a name="l00337"></a><span class="lineno">  337</span>&#160;        {</div>
<div class="line"><a name="l00343"></a><span class="lineno">  343</span>&#160;                <span class="keywordtype">void</span> setupN(<span class="keywordtype">double</span> phaseFrequency,</div>
<div class="line"><a name="l00344"></a><span class="lineno">  344</span>&#160;                            <span class="keywordtype">double</span> q  = ONESQRT2);</div>
<div class="line"><a name="l00345"></a><span class="lineno">  345</span>&#160;                </div>
<div class="line"><a name="l00352"></a><span class="lineno"><a class="line" href="structIir_1_1RBJ_1_1AllPass.html#ae68e5b4a22b3256b52798cddc3ff653c">  352</a></span>&#160;                <span class="keywordtype">void</span> <a class="code" href="structIir_1_1RBJ_1_1AllPass.html#ae68e5b4a22b3256b52798cddc3ff653c">setup</a> (<span class="keywordtype">double</span> sampleRate,</div>
<div class="line"><a name="l00353"></a><span class="lineno">  353</span>&#160;                            <span class="keywordtype">double</span> phaseFrequency,</div>
<div class="line"><a name="l00354"></a><span class="lineno">  354</span>&#160;                            <span class="keywordtype">double</span> q  = ONESQRT2) {</div>
<div class="line"><a name="l00355"></a><span class="lineno">  355</span>&#160;                        setupN( phaseFrequency / sampleRate, q);</div>
<div class="line"><a name="l00356"></a><span class="lineno">  356</span>&#160;                }</div>
<div class="line"><a name="l00357"></a><span class="lineno">  357</span>&#160;        };</div>
<div class="line"><a name="l00358"></a><span class="lineno">  358</span>&#160;        </div>
<div class="line"><a name="l00359"></a><span class="lineno">  359</span>&#160;}</div>
<div class="line"><a name="l00360"></a><span class="lineno">  360</span>&#160; </div>
<div class="line"><a name="l00361"></a><span class="lineno">  361</span>&#160;}</div>
<div class="line"><a name="l00362"></a><span class="lineno">  362</span>&#160; </div>
<div class="line"><a name="l00363"></a><span class="lineno">  363</span>&#160; </div>
<div class="line"><a name="l00364"></a><span class="lineno">  364</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="ttc" id="aclassIir_1_1Biquad_html"><div class="ttname"><a href="classIir_1_1Biquad.html">Iir::Biquad</a></div><div class="ttdef"><b>Definition:</b> Biquad.h:52</div></div>
<div class="ttc" id="aclassIir_1_1DirectFormI_html"><div class="ttname"><a href="classIir_1_1DirectFormI.html">Iir::DirectFormI</a></div><div class="ttdef"><b>Definition:</b> State.h:56</div></div>
<div class="ttc" id="anamespaceIir_html"><div class="ttname"><a href="namespaceIir.html">Iir</a></div><div class="ttdef"><b>Definition:</b> Biquad.cpp:40</div></div>
<div class="ttc" id="astructIir_1_1RBJ_1_1AllPass_html"><div class="ttname"><a href="structIir_1_1RBJ_1_1AllPass.html">Iir::RBJ::AllPass</a></div><div class="ttdef"><b>Definition:</b> RBJ.h:337</div></div>
<div class="ttc" id="astructIir_1_1RBJ_1_1AllPass_html_ae68e5b4a22b3256b52798cddc3ff653c"><div class="ttname"><a href="structIir_1_1RBJ_1_1AllPass.html#ae68e5b4a22b3256b52798cddc3ff653c">Iir::RBJ::AllPass::setup</a></div><div class="ttdeci">void setup(double sampleRate, double phaseFrequency, double q=(1/sqrt(2)))</div><div class="ttdef"><b>Definition:</b> RBJ.h:352</div></div>
<div class="ttc" id="astructIir_1_1RBJ_1_1BandPass1_html"><div class="ttname"><a href="structIir_1_1RBJ_1_1BandPass1.html">Iir::RBJ::BandPass1</a></div><div class="ttdef"><b>Definition:</b> RBJ.h:141</div></div>
<div class="ttc" id="astructIir_1_1RBJ_1_1BandPass1_html_a82ba843c68435a5a09cc16fdbf358afc"><div class="ttname"><a href="structIir_1_1RBJ_1_1BandPass1.html#a82ba843c68435a5a09cc16fdbf358afc">Iir::RBJ::BandPass1::setup</a></div><div class="ttdeci">void setup(double sampleRate, double centerFrequency, double bandWidth)</div><div class="ttdef"><b>Definition:</b> RBJ.h:155</div></div>
<div class="ttc" id="astructIir_1_1RBJ_1_1BandPass2_html"><div class="ttname"><a href="structIir_1_1RBJ_1_1BandPass2.html">Iir::RBJ::BandPass2</a></div><div class="ttdef"><b>Definition:</b> RBJ.h:166</div></div>
<div class="ttc" id="astructIir_1_1RBJ_1_1BandPass2_html_a2173d305036d1e9d842c249aa34e4cde"><div class="ttname"><a href="structIir_1_1RBJ_1_1BandPass2.html#a2173d305036d1e9d842c249aa34e4cde">Iir::RBJ::BandPass2::setup</a></div><div class="ttdeci">void setup(double sampleRate, double centerFrequency, double bandWidth)</div><div class="ttdef"><b>Definition:</b> RBJ.h:180</div></div>
<div class="ttc" id="astructIir_1_1RBJ_1_1BandShelf_html"><div class="ttname"><a href="structIir_1_1RBJ_1_1BandShelf.html">Iir::RBJ::BandShelf</a></div><div class="ttdef"><b>Definition:</b> RBJ.h:308</div></div>
<div class="ttc" id="astructIir_1_1RBJ_1_1BandShelf_html_ae4b2c50b6cf6715d881f9c3681530870"><div class="ttname"><a href="structIir_1_1RBJ_1_1BandShelf.html#ae4b2c50b6cf6715d881f9c3681530870">Iir::RBJ::BandShelf::setup</a></div><div class="ttdeci">void setup(double sampleRate, double centerFrequency, double gainDb, double bandWidth)</div><div class="ttdef"><b>Definition:</b> RBJ.h:325</div></div>
<div class="ttc" id="astructIir_1_1RBJ_1_1BandStop_html"><div class="ttname"><a href="structIir_1_1RBJ_1_1BandStop.html">Iir::RBJ::BandStop</a></div><div class="ttdef"><b>Definition:</b> RBJ.h:192</div></div>
<div class="ttc" id="astructIir_1_1RBJ_1_1BandStop_html_afe29086ad05ecfe2c0697a09bfa45c49"><div class="ttname"><a href="structIir_1_1RBJ_1_1BandStop.html#afe29086ad05ecfe2c0697a09bfa45c49">Iir::RBJ::BandStop::setup</a></div><div class="ttdeci">void setup(double sampleRate, double centerFrequency, double bandWidth)</div><div class="ttdef"><b>Definition:</b> RBJ.h:206</div></div>
<div class="ttc" id="astructIir_1_1RBJ_1_1HighPass_html"><div class="ttname"><a href="structIir_1_1RBJ_1_1HighPass.html">Iir::RBJ::HighPass</a></div><div class="ttdef"><b>Definition:</b> RBJ.h:116</div></div>
<div class="ttc" id="astructIir_1_1RBJ_1_1HighPass_html_a445c9bba06d82c972e389fbae45962e9"><div class="ttname"><a href="structIir_1_1RBJ_1_1HighPass.html#a445c9bba06d82c972e389fbae45962e9">Iir::RBJ::HighPass::setup</a></div><div class="ttdeci">void setup(double sampleRate, double cutoffFrequency, double q=(1/sqrt(2)))</div><div class="ttdef"><b>Definition:</b> RBJ.h:130</div></div>
<div class="ttc" id="astructIir_1_1RBJ_1_1HighShelf_html"><div class="ttname"><a href="structIir_1_1RBJ_1_1HighShelf.html">Iir::RBJ::HighShelf</a></div><div class="ttdef"><b>Definition:</b> RBJ.h:279</div></div>
<div class="ttc" id="astructIir_1_1RBJ_1_1HighShelf_html_a4887616ad520c27fce55d76d915d5bf6"><div class="ttname"><a href="structIir_1_1RBJ_1_1HighShelf.html#a4887616ad520c27fce55d76d915d5bf6">Iir::RBJ::HighShelf::setup</a></div><div class="ttdeci">void setup(double sampleRate, double cutoffFrequency, double gainDb, double shelfSlope=1)</div><div class="ttdef"><b>Definition:</b> RBJ.h:296</div></div>
<div class="ttc" id="astructIir_1_1RBJ_1_1IIRNotch_html"><div class="ttname"><a href="structIir_1_1RBJ_1_1IIRNotch.html">Iir::RBJ::IIRNotch</a></div><div class="ttdef"><b>Definition:</b> RBJ.h:225</div></div>
<div class="ttc" id="astructIir_1_1RBJ_1_1IIRNotch_html_aedecb6358bc0a907742c56d5235d35df"><div class="ttname"><a href="structIir_1_1RBJ_1_1IIRNotch.html#aedecb6358bc0a907742c56d5235d35df">Iir::RBJ::IIRNotch::setup</a></div><div class="ttdeci">void setup(double sampleRate, double centerFrequency, double q_factor=10)</div><div class="ttdef"><b>Definition:</b> RBJ.h:239</div></div>
<div class="ttc" id="astructIir_1_1RBJ_1_1LowPass_html"><div class="ttname"><a href="structIir_1_1RBJ_1_1LowPass.html">Iir::RBJ::LowPass</a></div><div class="ttdef"><b>Definition:</b> RBJ.h:90</div></div>
<div class="ttc" id="astructIir_1_1RBJ_1_1LowPass_html_adc6ffd4bab0cdc4f665f667841b96cea"><div class="ttname"><a href="structIir_1_1RBJ_1_1LowPass.html#adc6ffd4bab0cdc4f665f667841b96cea">Iir::RBJ::LowPass::setup</a></div><div class="ttdeci">void setup(double sampleRate, double cutoffFrequency, double q=(1/sqrt(2)))</div><div class="ttdef"><b>Definition:</b> RBJ.h:105</div></div>
<div class="ttc" id="astructIir_1_1RBJ_1_1LowShelf_html"><div class="ttname"><a href="structIir_1_1RBJ_1_1LowShelf.html">Iir::RBJ::LowShelf</a></div><div class="ttdef"><b>Definition:</b> RBJ.h:250</div></div>
<div class="ttc" id="astructIir_1_1RBJ_1_1LowShelf_html_a9bac813cc8399f3c274d9d10dc3a5a1e"><div class="ttname"><a href="structIir_1_1RBJ_1_1LowShelf.html#a9bac813cc8399f3c274d9d10dc3a5a1e">Iir::RBJ::LowShelf::setup</a></div><div class="ttdeci">void setup(double sampleRate, double cutoffFrequency, double gainDb, double shelfSlope=1)</div><div class="ttdef"><b>Definition:</b> RBJ.h:267</div></div>
<div class="ttc" id="astructIir_1_1RBJ_1_1RBJbase_html"><div class="ttname"><a href="structIir_1_1RBJ_1_1RBJbase.html">Iir::RBJ::RBJbase</a></div><div class="ttdef"><b>Definition:</b> RBJ.h:67</div></div>
<div class="ttc" id="astructIir_1_1RBJ_1_1RBJbase_html_a0bd9a0b60e60b9326a14e43acbb430ab"><div class="ttname"><a href="structIir_1_1RBJ_1_1RBJbase.html#a0bd9a0b60e60b9326a14e43acbb430ab">Iir::RBJ::RBJbase::reset</a></div><div class="ttdeci">void reset()</div><div class="ttdoc">resets the delay lines to zero</div><div class="ttdef"><b>Definition:</b> RBJ.h:75</div></div>
<div class="ttc" id="astructIir_1_1RBJ_1_1RBJbase_html_a5dd179c8491e29966d5c33036cabe151"><div class="ttname"><a href="structIir_1_1RBJ_1_1RBJbase.html#a5dd179c8491e29966d5c33036cabe151">Iir::RBJ::RBJbase::filter</a></div><div class="ttdeci">Sample filter(Sample s)</div><div class="ttdoc">filter operation</div><div class="ttdef"><b>Definition:</b> RBJ.h:71</div></div>
<div class="ttc" id="astructIir_1_1RBJ_1_1RBJbase_html_a8093409edfce007a4972fa2992d69670"><div class="ttname"><a href="structIir_1_1RBJ_1_1RBJbase.html#a8093409edfce007a4972fa2992d69670">Iir::RBJ::RBJbase::getState</a></div><div class="ttdeci">const DirectFormI &amp; getState()</div><div class="ttdoc">gets the delay lines (=state) of the filter</div><div class="ttdef"><b>Definition:</b> RBJ.h:79</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Fri Jan 6 2023 23:53:32 for iir1 by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
