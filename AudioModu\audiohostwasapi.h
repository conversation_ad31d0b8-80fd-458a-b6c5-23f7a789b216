﻿#ifndef AUDIOHOSTWASAPI_H // 如果未定义 AUDIOHOSTWASAPI_H，防止重复包含
#define AUDIOHOSTWASAPI_H // 定义 AUDIOHOSTWASAPI_H

// 核心功能（Core）：提供 Qt 对象模型和音频库支持
#include <QObject>
#include "RtAudio.h" // 包含 RtAudio 库，用于实时音频处理

// 数据结构（Data Structures）：支持各种容器
#include <vector> // 动态数组
#include <map>    // 键值对映射
#include <set>    // 集合操作

// 线程与同步（Threading and Synchronization）：处理原子操作和互斥锁
#include <atomic>    // 原子操作
#include <mutex>     // 互斥锁
#include <functional> // 函数对象支持

// 工具与调试（Tools and Debugging）：调试输出功能
#include <QDebug> // 调试输出

// 用户交互（User Interaction）：处理消息框
#include <QMessageBox> // 消息框类

class AudioHostWasapi : public QObject { // WASAPI 音频主机类，继承自 QObject
    Q_OBJECT // 使用 Qt 的信号槽机制

public:
    explicit AudioHostWasapi(QObject *parent = nullptr); // 构造函数，带可选父对象参数
    ~AudioHostWasapi(); // 析构函数

    std::vector<RtAudio::DeviceInfo> getDevices(); // 获取设备信息列表
    bool openStream(unsigned int deviceId, const std::set<unsigned int>& outputChannels, // 打开音频流（第 1 行）
                    const std::set<unsigned int>& inputChannels, unsigned int sampleRate); // 打开音频流（第 2 行）
    bool startStream(unsigned int deviceId, const std::set<unsigned int>& outputChannels, // 启动音频流（第 1 行）
                    const std::set<unsigned int>& inputChannels, unsigned int sampleRate); // 启动音频流（第 2 行）
    void stopStream(); // 停止音频流
    bool areChannelsAvailable(unsigned int deviceId, const std::set<unsigned int>& channels, bool isOutput); // 检查通道是否可用
    unsigned int getRunningDeviceId() const; // 获取当前运行的设备 ID
    void confirmInputDataRead(); // 确认输入数据已读取
    void fillOutputData(const std::vector<int32_t>& data); // 填充输出数据

    RtAudio* getRtAudio() { return &wasapi; } // 获取 RtAudio 实例

signals:
    void requestOutputData(unsigned int nFrames, unsigned int nChannels); // 请求输出数据的信号
    void inputDataAvailable(const std::vector<int32_t>& data, unsigned int nChannels); // 输入数据可用信号
    void timeoutWarning(const QString& message); // 超时警告信号

private:
    RtAudio wasapi; // WASAPI RtAudio 实例

    bool isRunning; // 是否正在运行
    unsigned int runningDeviceId; // 当前运行的设备 ID
    std::map<unsigned int, std::set<unsigned int>> activeOutputChannels; // 活动输出通道
    std::map<unsigned int, std::set<unsigned int>> activeInputChannels; // 活动输入通道

    struct StreamConfig { // 流配置结构体
        unsigned int sampleRate; // 采样率
        unsigned int bufferFrames; // 缓冲区帧数
        unsigned int outputChannelCount; // 输出通道数
        unsigned int inputChannelCount; // 输入通道数
    }; // 流配置结构体结束
    StreamConfig currentConfig; // 当前流配置

    std::vector<int32_t> outputBuffer1, outputBuffer2; // 输出缓冲区1和2
    std::vector<int32_t> inputBuffer1, inputBuffer2; // 输入缓冲区1和2
    std::atomic<bool> useBuffer1; // 原子布尔值，指示使用哪个缓冲区
    std::mutex bufferMutex; // 缓冲区互斥锁

    bool outputFilled; // 输出缓冲区是否已填充
    bool inputRead; // 输入缓冲区是否已读取
    double lastCallbackTime; // 上次回调时间

    static int callback(void *outputBuffer, void *inputBuffer, unsigned int nBufferFrames, // 静态回调函数（第 1 行）
                        double streamTime, RtAudioStreamStatus status, void *userData); // 静态回调函数（第 2 行）
    void errorCallback(RtAudioErrorType type, const std::string &errorText); // 错误回调函数
}; // AudioHostWasapi 类结束

#endif // AUDIOHOSTWASAPI_H // 结束条件编译
