/********************************************************************************
** Form generated from reading UI file 'chanaleditform.ui'
**
** Created by: Qt User Interface Compiler version 5.15.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_CHANALEDITFORM_H
#define UI_CHANALEDITFORM_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_ChanalEditForm
{
public:
    QWidget *layoutWidget;
    QGridLayout *gridLayout;
    QGroupBox *groupBox;
    QVBoxLayout *verticalLayout_6;
    QLineEdit *ChanalNamelineEdit;
    QGroupBox *groupBox_2;
    QVBoxLayout *verticalLayout_4;
    QComboBox *DriverTypecomboBox;
    QGroupBox *groupBox_5;
    QVBoxLayout *verticalLayout;
    QLineEdit *MaxValuelineEdit;
    QGroupBox *groupBox_3;
    QVBoxLayout *verticalLayout_7;
    QComboBox *DriverComboBox;
    QGroupBox *groupBox_4;
    QVBoxLayout *verticalLayout_5;
    QComboBox *ChanalcomboBox;
    QGroupBox *groupBox_6;
    QVBoxLayout *verticalLayout_2;
    QLineEdit *MinValuelineEdit;
    QPushButton *UpdateBtn;
    QGroupBox *groupBox_7;
    QVBoxLayout *verticalLayout_3;
    QLineEdit *GainlineEdit;
    QWidget *layoutWidget1;
    QHBoxLayout *horizontalLayout;
    QPushButton *YesBtn;
    QPushButton *ExitBtn;

    void setupUi(QWidget *ChanalEditForm)
    {
        if (ChanalEditForm->objectName().isEmpty())
            ChanalEditForm->setObjectName(QString::fromUtf8("ChanalEditForm"));
        ChanalEditForm->resize(661, 428);
        layoutWidget = new QWidget(ChanalEditForm);
        layoutWidget->setObjectName(QString::fromUtf8("layoutWidget"));
        layoutWidget->setGeometry(QRect(50, 20, 551, 251));
        gridLayout = new QGridLayout(layoutWidget);
        gridLayout->setObjectName(QString::fromUtf8("gridLayout"));
        gridLayout->setContentsMargins(0, 0, 0, 0);
        groupBox = new QGroupBox(layoutWidget);
        groupBox->setObjectName(QString::fromUtf8("groupBox"));
        QFont font;
        font.setPointSize(11);
        groupBox->setFont(font);
        verticalLayout_6 = new QVBoxLayout(groupBox);
        verticalLayout_6->setObjectName(QString::fromUtf8("verticalLayout_6"));
        verticalLayout_6->setContentsMargins(1, -1, 1, -1);
        ChanalNamelineEdit = new QLineEdit(groupBox);
        ChanalNamelineEdit->setObjectName(QString::fromUtf8("ChanalNamelineEdit"));
        ChanalNamelineEdit->setMinimumSize(QSize(0, 30));

        verticalLayout_6->addWidget(ChanalNamelineEdit);


        gridLayout->addWidget(groupBox, 0, 0, 1, 1);

        groupBox_2 = new QGroupBox(layoutWidget);
        groupBox_2->setObjectName(QString::fromUtf8("groupBox_2"));
        QFont font1;
        font1.setPointSize(11);
        font1.setBold(false);
        font1.setWeight(50);
        groupBox_2->setFont(font1);
        verticalLayout_4 = new QVBoxLayout(groupBox_2);
        verticalLayout_4->setObjectName(QString::fromUtf8("verticalLayout_4"));
        verticalLayout_4->setContentsMargins(1, -1, 1, 9);
        DriverTypecomboBox = new QComboBox(groupBox_2);
        DriverTypecomboBox->addItem(QString());
        DriverTypecomboBox->addItem(QString());
        DriverTypecomboBox->addItem(QString());
        DriverTypecomboBox->addItem(QString());
        DriverTypecomboBox->setObjectName(QString::fromUtf8("DriverTypecomboBox"));
        DriverTypecomboBox->setMinimumSize(QSize(120, 30));

        verticalLayout_4->addWidget(DriverTypecomboBox);


        gridLayout->addWidget(groupBox_2, 0, 1, 1, 1);

        groupBox_5 = new QGroupBox(layoutWidget);
        groupBox_5->setObjectName(QString::fromUtf8("groupBox_5"));
        groupBox_5->setMaximumSize(QSize(110, 16777215));
        groupBox_5->setFont(font);
        verticalLayout = new QVBoxLayout(groupBox_5);
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        verticalLayout->setContentsMargins(1, -1, 1, -1);
        MaxValuelineEdit = new QLineEdit(groupBox_5);
        MaxValuelineEdit->setObjectName(QString::fromUtf8("MaxValuelineEdit"));
        MaxValuelineEdit->setMinimumSize(QSize(0, 13));
        MaxValuelineEdit->setMaximumSize(QSize(100, 16777215));

        verticalLayout->addWidget(MaxValuelineEdit);


        gridLayout->addWidget(groupBox_5, 0, 2, 1, 1);

        groupBox_3 = new QGroupBox(layoutWidget);
        groupBox_3->setObjectName(QString::fromUtf8("groupBox_3"));
        groupBox_3->setFont(font);
        verticalLayout_7 = new QVBoxLayout(groupBox_3);
        verticalLayout_7->setObjectName(QString::fromUtf8("verticalLayout_7"));
        verticalLayout_7->setContentsMargins(1, -1, 1, -1);
        DriverComboBox = new QComboBox(groupBox_3);
        DriverComboBox->setObjectName(QString::fromUtf8("DriverComboBox"));
        DriverComboBox->setMinimumSize(QSize(0, 30));

        verticalLayout_7->addWidget(DriverComboBox);


        gridLayout->addWidget(groupBox_3, 1, 0, 1, 1);

        groupBox_4 = new QGroupBox(layoutWidget);
        groupBox_4->setObjectName(QString::fromUtf8("groupBox_4"));
        groupBox_4->setFont(font);
        verticalLayout_5 = new QVBoxLayout(groupBox_4);
        verticalLayout_5->setObjectName(QString::fromUtf8("verticalLayout_5"));
        verticalLayout_5->setContentsMargins(1, -1, 1, -1);
        ChanalcomboBox = new QComboBox(groupBox_4);
        ChanalcomboBox->setObjectName(QString::fromUtf8("ChanalcomboBox"));
        ChanalcomboBox->setMinimumSize(QSize(150, 30));

        verticalLayout_5->addWidget(ChanalcomboBox);


        gridLayout->addWidget(groupBox_4, 1, 1, 1, 1);

        groupBox_6 = new QGroupBox(layoutWidget);
        groupBox_6->setObjectName(QString::fromUtf8("groupBox_6"));
        groupBox_6->setMaximumSize(QSize(110, 16777215));
        groupBox_6->setFont(font);
        verticalLayout_2 = new QVBoxLayout(groupBox_6);
        verticalLayout_2->setObjectName(QString::fromUtf8("verticalLayout_2"));
        verticalLayout_2->setContentsMargins(1, -1, 1, -1);
        MinValuelineEdit = new QLineEdit(groupBox_6);
        MinValuelineEdit->setObjectName(QString::fromUtf8("MinValuelineEdit"));
        MinValuelineEdit->setMinimumSize(QSize(0, 13));
        MinValuelineEdit->setMaximumSize(QSize(100, 16777215));

        verticalLayout_2->addWidget(MinValuelineEdit);


        gridLayout->addWidget(groupBox_6, 1, 2, 1, 1);

        UpdateBtn = new QPushButton(layoutWidget);
        UpdateBtn->setObjectName(QString::fromUtf8("UpdateBtn"));
        UpdateBtn->setFont(font);

        gridLayout->addWidget(UpdateBtn, 2, 0, 1, 2);

        groupBox_7 = new QGroupBox(layoutWidget);
        groupBox_7->setObjectName(QString::fromUtf8("groupBox_7"));
        groupBox_7->setMaximumSize(QSize(110, 16777215));
        groupBox_7->setFont(font);
        verticalLayout_3 = new QVBoxLayout(groupBox_7);
        verticalLayout_3->setObjectName(QString::fromUtf8("verticalLayout_3"));
        verticalLayout_3->setContentsMargins(1, -1, 1, -1);
        GainlineEdit = new QLineEdit(groupBox_7);
        GainlineEdit->setObjectName(QString::fromUtf8("GainlineEdit"));
        GainlineEdit->setMinimumSize(QSize(0, 13));
        GainlineEdit->setMaximumSize(QSize(100, 16777215));
        QFont font2;
        font2.setPointSize(9);
        GainlineEdit->setFont(font2);

        verticalLayout_3->addWidget(GainlineEdit);


        gridLayout->addWidget(groupBox_7, 2, 2, 1, 1);

        layoutWidget1 = new QWidget(ChanalEditForm);
        layoutWidget1->setObjectName(QString::fromUtf8("layoutWidget1"));
        layoutWidget1->setGeometry(QRect(330, 310, 271, 71));
        horizontalLayout = new QHBoxLayout(layoutWidget1);
        horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));
        horizontalLayout->setContentsMargins(0, 0, 0, 0);
        YesBtn = new QPushButton(layoutWidget1);
        YesBtn->setObjectName(QString::fromUtf8("YesBtn"));
        YesBtn->setMinimumSize(QSize(0, 30));

        horizontalLayout->addWidget(YesBtn);

        ExitBtn = new QPushButton(layoutWidget1);
        ExitBtn->setObjectName(QString::fromUtf8("ExitBtn"));
        ExitBtn->setMinimumSize(QSize(0, 30));

        horizontalLayout->addWidget(ExitBtn);


        retranslateUi(ChanalEditForm);

        QMetaObject::connectSlotsByName(ChanalEditForm);
    } // setupUi

    void retranslateUi(QWidget *ChanalEditForm)
    {
        ChanalEditForm->setWindowTitle(QCoreApplication::translate("ChanalEditForm", "\347\274\226\350\276\221\351\200\232\351\201\223\344\277\241\346\201\257", nullptr));
        groupBox->setTitle(QCoreApplication::translate("ChanalEditForm", "\351\200\232\351\201\223\345\220\215\347\247\260", nullptr));
        ChanalNamelineEdit->setText(QCoreApplication::translate("ChanalEditForm", "Chanal_0", nullptr));
        groupBox_2->setTitle(QCoreApplication::translate("ChanalEditForm", "\347\261\273\345\236\213", nullptr));
        DriverTypecomboBox->setItemText(0, QCoreApplication::translate("ChanalEditForm", "WDM", nullptr));
        DriverTypecomboBox->setItemText(1, QCoreApplication::translate("ChanalEditForm", "ASIO", nullptr));
        DriverTypecomboBox->setItemText(2, QCoreApplication::translate("ChanalEditForm", "DAQ", nullptr));
        DriverTypecomboBox->setItemText(3, QCoreApplication::translate("ChanalEditForm", "CUSTOM", nullptr));

        groupBox_5->setTitle(QCoreApplication::translate("ChanalEditForm", "\346\234\200\345\244\247\345\200\274", nullptr));
        MaxValuelineEdit->setText(QCoreApplication::translate("ChanalEditForm", "1", nullptr));
        groupBox_3->setTitle(QCoreApplication::translate("ChanalEditForm", "\350\256\276\345\244\207", nullptr));
        groupBox_4->setTitle(QCoreApplication::translate("ChanalEditForm", "\351\200\232\351\201\223", nullptr));
        groupBox_6->setTitle(QCoreApplication::translate("ChanalEditForm", "\346\234\200\345\260\217\345\200\274", nullptr));
        MinValuelineEdit->setText(QCoreApplication::translate("ChanalEditForm", "-1", nullptr));
        UpdateBtn->setText(QCoreApplication::translate("ChanalEditForm", "\345\210\267\346\226\260", nullptr));
        groupBox_7->setTitle(QCoreApplication::translate("ChanalEditForm", "\345\242\236\347\233\212", nullptr));
        GainlineEdit->setText(QCoreApplication::translate("ChanalEditForm", "1", nullptr));
        YesBtn->setText(QCoreApplication::translate("ChanalEditForm", "\347\241\256\345\256\232", nullptr));
        ExitBtn->setText(QCoreApplication::translate("ChanalEditForm", "\351\200\200\345\207\272", nullptr));
    } // retranslateUi

};

namespace Ui {
    class ChanalEditForm: public Ui_ChanalEditForm {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_CHANALEDITFORM_H
