\doxysubsection{Class List}
Here are the classes, structs, unions and interfaces with brief descriptions\+:\begin{DoxyCompactList}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1RBJ_1_1AllPass}{Iir\+::\+RBJ\+::\+All\+Pass}} }{\pageref{structIir_1_1RBJ_1_1AllPass}}{}
\item\contentsline{section}{\mbox{\hyperlink{classIir_1_1Butterworth_1_1AnalogLowPass}{Iir\+::\+Butterworth\+::\+Analog\+Low\+Pass}} }{\pageref{classIir_1_1Butterworth_1_1AnalogLowPass}}{}
\item\contentsline{section}{\mbox{\hyperlink{classIir_1_1ChebyshevI_1_1AnalogLowPass}{Iir\+::\+Chebyshev\+I\+::\+Analog\+Low\+Pass}} }{\pageref{classIir_1_1ChebyshevI_1_1AnalogLowPass}}{}
\item\contentsline{section}{\mbox{\hyperlink{classIir_1_1ChebyshevII_1_1AnalogLowPass}{Iir\+::\+Chebyshev\+II\+::\+Analog\+Low\+Pass}} }{\pageref{classIir_1_1ChebyshevII_1_1AnalogLowPass}}{}
\item\contentsline{section}{\mbox{\hyperlink{classIir_1_1Butterworth_1_1AnalogLowShelf}{Iir\+::\+Butterworth\+::\+Analog\+Low\+Shelf}} }{\pageref{classIir_1_1Butterworth_1_1AnalogLowShelf}}{}
\item\contentsline{section}{\mbox{\hyperlink{classIir_1_1ChebyshevI_1_1AnalogLowShelf}{Iir\+::\+Chebyshev\+I\+::\+Analog\+Low\+Shelf}} }{\pageref{classIir_1_1ChebyshevI_1_1AnalogLowShelf}}{}
\item\contentsline{section}{\mbox{\hyperlink{classIir_1_1ChebyshevII_1_1AnalogLowShelf}{Iir\+::\+Chebyshev\+II\+::\+Analog\+Low\+Shelf}} }{\pageref{classIir_1_1ChebyshevII_1_1AnalogLowShelf}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1Butterworth_1_1BandPass}{Iir\+::\+Butterworth\+::\+Band\+Pass$<$ Filter\+Order, State\+Type $>$}} }{\pageref{structIir_1_1Butterworth_1_1BandPass}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1ChebyshevI_1_1BandPass}{Iir\+::\+Chebyshev\+I\+::\+Band\+Pass$<$ Filter\+Order, State\+Type $>$}} }{\pageref{structIir_1_1ChebyshevI_1_1BandPass}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1ChebyshevII_1_1BandPass}{Iir\+::\+Chebyshev\+II\+::\+Band\+Pass$<$ Filter\+Order, State\+Type $>$}} }{\pageref{structIir_1_1ChebyshevII_1_1BandPass}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1RBJ_1_1BandPass1}{Iir\+::\+RBJ\+::\+Band\+Pass1}} }{\pageref{structIir_1_1RBJ_1_1BandPass1}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1RBJ_1_1BandPass2}{Iir\+::\+RBJ\+::\+Band\+Pass2}} }{\pageref{structIir_1_1RBJ_1_1BandPass2}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1Butterworth_1_1BandPassBase}{Iir\+::\+Butterworth\+::\+Band\+Pass\+Base}} }{\pageref{structIir_1_1Butterworth_1_1BandPassBase}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1ChebyshevI_1_1BandPassBase}{Iir\+::\+Chebyshev\+I\+::\+Band\+Pass\+Base}} }{\pageref{structIir_1_1ChebyshevI_1_1BandPassBase}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1ChebyshevII_1_1BandPassBase}{Iir\+::\+Chebyshev\+II\+::\+Band\+Pass\+Base}} }{\pageref{structIir_1_1ChebyshevII_1_1BandPassBase}}{}
\item\contentsline{section}{\mbox{\hyperlink{classIir_1_1BandPassTransform}{Iir\+::\+Band\+Pass\+Transform}} }{\pageref{classIir_1_1BandPassTransform}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1Butterworth_1_1BandShelf}{Iir\+::\+Butterworth\+::\+Band\+Shelf$<$ Filter\+Order, State\+Type $>$}} }{\pageref{structIir_1_1Butterworth_1_1BandShelf}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1ChebyshevI_1_1BandShelf}{Iir\+::\+Chebyshev\+I\+::\+Band\+Shelf$<$ Filter\+Order, State\+Type $>$}} }{\pageref{structIir_1_1ChebyshevI_1_1BandShelf}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1ChebyshevII_1_1BandShelf}{Iir\+::\+Chebyshev\+II\+::\+Band\+Shelf$<$ Filter\+Order, State\+Type $>$}} }{\pageref{structIir_1_1ChebyshevII_1_1BandShelf}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1RBJ_1_1BandShelf}{Iir\+::\+RBJ\+::\+Band\+Shelf}} }{\pageref{structIir_1_1RBJ_1_1BandShelf}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1Butterworth_1_1BandShelfBase}{Iir\+::\+Butterworth\+::\+Band\+Shelf\+Base}} }{\pageref{structIir_1_1Butterworth_1_1BandShelfBase}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1ChebyshevI_1_1BandShelfBase}{Iir\+::\+Chebyshev\+I\+::\+Band\+Shelf\+Base}} }{\pageref{structIir_1_1ChebyshevI_1_1BandShelfBase}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1ChebyshevII_1_1BandShelfBase}{Iir\+::\+Chebyshev\+II\+::\+Band\+Shelf\+Base}} }{\pageref{structIir_1_1ChebyshevII_1_1BandShelfBase}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1Butterworth_1_1BandStop}{Iir\+::\+Butterworth\+::\+Band\+Stop$<$ Filter\+Order, State\+Type $>$}} }{\pageref{structIir_1_1Butterworth_1_1BandStop}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1ChebyshevI_1_1BandStop}{Iir\+::\+Chebyshev\+I\+::\+Band\+Stop$<$ Filter\+Order, State\+Type $>$}} }{\pageref{structIir_1_1ChebyshevI_1_1BandStop}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1ChebyshevII_1_1BandStop}{Iir\+::\+Chebyshev\+II\+::\+Band\+Stop$<$ Filter\+Order, State\+Type $>$}} }{\pageref{structIir_1_1ChebyshevII_1_1BandStop}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1RBJ_1_1BandStop}{Iir\+::\+RBJ\+::\+Band\+Stop}} }{\pageref{structIir_1_1RBJ_1_1BandStop}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1Butterworth_1_1BandStopBase}{Iir\+::\+Butterworth\+::\+Band\+Stop\+Base}} }{\pageref{structIir_1_1Butterworth_1_1BandStopBase}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1ChebyshevI_1_1BandStopBase}{Iir\+::\+Chebyshev\+I\+::\+Band\+Stop\+Base}} }{\pageref{structIir_1_1ChebyshevI_1_1BandStopBase}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1ChebyshevII_1_1BandStopBase}{Iir\+::\+Chebyshev\+II\+::\+Band\+Stop\+Base}} }{\pageref{structIir_1_1ChebyshevII_1_1BandStopBase}}{}
\item\contentsline{section}{\mbox{\hyperlink{classIir_1_1BandStopTransform}{Iir\+::\+Band\+Stop\+Transform}} }{\pageref{classIir_1_1BandStopTransform}}{}
\item\contentsline{section}{\mbox{\hyperlink{classIir_1_1Biquad}{Iir\+::\+Biquad}} }{\pageref{classIir_1_1Biquad}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1BiquadPoleState}{Iir\+::\+Biquad\+Pole\+State}} }{\pageref{structIir_1_1BiquadPoleState}}{}
\item\contentsline{section}{\mbox{\hyperlink{classIir_1_1Cascade}{Iir\+::\+Cascade}} }{\pageref{classIir_1_1Cascade}}{}
\item\contentsline{section}{\mbox{\hyperlink{classIir_1_1CascadeStages}{Iir\+::\+Cascade\+Stages$<$ Max\+Stages, State\+Type $>$}} }{\pageref{classIir_1_1CascadeStages}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1ComplexPair}{Iir\+::\+Complex\+Pair}} }{\pageref{structIir_1_1ComplexPair}}{}
\item\contentsline{section}{\mbox{\hyperlink{classIir_1_1DirectFormI}{Iir\+::\+Direct\+FormI}} }{\pageref{classIir_1_1DirectFormI}}{}
\item\contentsline{section}{\mbox{\hyperlink{classIir_1_1DirectFormII}{Iir\+::\+Direct\+Form\+II}} }{\pageref{classIir_1_1DirectFormII}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1Butterworth_1_1HighPass}{Iir\+::\+Butterworth\+::\+High\+Pass$<$ Filter\+Order, State\+Type $>$}} }{\pageref{structIir_1_1Butterworth_1_1HighPass}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1ChebyshevI_1_1HighPass}{Iir\+::\+Chebyshev\+I\+::\+High\+Pass$<$ Filter\+Order, State\+Type $>$}} }{\pageref{structIir_1_1ChebyshevI_1_1HighPass}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1ChebyshevII_1_1HighPass}{Iir\+::\+Chebyshev\+II\+::\+High\+Pass$<$ Filter\+Order, State\+Type $>$}} }{\pageref{structIir_1_1ChebyshevII_1_1HighPass}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1RBJ_1_1HighPass}{Iir\+::\+RBJ\+::\+High\+Pass}} }{\pageref{structIir_1_1RBJ_1_1HighPass}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1Butterworth_1_1HighPassBase}{Iir\+::\+Butterworth\+::\+High\+Pass\+Base}} }{\pageref{structIir_1_1Butterworth_1_1HighPassBase}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1ChebyshevI_1_1HighPassBase}{Iir\+::\+Chebyshev\+I\+::\+High\+Pass\+Base}} }{\pageref{structIir_1_1ChebyshevI_1_1HighPassBase}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1ChebyshevII_1_1HighPassBase}{Iir\+::\+Chebyshev\+II\+::\+High\+Pass\+Base}} }{\pageref{structIir_1_1ChebyshevII_1_1HighPassBase}}{}
\item\contentsline{section}{\mbox{\hyperlink{classIir_1_1HighPassTransform}{Iir\+::\+High\+Pass\+Transform}} }{\pageref{classIir_1_1HighPassTransform}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1Butterworth_1_1HighShelf}{Iir\+::\+Butterworth\+::\+High\+Shelf$<$ Filter\+Order, State\+Type $>$}} }{\pageref{structIir_1_1Butterworth_1_1HighShelf}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1ChebyshevI_1_1HighShelf}{Iir\+::\+Chebyshev\+I\+::\+High\+Shelf$<$ Filter\+Order, State\+Type $>$}} }{\pageref{structIir_1_1ChebyshevI_1_1HighShelf}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1ChebyshevII_1_1HighShelf}{Iir\+::\+Chebyshev\+II\+::\+High\+Shelf$<$ Filter\+Order, State\+Type $>$}} }{\pageref{structIir_1_1ChebyshevII_1_1HighShelf}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1RBJ_1_1HighShelf}{Iir\+::\+RBJ\+::\+High\+Shelf}} }{\pageref{structIir_1_1RBJ_1_1HighShelf}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1Butterworth_1_1HighShelfBase}{Iir\+::\+Butterworth\+::\+High\+Shelf\+Base}} }{\pageref{structIir_1_1Butterworth_1_1HighShelfBase}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1ChebyshevI_1_1HighShelfBase}{Iir\+::\+Chebyshev\+I\+::\+High\+Shelf\+Base}} }{\pageref{structIir_1_1ChebyshevI_1_1HighShelfBase}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1ChebyshevII_1_1HighShelfBase}{Iir\+::\+Chebyshev\+II\+::\+High\+Shelf\+Base}} }{\pageref{structIir_1_1ChebyshevII_1_1HighShelfBase}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1RBJ_1_1IIRNotch}{Iir\+::\+RBJ\+::\+IIRNotch}} }{\pageref{structIir_1_1RBJ_1_1IIRNotch}}{}
\item\contentsline{section}{\mbox{\hyperlink{classIir_1_1Layout}{Iir\+::\+Layout$<$ Max\+Poles $>$}} }{\pageref{classIir_1_1Layout}}{}
\item\contentsline{section}{\mbox{\hyperlink{classIir_1_1LayoutBase}{Iir\+::\+Layout\+Base}} }{\pageref{classIir_1_1LayoutBase}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1Butterworth_1_1LowPass}{Iir\+::\+Butterworth\+::\+Low\+Pass$<$ Filter\+Order, State\+Type $>$}} }{\pageref{structIir_1_1Butterworth_1_1LowPass}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1ChebyshevI_1_1LowPass}{Iir\+::\+Chebyshev\+I\+::\+Low\+Pass$<$ Filter\+Order, State\+Type $>$}} }{\pageref{structIir_1_1ChebyshevI_1_1LowPass}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1ChebyshevII_1_1LowPass}{Iir\+::\+Chebyshev\+II\+::\+Low\+Pass$<$ Filter\+Order, State\+Type $>$}} }{\pageref{structIir_1_1ChebyshevII_1_1LowPass}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1RBJ_1_1LowPass}{Iir\+::\+RBJ\+::\+Low\+Pass}} }{\pageref{structIir_1_1RBJ_1_1LowPass}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1Butterworth_1_1LowPassBase}{Iir\+::\+Butterworth\+::\+Low\+Pass\+Base}} }{\pageref{structIir_1_1Butterworth_1_1LowPassBase}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1ChebyshevI_1_1LowPassBase}{Iir\+::\+Chebyshev\+I\+::\+Low\+Pass\+Base}} }{\pageref{structIir_1_1ChebyshevI_1_1LowPassBase}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1ChebyshevII_1_1LowPassBase}{Iir\+::\+Chebyshev\+II\+::\+Low\+Pass\+Base}} }{\pageref{structIir_1_1ChebyshevII_1_1LowPassBase}}{}
\item\contentsline{section}{\mbox{\hyperlink{classIir_1_1LowPassTransform}{Iir\+::\+Low\+Pass\+Transform}} }{\pageref{classIir_1_1LowPassTransform}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1Butterworth_1_1LowShelf}{Iir\+::\+Butterworth\+::\+Low\+Shelf$<$ Filter\+Order, State\+Type $>$}} }{\pageref{structIir_1_1Butterworth_1_1LowShelf}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1ChebyshevI_1_1LowShelf}{Iir\+::\+Chebyshev\+I\+::\+Low\+Shelf$<$ Filter\+Order, State\+Type $>$}} }{\pageref{structIir_1_1ChebyshevI_1_1LowShelf}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1ChebyshevII_1_1LowShelf}{Iir\+::\+Chebyshev\+II\+::\+Low\+Shelf$<$ Filter\+Order, State\+Type $>$}} }{\pageref{structIir_1_1ChebyshevII_1_1LowShelf}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1RBJ_1_1LowShelf}{Iir\+::\+RBJ\+::\+Low\+Shelf}} }{\pageref{structIir_1_1RBJ_1_1LowShelf}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1Butterworth_1_1LowShelfBase}{Iir\+::\+Butterworth\+::\+Low\+Shelf\+Base}} }{\pageref{structIir_1_1Butterworth_1_1LowShelfBase}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1ChebyshevI_1_1LowShelfBase}{Iir\+::\+Chebyshev\+I\+::\+Low\+Shelf\+Base}} }{\pageref{structIir_1_1ChebyshevI_1_1LowShelfBase}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1ChebyshevII_1_1LowShelfBase}{Iir\+::\+Chebyshev\+II\+::\+Low\+Shelf\+Base}} }{\pageref{structIir_1_1ChebyshevII_1_1LowShelfBase}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1Custom_1_1OnePole}{Iir\+::\+Custom\+::\+One\+Pole}} }{\pageref{structIir_1_1Custom_1_1OnePole}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1PoleFilter}{Iir\+::\+Pole\+Filter$<$ Base\+Class, State\+Type, Max\+Analog\+Poles, Max\+Digital\+Poles $>$}} }{\pageref{structIir_1_1PoleFilter}}{}
\item\contentsline{section}{\mbox{\hyperlink{classIir_1_1PoleFilterBase}{Iir\+::\+Pole\+Filter\+Base$<$ Analog\+Prototype $>$}} }{\pageref{classIir_1_1PoleFilterBase}}{}
\item\contentsline{section}{\mbox{\hyperlink{classIir_1_1PoleFilterBase2}{Iir\+::\+Pole\+Filter\+Base2}} }{\pageref{classIir_1_1PoleFilterBase2}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1PoleZeroPair}{Iir\+::\+Pole\+Zero\+Pair}} }{\pageref{structIir_1_1PoleZeroPair}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1RBJ_1_1RBJbase}{Iir\+::\+RBJ\+::\+RBJbase}} }{\pageref{structIir_1_1RBJ_1_1RBJbase}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1Custom_1_1SOSCascade}{Iir\+::\+Custom\+::\+SOSCascade$<$ NSOS, State\+Type $>$}} }{\pageref{structIir_1_1Custom_1_1SOSCascade}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1Cascade_1_1Storage}{Iir\+::\+Cascade\+::\+Storage}} }{\pageref{structIir_1_1Cascade_1_1Storage}}{}
\item\contentsline{section}{\mbox{\hyperlink{classIir_1_1TransposedDirectFormII}{Iir\+::\+Transposed\+Direct\+Form\+II}} }{\pageref{classIir_1_1TransposedDirectFormII}}{}
\item\contentsline{section}{\mbox{\hyperlink{structIir_1_1Custom_1_1TwoPole}{Iir\+::\+Custom\+::\+Two\+Pole}} }{\pageref{structIir_1_1Custom_1_1TwoPole}}{}
\end{DoxyCompactList}
