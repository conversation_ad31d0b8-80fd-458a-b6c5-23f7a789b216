﻿#ifndef AUDIOCHANNELCONFIGMANAGER_H
#define AUDIOCHANNELCONFIGMANAGER_H

// 核心功能（Core）：提供基本数据结构和对象支持
#include <QMap>      // 用于存储键值对的映射容器
#include <QString>   // 用于处理字符串
#include <QObject>   // Qt 对象的基类，提供信号与槽机制

// 文件操作（File Operations）：处理文件和目录操作
#include <QFile>     // 用于文件读写操作
#include <QDir>      // 用于目录操作，如创建目录

// 同步和调试（Synchronization and Debugging）：线程安全和调试输出
#include <QMutex>    // 用于线程同步的互斥锁
//#include <QMutexLocker>  // 提供自动加锁和解锁的辅助类（已注释，未使用）
#include <QDebug>    // 用于调试输出

// 用户交互（User Interaction）：处理消息框等界面元素
#include <QMessageBox>  // 用于显示警告或提示消息框

// JSON 处理（JSON Processing）：解析和生成 JSON 数据
#include <QJsonDocument>  // 用于处理 JSON 文档
#include <QJsonObject>    // 用于处理 JSON 对象
#include <QJsonArray>     // 用于处理 JSON 数组

// 音频通道配置结构体，存储单个通道的配置信息
struct AudioChannalInfoConfig {
    unsigned int DriverTypeIndexes{};   // 驱动类型索引，默认为 0
    QString Driver_Type;                // 驱动类型（如 "ASIO" 或 "WASAPI"）
    QString Driver_Name;                // 设备名称
    QString Sound_Card_Channel_Name;    // 声卡通道名称
    unsigned int DriverID{};            // 声卡 ID，默认为 0
    float Channel_Gain;                 // 通道增益（以 dB 为单位）
    float Channel_Max_Value;            // 通道最大值（如信号幅度上限）
    float Channel_Min_Value;            // 通道最小值（如信号幅度下限）
};

// 音频通道配置管理类，继承自 QObject，支持信号与槽机制
class AudioChannelConfigManager : public QObject {
    Q_OBJECT
public:
    // 构造函数
    // 参数：
    //   configPath：配置文件路径，默认为 "config/Channel_Info.json"
    //   parent：父对象指针，默认为 nullptr，用于 Qt 对象树管理
    AudioChannelConfigManager(const QString& configPath = "config/Channel_Info.json", QObject* parent = nullptr);

    // 枚举类型，定义通道类型（输入或输出）
    enum class ChannelType {
        isOutput,  // 输出通道
        isInput    // 输入通道
    };

    // 存储输入通道配置的映射表
    // 键：ChannelType::isInput
    // 值：通道名称到配置的映射（QString -> AudioChannalInfoConfig）
    QMap<ChannelType, QMap<QString, AudioChannalInfoConfig>> audioInputs;

    // 存储输出通道配置的映射表
    // 键：ChannelType::isOutput
    // 值：通道名称到配置的映射（QString -> AudioChannalInfoConfig）
    QMap<ChannelType, QMap<QString, AudioChannalInfoConfig>> audioOutputs;

    // 将配置保存到 JSON 文件
    // 返回：true 表示保存成功，false 表示失败
    bool saveToFile();

    // 从 JSON 文件加载配置
    // 返回：true 表示加载成功，false 表示失败
    bool loadFromFile();

    // 添加新的通道配置
    // 参数：
    //   type：通道类型（输入或输出）
    //   channelName：通道名称
    //   config：通道配置信息
    // 返回：true 表示添加成功，false 表示通道已存在
    bool addChannel(ChannelType type, const QString& channelName, const AudioChannalInfoConfig& config);

    // 删除指定通道配置
    // 参数：
    //   type：通道类型（输入或输出）
    //   channelName：要删除的通道名称
    // 返回：true 表示删除成功，false 表示通道不存在
    bool deleteChannel(ChannelType type, const QString& channelName);

    // 修改现有通道配置
    // 参数：
    //   type：通道类型（输入或输出）
    //   channelName：要修改的通道名称
    //   config：新的通道配置信息
    // 返回：true 表示修改成功，false 表示通道不存在
    bool modifyChannel(ChannelType type, const QString& channelName, const AudioChannalInfoConfig& config);

    // 查询指定通道的配置
    // 参数：
    //   type：通道类型（输入或输出）
    //   channelName：要查询的通道名称
    //   config：输出参数，存储查询到的配置信息
    // 返回：true 表示查询成功，false 表示通道不存在
    bool queryChannel(ChannelType type, const QString& channelName, AudioChannalInfoConfig& config) const;

    // 清空指定类型的通道配置
    // 参数：
    //   type：要清空的通道类型（输入或输出）
    void clearChannels(ChannelType type);

    // 获取所有输入通道的名称列表
    // 返回：输入通道名称的 QStringList
    QStringList getInputChannelNames() const;

    // 获取所有输出通道的名称列表
    // 返回：输出通道名称的 QStringList
    QStringList getOutputChannelNames() const;

    // 获取指定通道的配置信息（输入或输出）
    // 参数：
    //   channelName：通道名称
    //   config：输出参数，存储查询到的配置信息
    // 返回：true 表示查询成功，false 表示通道不存在
    bool getChannelConfig(const QString& channelName, AudioChannalInfoConfig& config) const;

    // 转换配置为 JSON：将 AudioChannalInfoConfig 转换为 QJsonObject
    QJsonObject configToJson(const AudioChannalInfoConfig& config) const;
    // 转换 JSON 为配置：将 QJsonObject 解析为 AudioChannalInfoConfig
    AudioChannalInfoConfig jsonToConfig(const QJsonObject& json) const;

private:
    // 配置文件路径（默认为 "config/Channel_Info.json"）
    QString configFilePath;

    // 互斥锁，用于线程安全的访问 audioInputs 和 audioOutputs
    mutable QMutex mutex;
};

#endif // AUDIOCHANNELCONFIGMANAGER_H
