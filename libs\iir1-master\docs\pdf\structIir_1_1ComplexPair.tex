\hypertarget{structIir_1_1ComplexPair}{}\doxysubsection{Iir\+::Complex\+Pair Struct Reference}
\label{structIir_1_1ComplexPair}\index{Iir::ComplexPair@{Iir::ComplexPair}}


{\ttfamily \#include $<$Types.\+h$>$}

Inheritance diagram for Iir\+::Complex\+Pair\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=2.000000cm]{structIir_1_1ComplexPair}
\end{center}
\end{figure}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
bool \mbox{\hyperlink{structIir_1_1ComplexPair_a79d121320c8b042faebcc0364398b071}{is\+Matched\+Pair}} () const
\end{DoxyCompactItemize}


\doxysubsubsection{Detailed Description}
A conjugate or real pair 

\doxysubsubsection{Member Function Documentation}
\mbox{\Hypertarget{structIir_1_1ComplexPair_a79d121320c8b042faebcc0364398b071}\label{structIir_1_1ComplexPair_a79d121320c8b042faebcc0364398b071}} 
\index{Iir::ComplexPair@{Iir::ComplexPair}!isMatchedPair@{isMatchedPair}}
\index{isMatchedPair@{isMatchedPair}!Iir::ComplexPair@{Iir::ComplexPair}}
\doxyparagraph{\texorpdfstring{isMatchedPair()}{isMatchedPair()}}
{\footnotesize\ttfamily bool Iir\+::\+Complex\+Pair\+::is\+Matched\+Pair (\begin{DoxyParamCaption}{ }\end{DoxyParamCaption}) const\hspace{0.3cm}{\ttfamily [inline]}}

Returns true if this is either a conjugate pair, or a pair of reals where neither is zero. 

The documentation for this struct was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
iir/Types.\+h\end{DoxyCompactItemize}
