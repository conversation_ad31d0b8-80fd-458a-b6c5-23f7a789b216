\hypertarget{structIir_1_1ChebyshevI_1_1BandShelf}{}\doxysubsection{Iir\+::ChebyshevI\+::Band\+Shelf$<$ Filter\+Order, State\+Type $>$ Struct Template Reference}
\label{structIir_1_1ChebyshevI_1_1BandShelf}\index{Iir::ChebyshevI::BandShelf$<$ FilterOrder, StateType $>$@{Iir::ChebyshevI::BandShelf$<$ FilterOrder, StateType $>$}}


{\ttfamily \#include $<$Chebyshev\+I.\+h$>$}

Inheritance diagram for Iir\+::ChebyshevI\+::Band\+Shelf$<$ Filter\+Order, State\+Type $>$\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=2.434783cm]{structIir_1_1ChebyshevI_1_1BandShelf}
\end{center}
\end{figure}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
void \mbox{\hyperlink{structIir_1_1ChebyshevI_1_1BandShelf_a959d521af6b77dc269814c85543cff2f}{setup}} (double sample\+Rate, double center\+Frequency, double width\+Frequency, double gain\+Db, double ripple\+Db)
\item 
void \mbox{\hyperlink{structIir_1_1ChebyshevI_1_1BandShelf_abb0e9a1480addb5b4ce38b2d83888c95}{setup}} (int req\+Order, double sample\+Rate, double center\+Frequency, double width\+Frequency, double gain\+Db, double ripple\+Db)
\item 
void \mbox{\hyperlink{structIir_1_1ChebyshevI_1_1BandShelf_aef32bdf8e3209f0ab169d2641781ad6c}{setupN}} (double center\+Frequency, double width\+Frequency, double gain\+Db, double ripple\+Db)
\item 
void \mbox{\hyperlink{structIir_1_1ChebyshevI_1_1BandShelf_a6aa48c5f0f3796ffc9b0384acc8c42fa}{setupN}} (int req\+Order, double center\+Frequency, double width\+Frequency, double gain\+Db, double ripple\+Db)
\end{DoxyCompactItemize}


\doxysubsubsection{Detailed Description}
\subsubsection*{template$<$int Filter\+Order = 4, class State\+Type = Direct\+Form\+II$>$\newline
struct Iir\+::\+Chebyshev\+I\+::\+Band\+Shelf$<$ Filter\+Order, State\+Type $>$}

\mbox{\hyperlink{namespaceIir_1_1ChebyshevI}{ChebyshevI}} bandshelf filter. Specified gain in the passband. Otherwise 0 dB. 
\begin{DoxyParams}{Parameters}
{\em Filter\+Order} & Reserves memory for a filter of the order Filter\+Order \\
\hline
{\em State\+Type} & The filter topology\+: \mbox{\hyperlink{classIir_1_1DirectFormI}{Direct\+FormI}}, \mbox{\hyperlink{classIir_1_1DirectFormII}{Direct\+Form\+II}}, ... \\
\hline
\end{DoxyParams}


\doxysubsubsection{Member Function Documentation}
\mbox{\Hypertarget{structIir_1_1ChebyshevI_1_1BandShelf_a959d521af6b77dc269814c85543cff2f}\label{structIir_1_1ChebyshevI_1_1BandShelf_a959d521af6b77dc269814c85543cff2f}} 
\index{Iir::ChebyshevI::BandShelf$<$ FilterOrder, StateType $>$@{Iir::ChebyshevI::BandShelf$<$ FilterOrder, StateType $>$}!setup@{setup}}
\index{setup@{setup}!Iir::ChebyshevI::BandShelf$<$ FilterOrder, StateType $>$@{Iir::ChebyshevI::BandShelf$<$ FilterOrder, StateType $>$}}
\doxyparagraph{\texorpdfstring{setup()}{setup()}\hspace{0.1cm}{\footnotesize\ttfamily [1/2]}}
{\footnotesize\ttfamily template$<$int Filter\+Order = 4, class State\+Type  = Direct\+Form\+II$>$ \\
void \mbox{\hyperlink{structIir_1_1ChebyshevI_1_1BandShelf}{Iir\+::\+Chebyshev\+I\+::\+Band\+Shelf}}$<$ Filter\+Order, State\+Type $>$\+::setup (\begin{DoxyParamCaption}\item[{double}]{sample\+Rate,  }\item[{double}]{center\+Frequency,  }\item[{double}]{width\+Frequency,  }\item[{double}]{gain\+Db,  }\item[{double}]{ripple\+Db }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients of the filter at the order Filter\+Order 
\begin{DoxyParams}{Parameters}
{\em sample\+Rate} & Sampling rate \\
\hline
{\em center\+Frequency} & Center frequency of the passband \\
\hline
{\em width\+Frequency} & Width of the passband. \\
\hline
{\em gain\+Db} & Gain in the passband. The stopband has 0 dB. \\
\hline
{\em ripple\+Db} & Permitted ripples in dB in the passband. \\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{structIir_1_1ChebyshevI_1_1BandShelf_abb0e9a1480addb5b4ce38b2d83888c95}\label{structIir_1_1ChebyshevI_1_1BandShelf_abb0e9a1480addb5b4ce38b2d83888c95}} 
\index{Iir::ChebyshevI::BandShelf$<$ FilterOrder, StateType $>$@{Iir::ChebyshevI::BandShelf$<$ FilterOrder, StateType $>$}!setup@{setup}}
\index{setup@{setup}!Iir::ChebyshevI::BandShelf$<$ FilterOrder, StateType $>$@{Iir::ChebyshevI::BandShelf$<$ FilterOrder, StateType $>$}}
\doxyparagraph{\texorpdfstring{setup()}{setup()}\hspace{0.1cm}{\footnotesize\ttfamily [2/2]}}
{\footnotesize\ttfamily template$<$int Filter\+Order = 4, class State\+Type  = Direct\+Form\+II$>$ \\
void \mbox{\hyperlink{structIir_1_1ChebyshevI_1_1BandShelf}{Iir\+::\+Chebyshev\+I\+::\+Band\+Shelf}}$<$ Filter\+Order, State\+Type $>$\+::setup (\begin{DoxyParamCaption}\item[{int}]{req\+Order,  }\item[{double}]{sample\+Rate,  }\item[{double}]{center\+Frequency,  }\item[{double}]{width\+Frequency,  }\item[{double}]{gain\+Db,  }\item[{double}]{ripple\+Db }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients of the filter at specified order 
\begin{DoxyParams}{Parameters}
{\em req\+Order} & Actual order for the filter calculations \\
\hline
{\em sample\+Rate} & Sampling rate \\
\hline
{\em center\+Frequency} & Center frequency of the passband \\
\hline
{\em width\+Frequency} & Width of the passband. \\
\hline
{\em gain\+Db} & Gain in the passband. The stopband has 0 dB. \\
\hline
{\em ripple\+Db} & Permitted ripples in dB in the passband. \\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{structIir_1_1ChebyshevI_1_1BandShelf_aef32bdf8e3209f0ab169d2641781ad6c}\label{structIir_1_1ChebyshevI_1_1BandShelf_aef32bdf8e3209f0ab169d2641781ad6c}} 
\index{Iir::ChebyshevI::BandShelf$<$ FilterOrder, StateType $>$@{Iir::ChebyshevI::BandShelf$<$ FilterOrder, StateType $>$}!setupN@{setupN}}
\index{setupN@{setupN}!Iir::ChebyshevI::BandShelf$<$ FilterOrder, StateType $>$@{Iir::ChebyshevI::BandShelf$<$ FilterOrder, StateType $>$}}
\doxyparagraph{\texorpdfstring{setupN()}{setupN()}\hspace{0.1cm}{\footnotesize\ttfamily [1/2]}}
{\footnotesize\ttfamily template$<$int Filter\+Order = 4, class State\+Type  = Direct\+Form\+II$>$ \\
void \mbox{\hyperlink{structIir_1_1ChebyshevI_1_1BandShelf}{Iir\+::\+Chebyshev\+I\+::\+Band\+Shelf}}$<$ Filter\+Order, State\+Type $>$\+::setupN (\begin{DoxyParamCaption}\item[{double}]{center\+Frequency,  }\item[{double}]{width\+Frequency,  }\item[{double}]{gain\+Db,  }\item[{double}]{ripple\+Db }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients of the filter at the order Filter\+Order 
\begin{DoxyParams}{Parameters}
{\em center\+Frequency} & Normalised centre frequency (0..1/2) of the passband \\
\hline
{\em width\+Frequency} & Width of the passband. \\
\hline
{\em gain\+Db} & Gain in the passband. The stopband has 0 dB. \\
\hline
{\em ripple\+Db} & Permitted ripples in dB in the passband. \\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{structIir_1_1ChebyshevI_1_1BandShelf_a6aa48c5f0f3796ffc9b0384acc8c42fa}\label{structIir_1_1ChebyshevI_1_1BandShelf_a6aa48c5f0f3796ffc9b0384acc8c42fa}} 
\index{Iir::ChebyshevI::BandShelf$<$ FilterOrder, StateType $>$@{Iir::ChebyshevI::BandShelf$<$ FilterOrder, StateType $>$}!setupN@{setupN}}
\index{setupN@{setupN}!Iir::ChebyshevI::BandShelf$<$ FilterOrder, StateType $>$@{Iir::ChebyshevI::BandShelf$<$ FilterOrder, StateType $>$}}
\doxyparagraph{\texorpdfstring{setupN()}{setupN()}\hspace{0.1cm}{\footnotesize\ttfamily [2/2]}}
{\footnotesize\ttfamily template$<$int Filter\+Order = 4, class State\+Type  = Direct\+Form\+II$>$ \\
void \mbox{\hyperlink{structIir_1_1ChebyshevI_1_1BandShelf}{Iir\+::\+Chebyshev\+I\+::\+Band\+Shelf}}$<$ Filter\+Order, State\+Type $>$\+::setupN (\begin{DoxyParamCaption}\item[{int}]{req\+Order,  }\item[{double}]{center\+Frequency,  }\item[{double}]{width\+Frequency,  }\item[{double}]{gain\+Db,  }\item[{double}]{ripple\+Db }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients of the filter at specified order 
\begin{DoxyParams}{Parameters}
{\em req\+Order} & Actual order for the filter calculations \\
\hline
{\em center\+Frequency} & Normalised centre frequency (0..1/2) of the passband \\
\hline
{\em width\+Frequency} & Width of the passband. \\
\hline
{\em gain\+Db} & Gain in the passband. The stopband has 0 dB. \\
\hline
{\em ripple\+Db} & Permitted ripples in dB in the passband. \\
\hline
\end{DoxyParams}


The documentation for this struct was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
iir/Chebyshev\+I.\+h\end{DoxyCompactItemize}
