\hypertarget{structIir_1_1RBJ_1_1BandShelf}{}\doxysubsection{Iir\+::RBJ\+::Band\+Shelf Struct Reference}
\label{structIir_1_1RBJ_1_1BandShelf}\index{Iir::RBJ::BandShelf@{Iir::RBJ::BandShelf}}


{\ttfamily \#include $<$RBJ.\+h$>$}

Inheritance diagram for Iir\+::RBJ\+::Band\+Shelf\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=3.000000cm]{structIir_1_1RBJ_1_1BandShelf}
\end{center}
\end{figure}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
void \mbox{\hyperlink{structIir_1_1RBJ_1_1BandShelf_ae5184f5a8c32602689c866128a3471c1}{setupN}} (double center\+Frequency, double gain\+Db, double band\+Width)
\item 
void \mbox{\hyperlink{structIir_1_1RBJ_1_1BandShelf_ae4b2c50b6cf6715d881f9c3681530870}{setup}} (double sample\+Rate, double center\+Frequency, double gain\+Db, double band\+Width)
\end{DoxyCompactItemize}


\doxysubsubsection{Detailed Description}
Band shelf\+: 0db in the stopband and gain\+Db in the passband. 

\doxysubsubsection{Member Function Documentation}
\mbox{\Hypertarget{structIir_1_1RBJ_1_1BandShelf_ae4b2c50b6cf6715d881f9c3681530870}\label{structIir_1_1RBJ_1_1BandShelf_ae4b2c50b6cf6715d881f9c3681530870}} 
\index{Iir::RBJ::BandShelf@{Iir::RBJ::BandShelf}!setup@{setup}}
\index{setup@{setup}!Iir::RBJ::BandShelf@{Iir::RBJ::BandShelf}}
\doxyparagraph{\texorpdfstring{setup()}{setup()}}
{\footnotesize\ttfamily void Iir\+::\+RBJ\+::\+Band\+Shelf\+::setup (\begin{DoxyParamCaption}\item[{double}]{sample\+Rate,  }\item[{double}]{center\+Frequency,  }\item[{double}]{gain\+Db,  }\item[{double}]{band\+Width }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients 
\begin{DoxyParams}{Parameters}
{\em sample\+Rate} & Sampling rate \\
\hline
{\em center\+Frequency} & frequency \\
\hline
{\em gain\+Db} & Gain in the passband \\
\hline
{\em band\+Width} & Bandwidth in octaves \\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{structIir_1_1RBJ_1_1BandShelf_ae5184f5a8c32602689c866128a3471c1}\label{structIir_1_1RBJ_1_1BandShelf_ae5184f5a8c32602689c866128a3471c1}} 
\index{Iir::RBJ::BandShelf@{Iir::RBJ::BandShelf}!setupN@{setupN}}
\index{setupN@{setupN}!Iir::RBJ::BandShelf@{Iir::RBJ::BandShelf}}
\doxyparagraph{\texorpdfstring{setupN()}{setupN()}}
{\footnotesize\ttfamily void Iir\+::\+RBJ\+::\+Band\+Shelf\+::setupN (\begin{DoxyParamCaption}\item[{double}]{center\+Frequency,  }\item[{double}]{gain\+Db,  }\item[{double}]{band\+Width }\end{DoxyParamCaption})}

Calculates the coefficients 
\begin{DoxyParams}{Parameters}
{\em center\+Frequency} & Normalised centre frequency \\
\hline
{\em gain\+Db} & Gain in the passband \\
\hline
{\em band\+Width} & Bandwidth in octaves \\
\hline
\end{DoxyParams}


The documentation for this struct was generated from the following files\+:\begin{DoxyCompactItemize}
\item 
iir/RBJ.\+h\item 
iir/RBJ.\+cpp\end{DoxyCompactItemize}
