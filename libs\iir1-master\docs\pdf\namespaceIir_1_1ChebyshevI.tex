\hypertarget{namespaceIir_1_1ChebyshevI}{}\doxysubsection{Iir\+::ChebyshevI Namespace Reference}
\label{namespaceIir_1_1ChebyshevI}\index{Iir::ChebyshevI@{Iir::ChebyshevI}}
\doxysubsubsection*{Classes}
\begin{DoxyCompactItemize}
\item 
class \mbox{\hyperlink{classIir_1_1ChebyshevI_1_1AnalogLowPass}{Analog\+Low\+Pass}}
\item 
class \mbox{\hyperlink{classIir_1_1ChebyshevI_1_1AnalogLowShelf}{Analog\+Low\+Shelf}}
\item 
struct \mbox{\hyperlink{structIir_1_1ChebyshevI_1_1LowPassBase}{Low\+Pass\+Base}}
\item 
struct \mbox{\hyperlink{structIir_1_1ChebyshevI_1_1HighPassBase}{High\+Pass\+Base}}
\item 
struct \mbox{\hyperlink{structIir_1_1ChebyshevI_1_1BandPassBase}{Band\+Pass\+Base}}
\item 
struct \mbox{\hyperlink{structIir_1_1ChebyshevI_1_1BandStopBase}{Band\+Stop\+Base}}
\item 
struct \mbox{\hyperlink{structIir_1_1ChebyshevI_1_1LowShelfBase}{Low\+Shelf\+Base}}
\item 
struct \mbox{\hyperlink{structIir_1_1ChebyshevI_1_1HighShelfBase}{High\+Shelf\+Base}}
\item 
struct \mbox{\hyperlink{structIir_1_1ChebyshevI_1_1BandShelfBase}{Band\+Shelf\+Base}}
\item 
struct \mbox{\hyperlink{structIir_1_1ChebyshevI_1_1LowPass}{Low\+Pass}}
\item 
struct \mbox{\hyperlink{structIir_1_1ChebyshevI_1_1HighPass}{High\+Pass}}
\item 
struct \mbox{\hyperlink{structIir_1_1ChebyshevI_1_1BandPass}{Band\+Pass}}
\item 
struct \mbox{\hyperlink{structIir_1_1ChebyshevI_1_1BandStop}{Band\+Stop}}
\item 
struct \mbox{\hyperlink{structIir_1_1ChebyshevI_1_1LowShelf}{Low\+Shelf}}
\item 
struct \mbox{\hyperlink{structIir_1_1ChebyshevI_1_1HighShelf}{High\+Shelf}}
\item 
struct \mbox{\hyperlink{structIir_1_1ChebyshevI_1_1BandShelf}{Band\+Shelf}}
\end{DoxyCompactItemize}


\doxysubsubsection{Detailed Description}
Filters with Chebyshev response characteristics. The last parameter defines the passband ripple in decibel. 