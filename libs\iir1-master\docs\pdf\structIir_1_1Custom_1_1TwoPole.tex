\hypertarget{structIir_1_1Custom_1_1TwoPole}{}\doxysubsection{Iir\+::Custom\+::Two\+Pole Struct Reference}
\label{structIir_1_1Custom_1_1TwoPole}\index{Iir::Custom::TwoPole@{Iir::Custom::TwoPole}}


{\ttfamily \#include $<$Custom.\+h$>$}

Inheritance diagram for Iir\+::Custom\+::Two\+Pole\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=2.000000cm]{structIir_1_1Custom_1_1TwoPole}
\end{center}
\end{figure}
\doxysubsubsection*{Additional Inherited Members}


\doxysubsubsection{Detailed Description}
Set a pole/zero pair in polar coordinates and scale the FIR filter coefficients 
\begin{DoxyParams}{Parameters}
{\em pole\+Rho} & Radius of the pole \\
\hline
{\em pole\+Theta} & Angle of the pole \\
\hline
{\em zero\+Rho} & Radius of the zero \\
\hline
{\em zero\+Theta} & Angle of the zero \\
\hline
\end{DoxyParams}


The documentation for this struct was generated from the following files\+:\begin{DoxyCompactItemize}
\item 
iir/Custom.\+h\item 
iir/Custom.\+cpp\end{DoxyCompactItemize}
