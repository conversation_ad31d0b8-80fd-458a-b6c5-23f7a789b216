\hypertarget{classIir_1_1LayoutBase}{}\doxysubsection{Iir\+::Layout\+Base Class Reference}
\label{classIir_1_1LayoutBase}\index{Iir::LayoutBase@{Iir::LayoutBase}}


{\ttfamily \#include $<$Layout.\+h$>$}

Inheritance diagram for Iir\+::Layout\+Base\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=0.910569cm]{classIir_1_1LayoutBase}
\end{center}
\end{figure}


\doxysubsubsection{Detailed Description}
Base uses pointers to reduce template instantiations 

The documentation for this class was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
iir/Layout.\+h\end{DoxyCompactItemize}
