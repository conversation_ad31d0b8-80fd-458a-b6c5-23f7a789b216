\hypertarget{classIir_1_1Butterworth_1_1AnalogLowShelf}{}\doxysubsection{Iir\+::Butter<PERSON>\+::Analog\+Low\+Shelf Class Reference}
\label{classIir_1_1Butterworth_1_1AnalogLowShelf}\index{Iir::Butterworth::AnalogLowShelf@{Iir::Butterworth::AnalogLowShelf}}


{\ttfamily \#include $<$Butterworth.\+h$>$}

Inheritance diagram for Iir\+::Butterworth\+::Analog\+Low\+Shelf\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=2.000000cm]{classIir_1_1Butterworth_1_1AnalogLowShelf}
\end{center}
\end{figure}


\doxysubsubsection{Detailed Description}
Analogue low shelf prototypes (s-\/plane) 

The documentation for this class was generated from the following files\+:\begin{DoxyCompactItemize}
\item 
iir/Butterworth.\+h\item 
iir/Butterworth.\+cpp\end{DoxyCompactItemize}
