\hypertarget{structIir_1_1Cascade_1_1Storage}{}\doxysubsection{Iir\+::Cascade\+::Storage Struct Reference}
\label{structIir_1_1Cascade_1_1Storage}\index{Iir::Cascade::Storage@{Iir::Cascade::Storage}}


{\ttfamily \#include $<$Cascade.\+h$>$}



\doxysubsubsection{Detailed Description}
To return the array from a function and to set it. Transmits number of stages and the pointer to the array. 

The documentation for this struct was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
iir/Cascade.\+h\end{DoxyCompactItemize}
