\hypertarget{classIir_1_1ChebyshevI_1_1AnalogLowPass}{}\doxysubsection{Iir\+::ChebyshevI\+::Analog\+Low\+Pass Class Reference}
\label{classIir_1_1ChebyshevI_1_1AnalogLowPass}\index{Iir::ChebyshevI::AnalogLowPass@{Iir::ChebyshevI::AnalogLowPass}}


{\ttfamily \#include $<$Chebyshev\+I.\+h$>$}

Inheritance diagram for Iir\+::ChebyshevI\+::Analog\+Low\+Pass\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=2.000000cm]{classIir_1_1ChebyshevI_1_1AnalogLowPass}
\end{center}
\end{figure}


\doxysubsubsection{Detailed Description}
Analog lowpass prototypes (s-\/plane) 

The documentation for this class was generated from the following files\+:\begin{DoxyCompactItemize}
\item 
iir/Chebyshev\+I.\+h\item 
iir/Chebyshev\+I.\+cpp\end{DoxyCompactItemize}
