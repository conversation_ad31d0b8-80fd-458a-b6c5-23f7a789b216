/********************************************************************************
** Form generated from reading UI file 'frequencyresponseone.ui'
**
** Created by: Qt User Interface Compiler version 5.15.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_FREQUENCYRESPONSEONE_H
#define UI_FREQUENCYRESPONSEONE_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QCheckBox>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QDoubleSpinBox>
#include <QtWidgets/QFrame>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QRadioButton>
#include <QtWidgets/QSpinBox>
#include <QtWidgets/QTabWidget>
#include <QtWidgets/QTableWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_FrequencyResponseOne
{
public:
    QPushButton *adjust;
    QPushButton *test_3;
    QTabWidget *tabWidget;
    QWidget *hardware_set;
    QGroupBox *groupBox_4;
    QRadioButton *universal;
    QRadioButton *referece_rb;
    QLabel *label_17;
    QLabel *outPass_lb;
    QComboBox *outPass;
    QLabel *inPass_lb;
    QComboBox *inPass;
    QLabel *referece_lb;
    QComboBox *referece;
    QLabel *stim_set_lb;
    QLabel *inGain_lb;
    QComboBox *stim_set;
    QComboBox *inGain;
    QLabel *sensors_lb;
    QComboBox *sensors;
    QWidget *test_set;
    QLabel *label_24;
    QComboBox *stim_signal;
    QCheckBox *checkBox_9;
    QLabel *label_25;
    QDoubleSpinBox *doubleSpinBox_5;
    QDoubleSpinBox *doubleSpinBox_6;
    QLabel *label_26;
    QFrame *line_5;
    QLabel *label_27;
    QComboBox *comboBox_10;
    QCheckBox *checkBox_10;
    QLabel *label_28;
    QSpinBox *spinBox_7;
    QCheckBox *checkBox_11;
    QCheckBox *checkBox_12;
    QLabel *label_29;
    QComboBox *comboBox_11;
    QWidget *limit_set;
    QVBoxLayout *verticalLayout_5;
    QTableWidget *tableWidget_2;
    QWidget *manifest_set;
    QVBoxLayout *verticalLayout;
    QTableWidget *tableWidget;
    QWidget *wave_set;
    QLabel *label_30;
    QComboBox *comboBox_12;
    QPushButton *pushButton;
    QPushButton *pushButton_2;
    QLabel *label_31;
    QLineEdit *lineEdit;
    QLabel *label_32;
    QLineEdit *lineEdit_2;
    QFrame *line_6;
    QLabel *label_33;
    QCheckBox *checkBox_13;
    QCheckBox *checkBox_14;
    QLabel *label_34;
    QLabel *label_35;
    QLabel *label_36;
    QSpinBox *spinBox_8;
    QSpinBox *spinBox_9;
    QSpinBox *spinBox_10;
    QWidget *inOut_set;
    QCheckBox *checkBox;
    QLabel *label;
    QDoubleSpinBox *doubleSpinBox;
    QLabel *label_2;
    QDoubleSpinBox *doubleSpinBox_2;
    QLabel *label_3;
    QComboBox *comboBox;
    QCheckBox *checkBox_2;
    QLabel *label_4;
    QSpinBox *spinBox;
    QWidget *analysis_set;
    QLabel *label_5;
    QLabel *label_6;
    QLabel *label_7;
    QSpinBox *spinBox_2;
    QSpinBox *spinBox_3;
    QCheckBox *checkBox_3;
    QCheckBox *checkBox_4;
    QFrame *line;
    QFrame *line_2;
    QGroupBox *groupBox_2;
    QWidget *verticalLayoutWidget_2;
    QVBoxLayout *verticalLayout_2;
    QRadioButton *radioButton_3;
    QRadioButton *radioButton_4;
    QGroupBox *groupBox;
    QWidget *verticalLayoutWidget_3;
    QVBoxLayout *verticalLayout_3;
    QRadioButton *radioButton_5;
    QRadioButton *radioButton_6;
    QLabel *label_15;
    QLabel *label_16;
    QGroupBox *groupBox_3;
    QWidget *verticalLayoutWidget_4;
    QVBoxLayout *verticalLayout_4;
    QRadioButton *radioButton_7;
    QRadioButton *radioButton_8;

    void setupUi(QWidget *FrequencyResponseOne)
    {
        if (FrequencyResponseOne->objectName().isEmpty())
            FrequencyResponseOne->setObjectName(QString::fromUtf8("FrequencyResponseOne"));
        FrequencyResponseOne->resize(596, 519);
        adjust = new QPushButton(FrequencyResponseOne);
        adjust->setObjectName(QString::fromUtf8("adjust"));
        adjust->setGeometry(QRect(50, 400, 81, 41));
        test_3 = new QPushButton(FrequencyResponseOne);
        test_3->setObjectName(QString::fromUtf8("test_3"));
        test_3->setGeometry(QRect(160, 400, 81, 41));
        tabWidget = new QTabWidget(FrequencyResponseOne);
        tabWidget->setObjectName(QString::fromUtf8("tabWidget"));
        tabWidget->setGeometry(QRect(10, 0, 521, 381));
        tabWidget->setLayoutDirection(Qt::LeftToRight);
        tabWidget->setTabPosition(QTabWidget::North);
        tabWidget->setUsesScrollButtons(true);
        hardware_set = new QWidget();
        hardware_set->setObjectName(QString::fromUtf8("hardware_set"));
        groupBox_4 = new QGroupBox(hardware_set);
        groupBox_4->setObjectName(QString::fromUtf8("groupBox_4"));
        groupBox_4->setGeometry(QRect(20, 40, 151, 41));
        universal = new QRadioButton(groupBox_4);
        universal->setObjectName(QString::fromUtf8("universal"));
        universal->setGeometry(QRect(10, 10, 89, 16));
        universal->setChecked(true);
        referece_rb = new QRadioButton(groupBox_4);
        referece_rb->setObjectName(QString::fromUtf8("referece_rb"));
        referece_rb->setGeometry(QRect(60, 10, 89, 16));
        label_17 = new QLabel(hardware_set);
        label_17->setObjectName(QString::fromUtf8("label_17"));
        label_17->setGeometry(QRect(30, 10, 81, 16));
        outPass_lb = new QLabel(hardware_set);
        outPass_lb->setObjectName(QString::fromUtf8("outPass_lb"));
        outPass_lb->setGeometry(QRect(30, 90, 61, 16));
        outPass = new QComboBox(hardware_set);
        outPass->addItem(QString());
        outPass->setObjectName(QString::fromUtf8("outPass"));
        outPass->setGeometry(QRect(30, 111, 69, 21));
        inPass_lb = new QLabel(hardware_set);
        inPass_lb->setObjectName(QString::fromUtf8("inPass_lb"));
        inPass_lb->setGeometry(QRect(30, 140, 61, 16));
        inPass = new QComboBox(hardware_set);
        inPass->setObjectName(QString::fromUtf8("inPass"));
        inPass->setGeometry(QRect(30, 170, 69, 22));
        referece_lb = new QLabel(hardware_set);
        referece_lb->setObjectName(QString::fromUtf8("referece_lb"));
        referece_lb->setGeometry(QRect(30, 200, 61, 16));
        referece = new QComboBox(hardware_set);
        referece->setObjectName(QString::fromUtf8("referece"));
        referece->setGeometry(QRect(30, 220, 161, 22));
        stim_set_lb = new QLabel(hardware_set);
        stim_set_lb->setObjectName(QString::fromUtf8("stim_set_lb"));
        stim_set_lb->setGeometry(QRect(170, 90, 61, 16));
        inGain_lb = new QLabel(hardware_set);
        inGain_lb->setObjectName(QString::fromUtf8("inGain_lb"));
        inGain_lb->setGeometry(QRect(170, 146, 91, 20));
        stim_set = new QComboBox(hardware_set);
        stim_set->setObjectName(QString::fromUtf8("stim_set"));
        stim_set->setGeometry(QRect(170, 110, 69, 22));
        inGain = new QComboBox(hardware_set);
        inGain->setObjectName(QString::fromUtf8("inGain"));
        inGain->setGeometry(QRect(170, 170, 69, 22));
        sensors_lb = new QLabel(hardware_set);
        sensors_lb->setObjectName(QString::fromUtf8("sensors_lb"));
        sensors_lb->setGeometry(QRect(330, 150, 61, 16));
        sensors = new QComboBox(hardware_set);
        sensors->setObjectName(QString::fromUtf8("sensors"));
        sensors->setGeometry(QRect(330, 170, 69, 22));
        tabWidget->addTab(hardware_set, QString());
        test_set = new QWidget();
        test_set->setObjectName(QString::fromUtf8("test_set"));
        label_24 = new QLabel(test_set);
        label_24->setObjectName(QString::fromUtf8("label_24"));
        label_24->setGeometry(QRect(20, 10, 81, 16));
        stim_signal = new QComboBox(test_set);
        stim_signal->setObjectName(QString::fromUtf8("stim_signal"));
        stim_signal->setGeometry(QRect(20, 30, 69, 22));
        checkBox_9 = new QCheckBox(test_set);
        checkBox_9->setObjectName(QString::fromUtf8("checkBox_9"));
        checkBox_9->setGeometry(QRect(20, 70, 91, 21));
        label_25 = new QLabel(test_set);
        label_25->setObjectName(QString::fromUtf8("label_25"));
        label_25->setGeometry(QRect(20, 110, 81, 16));
        doubleSpinBox_5 = new QDoubleSpinBox(test_set);
        doubleSpinBox_5->setObjectName(QString::fromUtf8("doubleSpinBox_5"));
        doubleSpinBox_5->setGeometry(QRect(20, 140, 71, 22));
        doubleSpinBox_6 = new QDoubleSpinBox(test_set);
        doubleSpinBox_6->setObjectName(QString::fromUtf8("doubleSpinBox_6"));
        doubleSpinBox_6->setGeometry(QRect(20, 190, 62, 22));
        label_26 = new QLabel(test_set);
        label_26->setObjectName(QString::fromUtf8("label_26"));
        label_26->setGeometry(QRect(20, 170, 81, 16));
        line_5 = new QFrame(test_set);
        line_5->setObjectName(QString::fromUtf8("line_5"));
        line_5->setGeometry(QRect(250, 10, 16, 271));
        line_5->setFrameShape(QFrame::VLine);
        line_5->setFrameShadow(QFrame::Sunken);
        label_27 = new QLabel(test_set);
        label_27->setObjectName(QString::fromUtf8("label_27"));
        label_27->setGeometry(QRect(290, 10, 81, 16));
        comboBox_10 = new QComboBox(test_set);
        comboBox_10->setObjectName(QString::fromUtf8("comboBox_10"));
        comboBox_10->setGeometry(QRect(290, 30, 69, 22));
        checkBox_10 = new QCheckBox(test_set);
        checkBox_10->setObjectName(QString::fromUtf8("checkBox_10"));
        checkBox_10->setGeometry(QRect(290, 70, 71, 16));
        label_28 = new QLabel(test_set);
        label_28->setObjectName(QString::fromUtf8("label_28"));
        label_28->setGeometry(QRect(290, 100, 81, 16));
        spinBox_7 = new QSpinBox(test_set);
        spinBox_7->setObjectName(QString::fromUtf8("spinBox_7"));
        spinBox_7->setGeometry(QRect(290, 120, 111, 22));
        checkBox_11 = new QCheckBox(test_set);
        checkBox_11->setObjectName(QString::fromUtf8("checkBox_11"));
        checkBox_11->setGeometry(QRect(290, 160, 71, 16));
        checkBox_12 = new QCheckBox(test_set);
        checkBox_12->setObjectName(QString::fromUtf8("checkBox_12"));
        checkBox_12->setGeometry(QRect(290, 180, 81, 21));
        label_29 = new QLabel(test_set);
        label_29->setObjectName(QString::fromUtf8("label_29"));
        label_29->setGeometry(QRect(290, 220, 81, 16));
        comboBox_11 = new QComboBox(test_set);
        comboBox_11->setObjectName(QString::fromUtf8("comboBox_11"));
        comboBox_11->setGeometry(QRect(290, 250, 69, 22));
        tabWidget->addTab(test_set, QString());
        limit_set = new QWidget();
        limit_set->setObjectName(QString::fromUtf8("limit_set"));
        verticalLayout_5 = new QVBoxLayout(limit_set);
        verticalLayout_5->setObjectName(QString::fromUtf8("verticalLayout_5"));
        tableWidget_2 = new QTableWidget(limit_set);
        if (tableWidget_2->columnCount() < 2)
            tableWidget_2->setColumnCount(2);
        QTableWidgetItem *__qtablewidgetitem = new QTableWidgetItem();
        tableWidget_2->setHorizontalHeaderItem(0, __qtablewidgetitem);
        QTableWidgetItem *__qtablewidgetitem1 = new QTableWidgetItem();
        tableWidget_2->setHorizontalHeaderItem(1, __qtablewidgetitem1);
        if (tableWidget_2->rowCount() < 6)
            tableWidget_2->setRowCount(6);
        QTableWidgetItem *__qtablewidgetitem2 = new QTableWidgetItem();
        tableWidget_2->setVerticalHeaderItem(0, __qtablewidgetitem2);
        QTableWidgetItem *__qtablewidgetitem3 = new QTableWidgetItem();
        tableWidget_2->setVerticalHeaderItem(1, __qtablewidgetitem3);
        QTableWidgetItem *__qtablewidgetitem4 = new QTableWidgetItem();
        tableWidget_2->setVerticalHeaderItem(2, __qtablewidgetitem4);
        QTableWidgetItem *__qtablewidgetitem5 = new QTableWidgetItem();
        tableWidget_2->setVerticalHeaderItem(3, __qtablewidgetitem5);
        QTableWidgetItem *__qtablewidgetitem6 = new QTableWidgetItem();
        tableWidget_2->setVerticalHeaderItem(4, __qtablewidgetitem6);
        QTableWidgetItem *__qtablewidgetitem7 = new QTableWidgetItem();
        tableWidget_2->setVerticalHeaderItem(5, __qtablewidgetitem7);
        tableWidget_2->setObjectName(QString::fromUtf8("tableWidget_2"));
        tableWidget_2->horizontalHeader()->setDefaultSectionSize(200);
        tableWidget_2->horizontalHeader()->setProperty("showSortIndicator", QVariant(false));
        tableWidget_2->horizontalHeader()->setStretchLastSection(true);

        verticalLayout_5->addWidget(tableWidget_2);

        tabWidget->addTab(limit_set, QString());
        manifest_set = new QWidget();
        manifest_set->setObjectName(QString::fromUtf8("manifest_set"));
        verticalLayout = new QVBoxLayout(manifest_set);
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        tableWidget = new QTableWidget(manifest_set);
        if (tableWidget->columnCount() < 2)
            tableWidget->setColumnCount(2);
        QTableWidgetItem *__qtablewidgetitem8 = new QTableWidgetItem();
        tableWidget->setHorizontalHeaderItem(0, __qtablewidgetitem8);
        QTableWidgetItem *__qtablewidgetitem9 = new QTableWidgetItem();
        tableWidget->setHorizontalHeaderItem(1, __qtablewidgetitem9);
        if (tableWidget->rowCount() < 6)
            tableWidget->setRowCount(6);
        QTableWidgetItem *__qtablewidgetitem10 = new QTableWidgetItem();
        tableWidget->setVerticalHeaderItem(0, __qtablewidgetitem10);
        QTableWidgetItem *__qtablewidgetitem11 = new QTableWidgetItem();
        tableWidget->setVerticalHeaderItem(1, __qtablewidgetitem11);
        QTableWidgetItem *__qtablewidgetitem12 = new QTableWidgetItem();
        tableWidget->setVerticalHeaderItem(2, __qtablewidgetitem12);
        QTableWidgetItem *__qtablewidgetitem13 = new QTableWidgetItem();
        tableWidget->setVerticalHeaderItem(3, __qtablewidgetitem13);
        QTableWidgetItem *__qtablewidgetitem14 = new QTableWidgetItem();
        tableWidget->setVerticalHeaderItem(4, __qtablewidgetitem14);
        QTableWidgetItem *__qtablewidgetitem15 = new QTableWidgetItem();
        tableWidget->setVerticalHeaderItem(5, __qtablewidgetitem15);
        QTableWidgetItem *__qtablewidgetitem16 = new QTableWidgetItem();
        __qtablewidgetitem16->setCheckState(Qt::Unchecked);
        tableWidget->setItem(0, 0, __qtablewidgetitem16);
        QTableWidgetItem *__qtablewidgetitem17 = new QTableWidgetItem();
        __qtablewidgetitem17->setTextAlignment(Qt::AlignJustify|Qt::AlignVCenter);
        tableWidget->setItem(0, 1, __qtablewidgetitem17);
        QTableWidgetItem *__qtablewidgetitem18 = new QTableWidgetItem();
        __qtablewidgetitem18->setCheckState(Qt::Unchecked);
        tableWidget->setItem(1, 0, __qtablewidgetitem18);
        tableWidget->setObjectName(QString::fromUtf8("tableWidget"));
        tableWidget->horizontalHeader()->setDefaultSectionSize(200);
        tableWidget->horizontalHeader()->setProperty("showSortIndicator", QVariant(true));
        tableWidget->horizontalHeader()->setStretchLastSection(true);

        verticalLayout->addWidget(tableWidget);

        tabWidget->addTab(manifest_set, QString());
        wave_set = new QWidget();
        wave_set->setObjectName(QString::fromUtf8("wave_set"));
        label_30 = new QLabel(wave_set);
        label_30->setObjectName(QString::fromUtf8("label_30"));
        label_30->setGeometry(QRect(30, 20, 54, 12));
        comboBox_12 = new QComboBox(wave_set);
        comboBox_12->setObjectName(QString::fromUtf8("comboBox_12"));
        comboBox_12->setGeometry(QRect(30, 50, 69, 22));
        pushButton = new QPushButton(wave_set);
        pushButton->setObjectName(QString::fromUtf8("pushButton"));
        pushButton->setGeometry(QRect(30, 90, 101, 23));
        pushButton_2 = new QPushButton(wave_set);
        pushButton_2->setObjectName(QString::fromUtf8("pushButton_2"));
        pushButton_2->setGeometry(QRect(30, 130, 81, 31));
        label_31 = new QLabel(wave_set);
        label_31->setObjectName(QString::fromUtf8("label_31"));
        label_31->setGeometry(QRect(280, 20, 81, 16));
        lineEdit = new QLineEdit(wave_set);
        lineEdit->setObjectName(QString::fromUtf8("lineEdit"));
        lineEdit->setGeometry(QRect(270, 60, 113, 20));
        label_32 = new QLabel(wave_set);
        label_32->setObjectName(QString::fromUtf8("label_32"));
        label_32->setGeometry(QRect(270, 100, 101, 16));
        lineEdit_2 = new QLineEdit(wave_set);
        lineEdit_2->setObjectName(QString::fromUtf8("lineEdit_2"));
        lineEdit_2->setGeometry(QRect(270, 130, 113, 20));
        line_6 = new QFrame(wave_set);
        line_6->setObjectName(QString::fromUtf8("line_6"));
        line_6->setGeometry(QRect(20, 170, 521, 16));
        line_6->setFrameShape(QFrame::HLine);
        line_6->setFrameShadow(QFrame::Sunken);
        label_33 = new QLabel(wave_set);
        label_33->setObjectName(QString::fromUtf8("label_33"));
        label_33->setGeometry(QRect(30, 210, 81, 16));
        checkBox_13 = new QCheckBox(wave_set);
        checkBox_13->setObjectName(QString::fromUtf8("checkBox_13"));
        checkBox_13->setGeometry(QRect(30, 240, 71, 16));
        checkBox_14 = new QCheckBox(wave_set);
        checkBox_14->setObjectName(QString::fromUtf8("checkBox_14"));
        checkBox_14->setGeometry(QRect(30, 270, 71, 16));
        label_34 = new QLabel(wave_set);
        label_34->setObjectName(QString::fromUtf8("label_34"));
        label_34->setGeometry(QRect(270, 200, 54, 12));
        label_35 = new QLabel(wave_set);
        label_35->setObjectName(QString::fromUtf8("label_35"));
        label_35->setGeometry(QRect(270, 250, 54, 12));
        label_36 = new QLabel(wave_set);
        label_36->setObjectName(QString::fromUtf8("label_36"));
        label_36->setGeometry(QRect(270, 290, 181, 16));
        spinBox_8 = new QSpinBox(wave_set);
        spinBox_8->setObjectName(QString::fromUtf8("spinBox_8"));
        spinBox_8->setGeometry(QRect(270, 220, 121, 22));
        spinBox_9 = new QSpinBox(wave_set);
        spinBox_9->setObjectName(QString::fromUtf8("spinBox_9"));
        spinBox_9->setGeometry(QRect(270, 260, 121, 22));
        spinBox_10 = new QSpinBox(wave_set);
        spinBox_10->setObjectName(QString::fromUtf8("spinBox_10"));
        spinBox_10->setGeometry(QRect(270, 310, 121, 22));
        tabWidget->addTab(wave_set, QString());
        inOut_set = new QWidget();
        inOut_set->setObjectName(QString::fromUtf8("inOut_set"));
        checkBox = new QCheckBox(inOut_set);
        checkBox->setObjectName(QString::fromUtf8("checkBox"));
        checkBox->setGeometry(QRect(20, 0, 131, 21));
        label = new QLabel(inOut_set);
        label->setObjectName(QString::fromUtf8("label"));
        label->setGeometry(QRect(30, 70, 71, 31));
        doubleSpinBox = new QDoubleSpinBox(inOut_set);
        doubleSpinBox->setObjectName(QString::fromUtf8("doubleSpinBox"));
        doubleSpinBox->setGeometry(QRect(30, 100, 71, 21));
        label_2 = new QLabel(inOut_set);
        label_2->setObjectName(QString::fromUtf8("label_2"));
        label_2->setGeometry(QRect(30, 130, 71, 16));
        doubleSpinBox_2 = new QDoubleSpinBox(inOut_set);
        doubleSpinBox_2->setObjectName(QString::fromUtf8("doubleSpinBox_2"));
        doubleSpinBox_2->setGeometry(QRect(30, 160, 71, 21));
        label_3 = new QLabel(inOut_set);
        label_3->setObjectName(QString::fromUtf8("label_3"));
        label_3->setGeometry(QRect(30, 190, 71, 16));
        comboBox = new QComboBox(inOut_set);
        comboBox->addItem(QString());
        comboBox->addItem(QString());
        comboBox->setObjectName(QString::fromUtf8("comboBox"));
        comboBox->setGeometry(QRect(30, 220, 69, 22));
        checkBox_2 = new QCheckBox(inOut_set);
        checkBox_2->setObjectName(QString::fromUtf8("checkBox_2"));
        checkBox_2->setGeometry(QRect(30, 260, 171, 21));
        label_4 = new QLabel(inOut_set);
        label_4->setObjectName(QString::fromUtf8("label_4"));
        label_4->setGeometry(QRect(30, 30, 101, 21));
        spinBox = new QSpinBox(inOut_set);
        spinBox->setObjectName(QString::fromUtf8("spinBox"));
        spinBox->setGeometry(QRect(30, 50, 71, 22));
        tabWidget->addTab(inOut_set, QString());
        analysis_set = new QWidget();
        analysis_set->setObjectName(QString::fromUtf8("analysis_set"));
        label_5 = new QLabel(analysis_set);
        label_5->setObjectName(QString::fromUtf8("label_5"));
        label_5->setGeometry(QRect(30, 10, 61, 21));
        label_6 = new QLabel(analysis_set);
        label_6->setObjectName(QString::fromUtf8("label_6"));
        label_6->setGeometry(QRect(30, 130, 54, 12));
        label_7 = new QLabel(analysis_set);
        label_7->setObjectName(QString::fromUtf8("label_7"));
        label_7->setGeometry(QRect(30, 190, 101, 16));
        spinBox_2 = new QSpinBox(analysis_set);
        spinBox_2->setObjectName(QString::fromUtf8("spinBox_2"));
        spinBox_2->setGeometry(QRect(30, 150, 81, 22));
        spinBox_3 = new QSpinBox(analysis_set);
        spinBox_3->setObjectName(QString::fromUtf8("spinBox_3"));
        spinBox_3->setGeometry(QRect(30, 210, 81, 22));
        checkBox_3 = new QCheckBox(analysis_set);
        checkBox_3->setObjectName(QString::fromUtf8("checkBox_3"));
        checkBox_3->setGeometry(QRect(30, 240, 231, 31));
        checkBox_4 = new QCheckBox(analysis_set);
        checkBox_4->setObjectName(QString::fromUtf8("checkBox_4"));
        checkBox_4->setGeometry(QRect(30, 270, 151, 21));
        line = new QFrame(analysis_set);
        line->setObjectName(QString::fromUtf8("line"));
        line->setGeometry(QRect(240, 30, 16, 191));
        line->setFrameShape(QFrame::VLine);
        line->setFrameShadow(QFrame::Sunken);
        line_2 = new QFrame(analysis_set);
        line_2->setObjectName(QString::fromUtf8("line_2"));
        line_2->setGeometry(QRect(20, 180, 201, 16));
        line_2->setFrameShape(QFrame::HLine);
        line_2->setFrameShadow(QFrame::Sunken);
        groupBox_2 = new QGroupBox(analysis_set);
        groupBox_2->setObjectName(QString::fromUtf8("groupBox_2"));
        groupBox_2->setGeometry(QRect(30, 30, 131, 81));
        verticalLayoutWidget_2 = new QWidget(groupBox_2);
        verticalLayoutWidget_2->setObjectName(QString::fromUtf8("verticalLayoutWidget_2"));
        verticalLayoutWidget_2->setGeometry(QRect(10, 10, 111, 61));
        verticalLayout_2 = new QVBoxLayout(verticalLayoutWidget_2);
        verticalLayout_2->setSpacing(6);
        verticalLayout_2->setObjectName(QString::fromUtf8("verticalLayout_2"));
        verticalLayout_2->setContentsMargins(0, 0, 0, 0);
        radioButton_3 = new QRadioButton(verticalLayoutWidget_2);
        radioButton_3->setObjectName(QString::fromUtf8("radioButton_3"));

        verticalLayout_2->addWidget(radioButton_3);

        radioButton_4 = new QRadioButton(verticalLayoutWidget_2);
        radioButton_4->setObjectName(QString::fromUtf8("radioButton_4"));
        radioButton_4->setAutoRepeatDelay(300);

        verticalLayout_2->addWidget(radioButton_4);

        groupBox = new QGroupBox(analysis_set);
        groupBox->setObjectName(QString::fromUtf8("groupBox"));
        groupBox->setGeometry(QRect(280, 60, 171, 101));
        verticalLayoutWidget_3 = new QWidget(groupBox);
        verticalLayoutWidget_3->setObjectName(QString::fromUtf8("verticalLayoutWidget_3"));
        verticalLayoutWidget_3->setGeometry(QRect(10, 10, 151, 81));
        verticalLayout_3 = new QVBoxLayout(verticalLayoutWidget_3);
        verticalLayout_3->setObjectName(QString::fromUtf8("verticalLayout_3"));
        verticalLayout_3->setContentsMargins(0, 0, 0, 0);
        radioButton_5 = new QRadioButton(verticalLayoutWidget_3);
        radioButton_5->setObjectName(QString::fromUtf8("radioButton_5"));

        verticalLayout_3->addWidget(radioButton_5);

        radioButton_6 = new QRadioButton(verticalLayoutWidget_3);
        radioButton_6->setObjectName(QString::fromUtf8("radioButton_6"));

        verticalLayout_3->addWidget(radioButton_6);

        label_15 = new QLabel(analysis_set);
        label_15->setObjectName(QString::fromUtf8("label_15"));
        label_15->setGeometry(QRect(290, 10, 91, 21));
        label_16 = new QLabel(analysis_set);
        label_16->setObjectName(QString::fromUtf8("label_16"));
        label_16->setGeometry(QRect(290, 170, 151, 21));
        groupBox_3 = new QGroupBox(analysis_set);
        groupBox_3->setObjectName(QString::fromUtf8("groupBox_3"));
        groupBox_3->setGeometry(QRect(310, 210, 171, 80));
        verticalLayoutWidget_4 = new QWidget(groupBox_3);
        verticalLayoutWidget_4->setObjectName(QString::fromUtf8("verticalLayoutWidget_4"));
        verticalLayoutWidget_4->setGeometry(QRect(20, 10, 81, 61));
        verticalLayout_4 = new QVBoxLayout(verticalLayoutWidget_4);
        verticalLayout_4->setObjectName(QString::fromUtf8("verticalLayout_4"));
        verticalLayout_4->setContentsMargins(0, 0, 0, 0);
        radioButton_7 = new QRadioButton(verticalLayoutWidget_4);
        radioButton_7->setObjectName(QString::fromUtf8("radioButton_7"));

        verticalLayout_4->addWidget(radioButton_7);

        radioButton_8 = new QRadioButton(verticalLayoutWidget_4);
        radioButton_8->setObjectName(QString::fromUtf8("radioButton_8"));

        verticalLayout_4->addWidget(radioButton_8);

        tabWidget->addTab(analysis_set, QString());

        retranslateUi(FrequencyResponseOne);

        tabWidget->setCurrentIndex(0);


        QMetaObject::connectSlotsByName(FrequencyResponseOne);
    } // setupUi

    void retranslateUi(QWidget *FrequencyResponseOne)
    {
        FrequencyResponseOne->setWindowTitle(QCoreApplication::translate("FrequencyResponseOne", "Form", nullptr));
        adjust->setText(QCoreApplication::translate("FrequencyResponseOne", "\346\211\213\345\212\250\346\265\213\350\257\225", nullptr));
        test_3->setText(QCoreApplication::translate("FrequencyResponseOne", "\351\207\221\346\234\272\346\240\241\345\207\206", nullptr));
        groupBox_4->setTitle(QString());
        universal->setText(QCoreApplication::translate("FrequencyResponseOne", "\351\200\232\347\224\250", nullptr));
        referece_rb->setText(QCoreApplication::translate("FrequencyResponseOne", "\345\217\202\346\225\260\347\273\204", nullptr));
        label_17->setText(QCoreApplication::translate("FrequencyResponseOne", "\347\241\254\344\273\266\345\217\202\346\225\260\347\261\273\345\236\213", nullptr));
        outPass_lb->setText(QCoreApplication::translate("FrequencyResponseOne", "\350\276\223\345\207\272\351\200\232\351\201\223", nullptr));
        outPass->setItemText(0, QString());

        inPass_lb->setText(QCoreApplication::translate("FrequencyResponseOne", "\350\276\223\345\205\245\351\200\232\351\201\223", nullptr));
        referece_lb->setText(QCoreApplication::translate("FrequencyResponseOne", "\345\217\202\346\225\260\347\273\204", nullptr));
        stim_set_lb->setText(QCoreApplication::translate("FrequencyResponseOne", "\346\277\200\345\212\261\350\256\276\345\244\207", nullptr));
        inGain_lb->setText(QCoreApplication::translate("FrequencyResponseOne", "\350\276\223\345\205\245\345\242\236\347\233\212(dB)", nullptr));
        sensors_lb->setText(QCoreApplication::translate("FrequencyResponseOne", "\344\274\240\346\204\237\345\231\250", nullptr));
        tabWidget->setTabText(tabWidget->indexOf(hardware_set), QCoreApplication::translate("FrequencyResponseOne", "\347\241\254\344\273\266\350\256\276\347\275\256", nullptr));
        label_24->setText(QCoreApplication::translate("FrequencyResponseOne", "\346\277\200\345\212\261\344\277\241\345\217\267", nullptr));
        checkBox_9->setText(QCoreApplication::translate("FrequencyResponseOne", "\351\242\204\346\222\255\345\231\252\345\243\260?", nullptr));
        label_25->setText(QCoreApplication::translate("FrequencyResponseOne", "\345\231\252\345\243\260\345\271\205\345\200\274(V)", nullptr));
        label_26->setText(QCoreApplication::translate("FrequencyResponseOne", "\345\231\252\345\243\260\346\227\266\351\225\277(S)", nullptr));
        label_27->setText(QCoreApplication::translate("FrequencyResponseOne", "\351\207\221\346\234\272\346\240\267\346\234\254", nullptr));
        checkBox_10->setText(QCoreApplication::translate("FrequencyResponseOne", "\346\267\273\345\212\240\350\241\245\345\201\277?", nullptr));
        label_28->setText(QCoreApplication::translate("FrequencyResponseOne", "\346\265\213\351\207\217\346\254\241\346\225\260", nullptr));
        checkBox_11->setText(QCoreApplication::translate("FrequencyResponseOne", "\346\230\276\347\244\272\344\277\241\345\217\267", nullptr));
        checkBox_12->setText(QCoreApplication::translate("FrequencyResponseOne", "\345\220\257\347\224\250\345\271\263\346\273\221?", nullptr));
        label_29->setText(QCoreApplication::translate("FrequencyResponseOne", "\345\271\263\346\273\221\345\256\275\345\272\246", nullptr));
        tabWidget->setTabText(tabWidget->indexOf(test_set), QCoreApplication::translate("FrequencyResponseOne", "\346\265\213\350\257\225\350\256\276\347\275\256", nullptr));
        QTableWidgetItem *___qtablewidgetitem = tableWidget_2->horizontalHeaderItem(0);
        ___qtablewidgetitem->setText(QCoreApplication::translate("FrequencyResponseOne", "\346\236\201\351\231\220\347\212\266\346\200\201", nullptr));
        QTableWidgetItem *___qtablewidgetitem1 = tableWidget_2->horizontalHeaderItem(1);
        ___qtablewidgetitem1->setText(QCoreApplication::translate("FrequencyResponseOne", "\346\236\201\351\231\220\345\220\215\347\247\260", nullptr));
        tabWidget->setTabText(tabWidget->indexOf(limit_set), QCoreApplication::translate("FrequencyResponseOne", "\351\231\220\345\200\274\350\256\276\347\275\256", nullptr));
        QTableWidgetItem *___qtablewidgetitem2 = tableWidget->horizontalHeaderItem(0);
        ___qtablewidgetitem2->setText(QCoreApplication::translate("FrequencyResponseOne", "\346\230\276\347\244\272\350\256\276\345\244\207", nullptr));
#if QT_CONFIG(tooltip)
        ___qtablewidgetitem2->setToolTip(QCoreApplication::translate("FrequencyResponseOne", "ew", nullptr));
#endif // QT_CONFIG(tooltip)
        QTableWidgetItem *___qtablewidgetitem3 = tableWidget->horizontalHeaderItem(1);
        ___qtablewidgetitem3->setText(QCoreApplication::translate("FrequencyResponseOne", "\345\215\225\344\275\215", nullptr));

        const bool __sortingEnabled = tableWidget->isSortingEnabled();
        tableWidget->setSortingEnabled(false);
        QTableWidgetItem *___qtablewidgetitem4 = tableWidget->item(0, 0);
        ___qtablewidgetitem4->setText(QCoreApplication::translate("FrequencyResponseOne", "\346\230\276\347\244\272\351\242\221\345\223\215\347\232\204\346\265\213\350\257\225\347\273\223\346\236\234?", nullptr));
        QTableWidgetItem *___qtablewidgetitem5 = tableWidget->item(1, 0);
        ___qtablewidgetitem5->setText(QCoreApplication::translate("FrequencyResponseOne", "\346\230\276\347\244\272THD\347\232\204\346\265\213\350\257\225\347\273\223\346\236\234\357\274\237", nullptr));
        tableWidget->setSortingEnabled(__sortingEnabled);

        tabWidget->setTabText(tabWidget->indexOf(manifest_set), QCoreApplication::translate("FrequencyResponseOne", "\346\230\276\347\244\272\350\256\276\347\275\256", nullptr));
        label_30->setText(QCoreApplication::translate("FrequencyResponseOne", "\346\226\271\346\263\225", nullptr));
        pushButton->setText(QCoreApplication::translate("FrequencyResponseOne", "THD\350\256\276\347\275\256", nullptr));
        pushButton_2->setText(QCoreApplication::translate("FrequencyResponseOne", "Rub&Buzz \350\256\276\347\275\256", nullptr));
        label_31->setText(QCoreApplication::translate("FrequencyResponseOne", "THD \350\260\220\346\263\242", nullptr));
        label_32->setText(QCoreApplication::translate("FrequencyResponseOne", "Rub&Buzz \350\260\220\346\263\242", nullptr));
        label_33->setText(QCoreApplication::translate("FrequencyResponseOne", "THD+N \350\256\276\347\275\256", nullptr));
        checkBox_13->setText(QCoreApplication::translate("FrequencyResponseOne", "CheckBox", nullptr));
        checkBox_14->setText(QCoreApplication::translate("FrequencyResponseOne", "CheckBox", nullptr));
        label_34->setText(QCoreApplication::translate("FrequencyResponseOne", "\351\253\230\351\200\232\351\242\221\347\216\207", nullptr));
        label_35->setText(QCoreApplication::translate("FrequencyResponseOne", "\344\275\216\351\200\232\351\242\221\347\216\207", nullptr));
        label_36->setText(QCoreApplication::translate("FrequencyResponseOne", "Advanced Span(% of Freq)", nullptr));
        tabWidget->setTabText(tabWidget->indexOf(wave_set), QCoreApplication::translate("FrequencyResponseOne", "\350\260\220\346\263\242\350\256\276\347\275\256", nullptr));
        checkBox->setText(QCoreApplication::translate("FrequencyResponseOne", "\350\276\223\345\205\245\344\275\277\347\224\250\346\214\207\345\256\232\351\207\207\346\240\267\347\216\207", nullptr));
        label->setText(QCoreApplication::translate("FrequencyResponseOne", "\351\207\207\351\233\206\345\241\253\345\205\205(s)", nullptr));
        label_2->setText(QCoreApplication::translate("FrequencyResponseOne", "\351\207\207\351\233\206\345\270\247\351\225\277(s)", nullptr));
        label_3->setText(QCoreApplication::translate("FrequencyResponseOne", "\350\247\246\345\217\221\347\261\273\345\236\213", nullptr));
        comboBox->setItemText(0, QCoreApplication::translate("FrequencyResponseOne", "\350\247\246\345\217\221", nullptr));
        comboBox->setItemText(1, QCoreApplication::translate("FrequencyResponseOne", "\344\270\215\350\247\246\345\217\221", nullptr));

        checkBox_2->setText(QCoreApplication::translate("FrequencyResponseOne", "\345\220\257\347\224\250\346\211\200\346\234\211AO\351\200\232\351\201\223(Shuttle)?", nullptr));
        label_4->setText(QCoreApplication::translate("FrequencyResponseOne", "\350\276\223\345\205\245\346\214\207\345\256\232\351\207\207\346\240\267\347\216\207", nullptr));
        tabWidget->setTabText(tabWidget->indexOf(inOut_set), QCoreApplication::translate("FrequencyResponseOne", "\350\276\223\345\205\245\350\276\223\345\207\272\347\232\204\351\253\230\347\272\247\350\256\276\347\275\256", nullptr));
        label_5->setText(QCoreApplication::translate("FrequencyResponseOne", "\345\271\263\347\247\273\346\226\271\345\274\217", nullptr));
        label_6->setText(QCoreApplication::translate("FrequencyResponseOne", "\345\271\263\347\247\273\347\202\271\346\225\260", nullptr));
        label_7->setText(QCoreApplication::translate("FrequencyResponseOne", "\347\233\270\344\275\215\345\271\263\347\247\273\345\217\202\350\200\203\351\242\221\347\216\207", nullptr));
        checkBox_3->setText(QCoreApplication::translate("FrequencyResponseOne", "\346\277\200\345\212\261\344\277\241\345\217\267\344\270\272\350\277\236\347\273\255\346\211\253\351\242\221\346\227\266\345\220\210\345\271\266\347\273\223\346\236\234\351\242\221\347\216\207\347\202\271", nullptr));
        checkBox_4->setText(QCoreApplication::translate("FrequencyResponseOne", "\347\255\211\345\276\205\347\233\264\350\207\263\345\210\206\346\236\220\345\256\214\346\210\220", nullptr));
        groupBox_2->setTitle(QString());
        radioButton_3->setText(QCoreApplication::translate("FrequencyResponseOne", "\350\207\252\345\212\250\345\271\263\347\247\273", nullptr));
        radioButton_4->setText(QCoreApplication::translate("FrequencyResponseOne", "\345\270\270\346\225\260\345\271\263\347\247\273", nullptr));
        groupBox->setTitle(QString());
        radioButton_5->setText(QCoreApplication::translate("FrequencyResponseOne", "0dB=\346\273\241\351\207\217\347\250\213\346\255\243\345\274\246\346\263\242", nullptr));
        radioButton_6->setText(QCoreApplication::translate("FrequencyResponseOne", "-3dB=\346\273\241\351\207\217\347\250\213\346\255\243\345\274\246\346\263\242\345\271\263\345\235\207", nullptr));
        label_15->setText(QCoreApplication::translate("FrequencyResponseOne", "dBFS\347\232\204\345\217\202\350\200\203", nullptr));
        label_16->setText(QCoreApplication::translate("FrequencyResponseOne", "\350\260\220\346\263\242\351\242\221\347\216\207\350\266\205\350\277\207\345\245\210\345\245\216\346\226\257\347\211\271\351\242\221\347\216\207", nullptr));
        groupBox_3->setTitle(QString());
        radioButton_7->setText(QCoreApplication::translate("FrequencyResponseOne", "\345\277\275\347\225\245", nullptr));
        radioButton_8->setText(QCoreApplication::translate("FrequencyResponseOne", "\346\217\222\345\200\274", nullptr));
        tabWidget->setTabText(tabWidget->indexOf(analysis_set), QCoreApplication::translate("FrequencyResponseOne", "\345\210\206\346\236\220\347\232\204\351\253\230\347\272\247\350\256\276\347\275\256", nullptr));
    } // retranslateUi

};

namespace Ui {
    class FrequencyResponseOne: public Ui_FrequencyResponseOne {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_FREQUENCYRESPONSEONE_H
