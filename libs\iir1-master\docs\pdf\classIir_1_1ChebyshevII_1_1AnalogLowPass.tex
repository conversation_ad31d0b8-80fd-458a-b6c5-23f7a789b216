\hypertarget{classIir_1_1ChebyshevII_1_1AnalogLowPass}{}\doxysubsection{Iir\+::Chebyshev\+II\+::Analog\+Low\+Pass Class Reference}
\label{classIir_1_1ChebyshevII_1_1AnalogLowPass}\index{Iir::ChebyshevII::AnalogLowPass@{Iir::ChebyshevII::AnalogLowPass}}


{\ttfamily \#include $<$Chebyshev\+II.\+h$>$}

Inheritance diagram for Iir\+::Chebyshev\+II\+::Analog\+Low\+Pass\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=2.000000cm]{classIir_1_1ChebyshevII_1_1AnalogLowPass}
\end{center}
\end{figure}


\doxysubsubsection{Detailed Description}
Analogue lowpass prototype (s-\/plane) 

The documentation for this class was generated from the following files\+:\begin{DoxyCompactItemize}
\item 
iir/Chebyshev\+II.\+h\item 
iir/Chebyshev\+II.\+cpp\end{DoxyCompactItemize}
