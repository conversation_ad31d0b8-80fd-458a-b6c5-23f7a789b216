\hypertarget{structIir_1_1RBJ_1_1BandPass2}{}\doxysubsection{Iir\+::RBJ\+::Band\+Pass2 Struct Reference}
\label{structIir_1_1RBJ_1_1BandPass2}\index{Iir::RBJ::BandPass2@{Iir::RBJ::BandPass2}}


{\ttfamily \#include $<$RBJ.\+h$>$}

Inheritance diagram for Iir\+::RBJ\+::Band\+Pass2\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=3.000000cm]{structIir_1_1RBJ_1_1BandPass2}
\end{center}
\end{figure}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
void \mbox{\hyperlink{structIir_1_1RBJ_1_1BandPass2_aaad03d74806380bb4df721bceaed0249}{setupN}} (double center\+Frequency, double band\+Width)
\item 
void \mbox{\hyperlink{structIir_1_1RBJ_1_1BandPass2_a2173d305036d1e9d842c249aa34e4cde}{setup}} (double sample\+Rate, double center\+Frequency, double band\+Width)
\end{DoxyCompactItemize}


\doxysubsubsection{Detailed Description}
Bandpass with constant 0 dB peak gain 

\doxysubsubsection{Member Function Documentation}
\mbox{\Hypertarget{structIir_1_1RBJ_1_1BandPass2_a2173d305036d1e9d842c249aa34e4cde}\label{structIir_1_1RBJ_1_1BandPass2_a2173d305036d1e9d842c249aa34e4cde}} 
\index{Iir::RBJ::BandPass2@{Iir::RBJ::BandPass2}!setup@{setup}}
\index{setup@{setup}!Iir::RBJ::BandPass2@{Iir::RBJ::BandPass2}}
\doxyparagraph{\texorpdfstring{setup()}{setup()}}
{\footnotesize\ttfamily void Iir\+::\+RBJ\+::\+Band\+Pass2\+::setup (\begin{DoxyParamCaption}\item[{double}]{sample\+Rate,  }\item[{double}]{center\+Frequency,  }\item[{double}]{band\+Width }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients 
\begin{DoxyParams}{Parameters}
{\em sample\+Rate} & Sampling rate \\
\hline
{\em center\+Frequency} & Center frequency of the bandpass \\
\hline
{\em band\+Width} & Bandwidth in octaves \\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{structIir_1_1RBJ_1_1BandPass2_aaad03d74806380bb4df721bceaed0249}\label{structIir_1_1RBJ_1_1BandPass2_aaad03d74806380bb4df721bceaed0249}} 
\index{Iir::RBJ::BandPass2@{Iir::RBJ::BandPass2}!setupN@{setupN}}
\index{setupN@{setupN}!Iir::RBJ::BandPass2@{Iir::RBJ::BandPass2}}
\doxyparagraph{\texorpdfstring{setupN()}{setupN()}}
{\footnotesize\ttfamily void Iir\+::\+RBJ\+::\+Band\+Pass2\+::setupN (\begin{DoxyParamCaption}\item[{double}]{center\+Frequency,  }\item[{double}]{band\+Width }\end{DoxyParamCaption})}

Calculates the coefficients 
\begin{DoxyParams}{Parameters}
{\em center\+Frequency} & Normalised centre frequency of the bandpass \\
\hline
{\em band\+Width} & Bandwidth in octaves \\
\hline
\end{DoxyParams}


The documentation for this struct was generated from the following files\+:\begin{DoxyCompactItemize}
\item 
iir/RBJ.\+h\item 
iir/RBJ.\+cpp\end{DoxyCompactItemize}
