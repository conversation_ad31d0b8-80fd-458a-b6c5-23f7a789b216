\hypertarget{structIir_1_1ChebyshevII_1_1HighPass}{}\doxysubsection{Iir\+::Chebyshev\+II\+::High\+Pass$<$ Filter\+Order, State\+Type $>$ Struct Template Reference}
\label{structIir_1_1ChebyshevII_1_1HighPass}\index{Iir::ChebyshevII::HighPass$<$ FilterOrder, StateType $>$@{Iir::ChebyshevII::HighPass$<$ FilterOrder, StateType $>$}}


{\ttfamily \#include $<$Chebyshev\+II.\+h$>$}

Inheritance diagram for Iir\+::Chebyshev\+II\+::High\+Pass$<$ Filter\+Order, State\+Type $>$\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=2.400000cm]{structIir_1_1ChebyshevII_1_1HighPass}
\end{center}
\end{figure}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
void \mbox{\hyperlink{structIir_1_1ChebyshevII_1_1HighPass_a6fc4ac9b61747fb4cfb6d1ee7c775f31}{setup}} (double sample\+Rate, double cutoff\+Frequency, double stop\+Band\+Db)
\item 
void \mbox{\hyperlink{structIir_1_1ChebyshevII_1_1HighPass_a415aac5f061addfcabe198bb017742de}{setup}} (int req\+Order, double sample\+Rate, double cutoff\+Frequency, double stop\+Band\+Db)
\item 
void \mbox{\hyperlink{structIir_1_1ChebyshevII_1_1HighPass_aa1e4fe46e854bdfa9f1c26e901fcafbd}{setupN}} (double cutoff\+Frequency, double stop\+Band\+Db)
\item 
void \mbox{\hyperlink{structIir_1_1ChebyshevII_1_1HighPass_a8e7a7015cd832b4bd99f002389fbc8e0}{setupN}} (int req\+Order, double cutoff\+Frequency, double stop\+Band\+Db)
\end{DoxyCompactItemize}


\doxysubsubsection{Detailed Description}
\subsubsection*{template$<$int Filter\+Order = 4, class State\+Type = Direct\+Form\+II$>$\newline
struct Iir\+::\+Chebyshev\+II\+::\+High\+Pass$<$ Filter\+Order, State\+Type $>$}

\mbox{\hyperlink{namespaceIir_1_1ChebyshevII}{Chebyshev\+II}} highpass filter 
\begin{DoxyParams}{Parameters}
{\em Filter\+Order} & Reserves memory for a filter of the order Filter\+Order \\
\hline
{\em State\+Type} & The filter topology\+: \mbox{\hyperlink{classIir_1_1DirectFormI}{Direct\+FormI}}, \mbox{\hyperlink{classIir_1_1DirectFormII}{Direct\+Form\+II}}, ... \\
\hline
\end{DoxyParams}


\doxysubsubsection{Member Function Documentation}
\mbox{\Hypertarget{structIir_1_1ChebyshevII_1_1HighPass_a6fc4ac9b61747fb4cfb6d1ee7c775f31}\label{structIir_1_1ChebyshevII_1_1HighPass_a6fc4ac9b61747fb4cfb6d1ee7c775f31}} 
\index{Iir::ChebyshevII::HighPass$<$ FilterOrder, StateType $>$@{Iir::ChebyshevII::HighPass$<$ FilterOrder, StateType $>$}!setup@{setup}}
\index{setup@{setup}!Iir::ChebyshevII::HighPass$<$ FilterOrder, StateType $>$@{Iir::ChebyshevII::HighPass$<$ FilterOrder, StateType $>$}}
\doxyparagraph{\texorpdfstring{setup()}{setup()}\hspace{0.1cm}{\footnotesize\ttfamily [1/2]}}
{\footnotesize\ttfamily template$<$int Filter\+Order = 4, class State\+Type  = Direct\+Form\+II$>$ \\
void \mbox{\hyperlink{structIir_1_1ChebyshevII_1_1HighPass}{Iir\+::\+Chebyshev\+II\+::\+High\+Pass}}$<$ Filter\+Order, State\+Type $>$\+::setup (\begin{DoxyParamCaption}\item[{double}]{sample\+Rate,  }\item[{double}]{cutoff\+Frequency,  }\item[{double}]{stop\+Band\+Db }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients of the filter 
\begin{DoxyParams}{Parameters}
{\em sample\+Rate} & Sampling rate \\
\hline
{\em cutoff\+Frequency} & Cutoff frequency. \\
\hline
{\em stop\+Band\+Db} & Permitted ripples in dB in the stopband \\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{structIir_1_1ChebyshevII_1_1HighPass_a415aac5f061addfcabe198bb017742de}\label{structIir_1_1ChebyshevII_1_1HighPass_a415aac5f061addfcabe198bb017742de}} 
\index{Iir::ChebyshevII::HighPass$<$ FilterOrder, StateType $>$@{Iir::ChebyshevII::HighPass$<$ FilterOrder, StateType $>$}!setup@{setup}}
\index{setup@{setup}!Iir::ChebyshevII::HighPass$<$ FilterOrder, StateType $>$@{Iir::ChebyshevII::HighPass$<$ FilterOrder, StateType $>$}}
\doxyparagraph{\texorpdfstring{setup()}{setup()}\hspace{0.1cm}{\footnotesize\ttfamily [2/2]}}
{\footnotesize\ttfamily template$<$int Filter\+Order = 4, class State\+Type  = Direct\+Form\+II$>$ \\
void \mbox{\hyperlink{structIir_1_1ChebyshevII_1_1HighPass}{Iir\+::\+Chebyshev\+II\+::\+High\+Pass}}$<$ Filter\+Order, State\+Type $>$\+::setup (\begin{DoxyParamCaption}\item[{int}]{req\+Order,  }\item[{double}]{sample\+Rate,  }\item[{double}]{cutoff\+Frequency,  }\item[{double}]{stop\+Band\+Db }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients of the filter 
\begin{DoxyParams}{Parameters}
{\em req\+Order} & Requested order which can be less than the instantiated one \\
\hline
{\em sample\+Rate} & Sampling rate \\
\hline
{\em cutoff\+Frequency} & Cutoff frequency. \\
\hline
{\em stop\+Band\+Db} & Permitted ripples in dB in the stopband \\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{structIir_1_1ChebyshevII_1_1HighPass_aa1e4fe46e854bdfa9f1c26e901fcafbd}\label{structIir_1_1ChebyshevII_1_1HighPass_aa1e4fe46e854bdfa9f1c26e901fcafbd}} 
\index{Iir::ChebyshevII::HighPass$<$ FilterOrder, StateType $>$@{Iir::ChebyshevII::HighPass$<$ FilterOrder, StateType $>$}!setupN@{setupN}}
\index{setupN@{setupN}!Iir::ChebyshevII::HighPass$<$ FilterOrder, StateType $>$@{Iir::ChebyshevII::HighPass$<$ FilterOrder, StateType $>$}}
\doxyparagraph{\texorpdfstring{setupN()}{setupN()}\hspace{0.1cm}{\footnotesize\ttfamily [1/2]}}
{\footnotesize\ttfamily template$<$int Filter\+Order = 4, class State\+Type  = Direct\+Form\+II$>$ \\
void \mbox{\hyperlink{structIir_1_1ChebyshevII_1_1HighPass}{Iir\+::\+Chebyshev\+II\+::\+High\+Pass}}$<$ Filter\+Order, State\+Type $>$\+::setupN (\begin{DoxyParamCaption}\item[{double}]{cutoff\+Frequency,  }\item[{double}]{stop\+Band\+Db }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients of the filter 
\begin{DoxyParams}{Parameters}
{\em cutoff\+Frequency} & Normalised cutoff frequency (0..1/2) \\
\hline
{\em stop\+Band\+Db} & Permitted ripples in dB in the stopband \\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{structIir_1_1ChebyshevII_1_1HighPass_a8e7a7015cd832b4bd99f002389fbc8e0}\label{structIir_1_1ChebyshevII_1_1HighPass_a8e7a7015cd832b4bd99f002389fbc8e0}} 
\index{Iir::ChebyshevII::HighPass$<$ FilterOrder, StateType $>$@{Iir::ChebyshevII::HighPass$<$ FilterOrder, StateType $>$}!setupN@{setupN}}
\index{setupN@{setupN}!Iir::ChebyshevII::HighPass$<$ FilterOrder, StateType $>$@{Iir::ChebyshevII::HighPass$<$ FilterOrder, StateType $>$}}
\doxyparagraph{\texorpdfstring{setupN()}{setupN()}\hspace{0.1cm}{\footnotesize\ttfamily [2/2]}}
{\footnotesize\ttfamily template$<$int Filter\+Order = 4, class State\+Type  = Direct\+Form\+II$>$ \\
void \mbox{\hyperlink{structIir_1_1ChebyshevII_1_1HighPass}{Iir\+::\+Chebyshev\+II\+::\+High\+Pass}}$<$ Filter\+Order, State\+Type $>$\+::setupN (\begin{DoxyParamCaption}\item[{int}]{req\+Order,  }\item[{double}]{cutoff\+Frequency,  }\item[{double}]{stop\+Band\+Db }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients of the filter 
\begin{DoxyParams}{Parameters}
{\em req\+Order} & Requested order which can be less than the instantiated one \\
\hline
{\em cutoff\+Frequency} & Normalised cutoff frequency (0..1/2) \\
\hline
{\em stop\+Band\+Db} & Permitted ripples in dB in the stopband \\
\hline
\end{DoxyParams}


The documentation for this struct was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
iir/Chebyshev\+II.\+h\end{DoxyCompactItemize}
