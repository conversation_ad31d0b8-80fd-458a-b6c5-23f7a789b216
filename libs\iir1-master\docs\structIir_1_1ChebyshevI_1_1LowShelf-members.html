<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>iir1: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">iir1
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespaceIir.html">Iir</a></li><li class="navelem"><a class="el" href="namespaceIir_1_1ChebyshevI.html">ChebyshevI</a></li><li class="navelem"><a class="el" href="structIir_1_1ChebyshevI_1_1LowShelf.html">LowShelf</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">Iir::ChebyshevI::LowShelf&lt; FilterOrder, StateType &gt; Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="structIir_1_1ChebyshevI_1_1LowShelf.html">Iir::ChebyshevI::LowShelf&lt; FilterOrder, StateType &gt;</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="classIir_1_1CascadeStages.html#aa18e9abcaac65fd21be31ee859add3bb">filter</a>(const Sample in)</td><td class="entry"><a class="el" href="classIir_1_1CascadeStages.html">Iir::CascadeStages&lt; MaxStages, StateType &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classIir_1_1CascadeStages.html#a034a9be8ae590b814c8499898a93987a">getCascadeStorage</a>()</td><td class="entry"><a class="el" href="classIir_1_1CascadeStages.html">Iir::CascadeStages&lt; MaxStages, StateType &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classIir_1_1CascadeStages.html#ae5253fde0be7ccfa459a3f70fe8e3e31">reset</a>()</td><td class="entry"><a class="el" href="classIir_1_1CascadeStages.html">Iir::CascadeStages&lt; MaxStages, StateType &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="structIir_1_1ChebyshevI_1_1LowShelf.html#a310d49b7a15137b915969e8d8b12379d">setup</a>(double sampleRate, double cutoffFrequency, double gainDb, double rippleDb)</td><td class="entry"><a class="el" href="structIir_1_1ChebyshevI_1_1LowShelf.html">Iir::ChebyshevI::LowShelf&lt; FilterOrder, StateType &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="structIir_1_1ChebyshevI_1_1LowShelf.html#af6a210d8a2199456748e14efd3e5e05c">setup</a>(int reqOrder, double sampleRate, double cutoffFrequency, double gainDb, double rippleDb)</td><td class="entry"><a class="el" href="structIir_1_1ChebyshevI_1_1LowShelf.html">Iir::ChebyshevI::LowShelf&lt; FilterOrder, StateType &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classIir_1_1CascadeStages.html#a56d24da4cf09f898b534b1b05578244d">PoleFilter&lt; LowShelfBase, DirectFormII, 4 &gt;::setup</a>(const double(&amp;sosCoefficients)[MaxStages][6])</td><td class="entry"><a class="el" href="classIir_1_1CascadeStages.html">Iir::CascadeStages&lt; MaxStages, StateType &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="structIir_1_1ChebyshevI_1_1LowShelf.html#a610b49d218c77ab80438a7ada4fc93a5">setupN</a>(double cutoffFrequency, double gainDb, double rippleDb)</td><td class="entry"><a class="el" href="structIir_1_1ChebyshevI_1_1LowShelf.html">Iir::ChebyshevI::LowShelf&lt; FilterOrder, StateType &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="structIir_1_1ChebyshevI_1_1LowShelf.html#ae0caf0b8c0dc3e39e31eac6efb938547">setupN</a>(int reqOrder, double cutoffFrequency, double gainDb, double rippleDb)</td><td class="entry"><a class="el" href="structIir_1_1ChebyshevI_1_1LowShelf.html">Iir::ChebyshevI::LowShelf&lt; FilterOrder, StateType &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
</table></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Fri Jan 6 2023 23:53:40 for iir1 by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
