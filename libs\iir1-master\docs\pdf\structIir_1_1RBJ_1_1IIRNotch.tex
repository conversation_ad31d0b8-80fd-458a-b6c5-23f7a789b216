\hypertarget{structIir_1_1RBJ_1_1IIRNotch}{}\doxysubsection{Iir\+::RBJ\+::IIRNotch Struct Reference}
\label{structIir_1_1RBJ_1_1IIRNotch}\index{Iir::RBJ::IIRNotch@{Iir::RBJ::IIRNotch}}


{\ttfamily \#include $<$RBJ.\+h$>$}

Inheritance diagram for Iir\+::RBJ\+::IIRNotch\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=3.000000cm]{structIir_1_1RBJ_1_1IIRNotch}
\end{center}
\end{figure}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
void \mbox{\hyperlink{structIir_1_1RBJ_1_1IIRNotch_a824ce85d420097081e71b3888acb7862}{setupN}} (double center\+Frequency, double q\+\_\+factor=10)
\item 
void \mbox{\hyperlink{structIir_1_1RBJ_1_1IIRNotch_aedecb6358bc0a907742c56d5235d35df}{setup}} (double sample\+Rate, double center\+Frequency, double q\+\_\+factor=10)
\end{DoxyCompactItemize}


\doxysubsubsection{Detailed Description}
Bandstop with Q factor\+: the higher the Q factor the more narrow is the notch. However, a narrow notch has a long impulse response ( = ringing) and numerical problems might prevent perfect damping. Practical values of the Q factor are about Q = 10 to 20. In terms of the design the Q factor defines the radius of the poles as r = exp(-\/ pi$\ast$(center\+Frequency/sample\+Rate)/q\+\_\+factor) whereas the angles of the poles/zeros define the bandstop frequency. The higher Q the closer r moves towards the unit circle. 

\doxysubsubsection{Member Function Documentation}
\mbox{\Hypertarget{structIir_1_1RBJ_1_1IIRNotch_aedecb6358bc0a907742c56d5235d35df}\label{structIir_1_1RBJ_1_1IIRNotch_aedecb6358bc0a907742c56d5235d35df}} 
\index{Iir::RBJ::IIRNotch@{Iir::RBJ::IIRNotch}!setup@{setup}}
\index{setup@{setup}!Iir::RBJ::IIRNotch@{Iir::RBJ::IIRNotch}}
\doxyparagraph{\texorpdfstring{setup()}{setup()}}
{\footnotesize\ttfamily void Iir\+::\+RBJ\+::\+IIRNotch\+::setup (\begin{DoxyParamCaption}\item[{double}]{sample\+Rate,  }\item[{double}]{center\+Frequency,  }\item[{double}]{q\+\_\+factor = {\ttfamily 10} }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients 
\begin{DoxyParams}{Parameters}
{\em sample\+Rate} & Sampling rate \\
\hline
{\em center\+Frequency} & Center frequency of the notch \\
\hline
{\em q\+\_\+factor} & Q factor of the notch (1 to $\sim$20) \\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{structIir_1_1RBJ_1_1IIRNotch_a824ce85d420097081e71b3888acb7862}\label{structIir_1_1RBJ_1_1IIRNotch_a824ce85d420097081e71b3888acb7862}} 
\index{Iir::RBJ::IIRNotch@{Iir::RBJ::IIRNotch}!setupN@{setupN}}
\index{setupN@{setupN}!Iir::RBJ::IIRNotch@{Iir::RBJ::IIRNotch}}
\doxyparagraph{\texorpdfstring{setupN()}{setupN()}}
{\footnotesize\ttfamily void Iir\+::\+RBJ\+::\+IIRNotch\+::setupN (\begin{DoxyParamCaption}\item[{double}]{center\+Frequency,  }\item[{double}]{q\+\_\+factor = {\ttfamily 10} }\end{DoxyParamCaption})}

Calculates the coefficients 
\begin{DoxyParams}{Parameters}
{\em center\+Frequency} & Normalised centre frequency of the notch \\
\hline
{\em q\+\_\+factor} & Q factor of the notch (1 to $\sim$20) \\
\hline
\end{DoxyParams}


The documentation for this struct was generated from the following files\+:\begin{DoxyCompactItemize}
\item 
iir/RBJ.\+h\item 
iir/RBJ.\+cpp\end{DoxyCompactItemize}
