#include "chart.h"
#include "ui_chart.h"

Chart::Chart(QWidget *parent, const QString& title,const QString xName,const QString yName)
    : QWidget(parent),
      ui(new Ui::Chart)
{
    ui->setupUi(this);
    initUi(title,xName,yName);
}

Chart::~Chart()
{
    delete ui;
}

// 设置图表标题
void Chart::setTitle(const QString title)
{
    ui->title->setText(title);
}

// 获取图表标题
QString Chart::title()
{
    return ui->title->text();
}

// 设置标题样式
void Chart::setTitleStyleSheet(bool isActive)
{
    ui->title->setStyleSheet(isActive ? activeStyle : inactiveStyle);
}

// 获得标题样式
bool Chart::getTitleStyleSheet()
{
    return ui->title->styleSheet()==activeStyle ? true:false;
}

// 初始化UI界面
void Chart::initUi(const QString& title,const QString xName,const QString yName)
{
    // 设置控制表格大小策略
    ui->controlGraph->setSizePolicy(QSizePolicy::Fixed, QSizePolicy::Preferred);
    // 设置标题和样式
    ui->title->setText(title);
    ui->title->setStyleSheet(inactiveStyle);

    //设置图标
    waveformArea = new WaveformMdiArea(ui->graph, xName,yName);
    waveformArea->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
    QVBoxLayout *graphLayout = new QVBoxLayout(ui->graph);
    graphLayout->addWidget(waveformArea);
    graphLayout->setContentsMargins(0, 0, 0, 0);

    //让waveformArea内部变化能影响到Chart按钮
    connect(waveformArea,&WaveformMdiArea::tracerIsTrue,this,[this]{if(ui->tracer->isChecked()==false)ui->tracer->setChecked(true);});
    connect(waveformArea,&WaveformMdiArea::tracerIsFalse,this,[this]{if(ui->tracer->isChecked()==true)ui->tracer->setChecked(false);});
    connect(waveformArea,&WaveformMdiArea::modeZoom,this,[this]{if(ui->mode->isChecked()==false)ui->mode->setChecked(true);});
    connect(waveformArea,&WaveformMdiArea::modeNone,this,[this]{if(ui->mode->isChecked()==true)ui->mode->setChecked(false);});
    connect(waveformArea, &WaveformMdiArea::clearCurves, this,
            [this]{haveCurve(false);headerCheckBox->setEnabled(false);updateCurveTable();});

    // 设置曲线控制表格
    ui->controlGraph->setColumnCount(2);
    ui->controlGraph->setHorizontalHeaderLabels({"曲线名称", "颜色"});
    ui->controlGraph->setColumnWidth(0, 130);
    ui->controlGraph->horizontalHeader()->setStretchLastSection(true);

    // 设置垂直表头
    ui->controlGraph->verticalHeader()->setVisible(true);
    ui->controlGraph->verticalHeader()->setDefaultSectionSize(30);
    ui->controlGraph->verticalHeader()->setSectionResizeMode(QHeaderView::Fixed);
    ui->controlGraph->verticalHeader()->setDefaultAlignment(Qt::AlignCenter);

    // 创建表头全选复选框
    headerCheckBox = new QCheckBox(ui->controlGraph->horizontalHeader());
    headerCheckBox->setChecked(true);
    headerCheckBox->setToolTip("全选/全不选曲线");
    int sectionPos = ui->controlGraph->horizontalHeader()->sectionPosition(0);
    headerCheckBox->move(sectionPos + 5, 5);
    headerCheckBox->resize(20, 20);

    // 同步按钮状态
    haveCurve(false);

    // 连接全选复选框信号
    connect(headerCheckBox, &QCheckBox::clicked, this, &Chart::onHeaderCheckBoxClicked);

    //链接按钮改变颜色
    connect(ui->controlGraph, &QTableWidget::itemDoubleClicked, this, &Chart::onCurveTableItemDoubleClicked);


    // 连接复选框状态变化信号
    connect(ui->controlGraph, &QTableWidget::itemChanged, this, [this](QTableWidgetItem *item) {
        if (item->column() == 0) {
            QString curveName = item->text();
            bool visible = (item->checkState() == Qt::Checked);
            waveformArea->setCurveVisible(curveName, visible);

            // 检查所有行状态
            bool isChecked = false;
            for (int row = 0; row < ui->controlGraph->rowCount(); ++row) {
                QTableWidgetItem *rowItem = ui->controlGraph->item(row, 0);
                if (rowItem && rowItem->checkState() == Qt::Checked) {
                    isChecked = true;
                    break;
                }
            }
            headerCheckBox->setChecked(isChecked);
            haveCurve(isChecked);
        }
    });
}

// 更新曲线数据
void Chart::updateCurve(const QVector<double>& x, const QVector<double>& y, const QString& curveName)
{
    if(!waveformArea) return;
    haveCurve(true);
    if(waveformArea->updateCurve(x, y, curveName)){
        headerCheckBox->setEnabled(true);
    }
    updateCurveTable();
    qDebug() << "更新曲线:" << curveName << "，数据点数:" << x.size();
}

// 清除所有曲线
void Chart::clearGraphs()
{
    emit clearCurves();
    haveCurve(false);
    headerCheckBox->setEnabled(false);
    waveformArea->onClearCurvesTriggered();
    updateCurveTable();
}



// 视图自适应
void Chart::on_fitToView_clicked()
{
    waveformArea->fitToView();
}

// 切换游标显示
void Chart::on_tracer_clicked()
{
    if(waveformArea){
        bool visible = !waveformArea->isTracerVisible();
        waveformArea->setTracerVisible(visible);
        ui->tracer->setChecked(visible);
    }
}

// 全屏切换
void Chart::on_showHide_toggled(bool checked)
{
    ui->controlGraph->setVisible(!checked);
}

// 切换到下一条曲线
void Chart::on_nextCurve_clicked()
{
    waveformArea->nextCurve();
    qDebug() << "切换下一条曲线";
}

// 模式切换
void Chart::on_mode_toggled(bool checked)
{
    waveformArea->setZoomMode(checked);
}

// 曲线颜色修改
void Chart::onCurveTableItemDoubleClicked(QTableWidgetItem *item)
{
    if (!item) return;
    if (item->column() == 1) {
        QColor currentColor = item->background().color();
        QColor color = QColorDialog::getColor(currentColor, this, "选择曲线颜色");
        if (color.isValid()) {
            int row = item->row();
            QString curveName = ui->controlGraph->item(row, 0)->text();
            waveformArea->setCurveColor(curveName, color);
            item->setText(color.name());
            updateCurveTable();
        }
    }
}

// 全选复选框点击事件
void Chart::onHeaderCheckBoxClicked()
{
    bool checked=headerCheckBox->isChecked();
    for (int row = 0; row < ui->controlGraph->rowCount(); ++row) {
        QTableWidgetItem *item = ui->controlGraph->item(row, 0);
        if (item) {
            item->setCheckState(checked ? Qt::Checked : Qt::Unchecked);
        }
    }
    headerCheckBox->setChecked(checked);
    haveCurve(checked);
}

// 关闭事件处理
void Chart::closeEvent(QCloseEvent *event)
{
    emit aboutToClose();
    event->accept();
    this->deleteLater();
}

// 更新曲线表格
void Chart::updateCurveTable()
{
    ui->controlGraph->clearContents();
    auto curves = waveformArea->getCurves();
    if(curves.size()==0)return;
    ui->controlGraph->setRowCount(curves.size());

    bool isChecked = false;

    for (int i = 0; i < curves.size(); ++i) {
        QString name = curves[i].first;
        QColor color = curves[i].second;

        // 添加曲线名称项（带复选框）
        QTableWidgetItem *nameItem = new QTableWidgetItem(name);
        nameItem->setFlags(nameItem->flags() & ~Qt::ItemIsEditable);
        nameItem->setCheckState(waveformArea->getCurveVisible(name) ? Qt::Checked : Qt::Unchecked);
        ui->controlGraph->setItem(i, 0, nameItem);

        // 添加颜色项
        QTableWidgetItem *colorItem = new QTableWidgetItem();
        colorItem->setBackground(color);
        colorItem->setFlags(colorItem->flags() & ~Qt::ItemIsEditable);
        ui->controlGraph->setItem(i, 1, colorItem);

        // 设置行号
        QTableWidgetItem *rowHeaderItem = new QTableWidgetItem(QString::number(i + 1));
        rowHeaderItem->setTextAlignment(Qt::AlignCenter);
        rowHeaderItem->setFlags(rowHeaderItem->flags() & ~Qt::ItemIsEditable);
        ui->controlGraph->setVerticalHeaderItem(i, rowHeaderItem);

        if (nameItem->checkState() == Qt::Checked) {
            isChecked = true;
        }
    }

    // 更新全选复选框状态
    headerCheckBox->setChecked(isChecked);
    haveCurve(isChecked);
}

//按钮管理
void Chart::haveCurve(bool have)
{
    ui->fitToView->setEnabled(have);
    ui->tracer->setEnabled(have);
    ui->nextCurve->setEnabled(have);
}
