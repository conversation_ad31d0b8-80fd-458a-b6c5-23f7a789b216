<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>iir1: Class Members - Functions</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">iir1
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
&#160;

<h3><a id="index_a"></a>- a -</h3><ul>
<li>applyScale()
: <a class="el" href="classIir_1_1Biquad.html#a38c8e327dca53dbfa9a25758b7be8227">Iir::Biquad</a>
</li>
</ul>


<h3><a id="index_f"></a>- f -</h3><ul>
<li>filter()
: <a class="el" href="classIir_1_1Biquad.html#a527b9666d6aef0e576193fdce7e19750">Iir::Biquad</a>
, <a class="el" href="classIir_1_1CascadeStages.html#aa18e9abcaac65fd21be31ee859add3bb">Iir::CascadeStages&lt; MaxStages, StateType &gt;</a>
, <a class="el" href="structIir_1_1RBJ_1_1RBJbase.html#a5dd179c8491e29966d5c33036cabe151">Iir::RBJ::RBJbase</a>
</li>
</ul>


<h3><a id="index_g"></a>- g -</h3><ul>
<li>getA0()
: <a class="el" href="classIir_1_1Biquad.html#a7debc9f6ef5e64622c710b8c9ba96056">Iir::Biquad</a>
</li>
<li>getA1()
: <a class="el" href="classIir_1_1Biquad.html#a459116db7aa381281997f428f15334cf">Iir::Biquad</a>
</li>
<li>getA2()
: <a class="el" href="classIir_1_1Biquad.html#a061e2402d528312fc6095db6551c9141">Iir::Biquad</a>
</li>
<li>getB0()
: <a class="el" href="classIir_1_1Biquad.html#a84686da988e160a216f3e7057682ffbb">Iir::Biquad</a>
</li>
<li>getB1()
: <a class="el" href="classIir_1_1Biquad.html#af418a5f260baadbcffe5a7029f089937">Iir::Biquad</a>
</li>
<li>getB2()
: <a class="el" href="classIir_1_1Biquad.html#a8f3d697bb7c2def508da648938f6afc3">Iir::Biquad</a>
</li>
<li>getCascadeStorage()
: <a class="el" href="classIir_1_1CascadeStages.html#a034a9be8ae590b814c8499898a93987a">Iir::CascadeStages&lt; MaxStages, StateType &gt;</a>
</li>
<li>getNumStages()
: <a class="el" href="classIir_1_1Cascade.html#a9694b85c160e3689a4d71fd51ca6175d">Iir::Cascade</a>
</li>
<li>getPoleZeros()
: <a class="el" href="classIir_1_1Biquad.html#a63c78d766bc40be10004a34aaebfb8e7">Iir::Biquad</a>
, <a class="el" href="classIir_1_1Cascade.html#a18df8bebec4a5e8e3ddc28c35b6bb2f8">Iir::Cascade</a>
</li>
<li>getState()
: <a class="el" href="structIir_1_1RBJ_1_1RBJbase.html#a8093409edfce007a4972fa2992d69670">Iir::RBJ::RBJbase</a>
</li>
</ul>


<h3><a id="index_i"></a>- i -</h3><ul>
<li>isMatchedPair()
: <a class="el" href="structIir_1_1ComplexPair.html#a79d121320c8b042faebcc0364398b071">Iir::ComplexPair</a>
</li>
</ul>


<h3><a id="index_o"></a>- o -</h3><ul>
<li>operator[]()
: <a class="el" href="classIir_1_1Cascade.html#afc58e4b464b2cdef11e77b01e1a80668">Iir::Cascade</a>
</li>
</ul>


<h3><a id="index_r"></a>- r -</h3><ul>
<li>reset()
: <a class="el" href="classIir_1_1CascadeStages.html#ae5253fde0be7ccfa459a3f70fe8e3e31">Iir::CascadeStages&lt; MaxStages, StateType &gt;</a>
, <a class="el" href="structIir_1_1RBJ_1_1RBJbase.html#a0bd9a0b60e60b9326a14e43acbb430ab">Iir::RBJ::RBJbase</a>
</li>
<li>response()
: <a class="el" href="classIir_1_1Biquad.html#ace03653f66fe65eddc55d48840458440">Iir::Biquad</a>
, <a class="el" href="classIir_1_1Cascade.html#aa76e09e3868829a80e954d0444390f90">Iir::Cascade</a>
</li>
</ul>


<h3><a id="index_s"></a>- s -</h3><ul>
<li>setCoefficients()
: <a class="el" href="classIir_1_1Biquad.html#a7f005089c194c68aeecdc66a3e6c6a78">Iir::Biquad</a>
</li>
<li>setIdentity()
: <a class="el" href="classIir_1_1Biquad.html#a53b066d077ce559a91f26fc3c4201c2c">Iir::Biquad</a>
</li>
<li>setOnePole()
: <a class="el" href="classIir_1_1Biquad.html#a455fd42a2e99ac84b09184becf2d047f">Iir::Biquad</a>
</li>
<li>setPoleZeroPair()
: <a class="el" href="classIir_1_1Biquad.html#a69b4a2eaedb4b51aea9fb46a99c3d3c2">Iir::Biquad</a>
</li>
<li>setTwoPole()
: <a class="el" href="classIir_1_1Biquad.html#a6c614e84db8493b8495b314dd860d0bc">Iir::Biquad</a>
</li>
<li>setup()
: <a class="el" href="structIir_1_1Butterworth_1_1BandPass.html#a4e01abd204275ae2223e5197bfc149dc">Iir::Butterworth::BandPass&lt; FilterOrder, StateType &gt;</a>
, <a class="el" href="structIir_1_1Butterworth_1_1BandShelf.html#a0de40c55dae63d23127cf4dd21bcf050">Iir::Butterworth::BandShelf&lt; FilterOrder, StateType &gt;</a>
, <a class="el" href="structIir_1_1Butterworth_1_1BandStop.html#a88300cc9eef7b5eda35f0381f7238c16">Iir::Butterworth::BandStop&lt; FilterOrder, StateType &gt;</a>
, <a class="el" href="structIir_1_1Butterworth_1_1HighPass.html#ac9835800f12a982c160720cebef2f963">Iir::Butterworth::HighPass&lt; FilterOrder, StateType &gt;</a>
, <a class="el" href="structIir_1_1Butterworth_1_1HighShelf.html#a19a78cb96c9b2454ccb48745ed50500d">Iir::Butterworth::HighShelf&lt; FilterOrder, StateType &gt;</a>
, <a class="el" href="structIir_1_1Butterworth_1_1LowPass.html#a0e10c27ec72f076bf7d0b21c0281860c">Iir::Butterworth::LowPass&lt; FilterOrder, StateType &gt;</a>
, <a class="el" href="structIir_1_1Butterworth_1_1LowShelf.html#a74f72a17ffe747ad5b0e9f36235fcd34">Iir::Butterworth::LowShelf&lt; FilterOrder, StateType &gt;</a>
, <a class="el" href="classIir_1_1CascadeStages.html#a56d24da4cf09f898b534b1b05578244d">Iir::CascadeStages&lt; MaxStages, StateType &gt;</a>
, <a class="el" href="structIir_1_1ChebyshevI_1_1BandPass.html#ac52fcb50fb3022ac8290e07e954a0ab0">Iir::ChebyshevI::BandPass&lt; FilterOrder, StateType &gt;</a>
, <a class="el" href="structIir_1_1ChebyshevI_1_1BandShelf.html#abb0e9a1480addb5b4ce38b2d83888c95">Iir::ChebyshevI::BandShelf&lt; FilterOrder, StateType &gt;</a>
, <a class="el" href="structIir_1_1ChebyshevI_1_1BandStop.html#a78e0e02cc47abebbd2271345605b12a2">Iir::ChebyshevI::BandStop&lt; FilterOrder, StateType &gt;</a>
, <a class="el" href="structIir_1_1ChebyshevI_1_1HighPass.html#a0ecdb8e5692663a043f49db30277ba37">Iir::ChebyshevI::HighPass&lt; FilterOrder, StateType &gt;</a>
, <a class="el" href="structIir_1_1ChebyshevI_1_1HighShelf.html#a9fe4f6cee3b4b1aa4688ea21d3f5202f">Iir::ChebyshevI::HighShelf&lt; FilterOrder, StateType &gt;</a>
, <a class="el" href="structIir_1_1ChebyshevI_1_1LowPass.html#a6ff5b40576b962bc35fa10eb2903873e">Iir::ChebyshevI::LowPass&lt; FilterOrder, StateType &gt;</a>
, <a class="el" href="structIir_1_1ChebyshevI_1_1LowShelf.html#a310d49b7a15137b915969e8d8b12379d">Iir::ChebyshevI::LowShelf&lt; FilterOrder, StateType &gt;</a>
, <a class="el" href="structIir_1_1ChebyshevII_1_1BandPass.html#a1157ea7f70718df1e2983fb0f576931f">Iir::ChebyshevII::BandPass&lt; FilterOrder, StateType &gt;</a>
, <a class="el" href="structIir_1_1ChebyshevII_1_1BandShelf.html#adca87357a1e04fe298144cae7b200670">Iir::ChebyshevII::BandShelf&lt; FilterOrder, StateType &gt;</a>
, <a class="el" href="structIir_1_1ChebyshevII_1_1BandStop.html#a2cced9fbf0ac69ac0f20192bc6d8375c">Iir::ChebyshevII::BandStop&lt; FilterOrder, StateType &gt;</a>
, <a class="el" href="structIir_1_1ChebyshevII_1_1HighPass.html#a6fc4ac9b61747fb4cfb6d1ee7c775f31">Iir::ChebyshevII::HighPass&lt; FilterOrder, StateType &gt;</a>
, <a class="el" href="structIir_1_1ChebyshevII_1_1HighShelf.html#afdf4c782aa49f832bce8cdca5404aec4">Iir::ChebyshevII::HighShelf&lt; FilterOrder, StateType &gt;</a>
, <a class="el" href="structIir_1_1ChebyshevII_1_1LowPass.html#ae966204946d7ce91c9656cb20b2162ef">Iir::ChebyshevII::LowPass&lt; FilterOrder, StateType &gt;</a>
, <a class="el" href="structIir_1_1ChebyshevII_1_1LowShelf.html#a7b93d59263561ce231f38293cc03b225">Iir::ChebyshevII::LowShelf&lt; FilterOrder, StateType &gt;</a>
, <a class="el" href="structIir_1_1Custom_1_1SOSCascade.html#aae603eb1bd4330411a6003e09ba36efc">Iir::Custom::SOSCascade&lt; NSOS, StateType &gt;</a>
, <a class="el" href="structIir_1_1RBJ_1_1AllPass.html#ae68e5b4a22b3256b52798cddc3ff653c">Iir::RBJ::AllPass</a>
, <a class="el" href="structIir_1_1RBJ_1_1BandPass1.html#a82ba843c68435a5a09cc16fdbf358afc">Iir::RBJ::BandPass1</a>
, <a class="el" href="structIir_1_1RBJ_1_1BandPass2.html#a2173d305036d1e9d842c249aa34e4cde">Iir::RBJ::BandPass2</a>
, <a class="el" href="structIir_1_1RBJ_1_1BandShelf.html#ae4b2c50b6cf6715d881f9c3681530870">Iir::RBJ::BandShelf</a>
, <a class="el" href="structIir_1_1RBJ_1_1BandStop.html#afe29086ad05ecfe2c0697a09bfa45c49">Iir::RBJ::BandStop</a>
, <a class="el" href="structIir_1_1RBJ_1_1HighPass.html#a445c9bba06d82c972e389fbae45962e9">Iir::RBJ::HighPass</a>
, <a class="el" href="structIir_1_1RBJ_1_1HighShelf.html#a4887616ad520c27fce55d76d915d5bf6">Iir::RBJ::HighShelf</a>
, <a class="el" href="structIir_1_1RBJ_1_1IIRNotch.html#aedecb6358bc0a907742c56d5235d35df">Iir::RBJ::IIRNotch</a>
, <a class="el" href="structIir_1_1RBJ_1_1LowPass.html#adc6ffd4bab0cdc4f665f667841b96cea">Iir::RBJ::LowPass</a>
, <a class="el" href="structIir_1_1RBJ_1_1LowShelf.html#a9bac813cc8399f3c274d9d10dc3a5a1e">Iir::RBJ::LowShelf</a>
</li>
<li>setupN()
: <a class="el" href="structIir_1_1Butterworth_1_1BandPass.html#ac33b003f6c77021f90a998d0e03b0e4d">Iir::Butterworth::BandPass&lt; FilterOrder, StateType &gt;</a>
, <a class="el" href="structIir_1_1Butterworth_1_1BandShelf.html#a96cc173942cd6789b5aff603b37e5b2f">Iir::Butterworth::BandShelf&lt; FilterOrder, StateType &gt;</a>
, <a class="el" href="structIir_1_1Butterworth_1_1BandStop.html#ab88fa7f17faa3de891c9db4766b3cba6">Iir::Butterworth::BandStop&lt; FilterOrder, StateType &gt;</a>
, <a class="el" href="structIir_1_1Butterworth_1_1HighPass.html#ac33912cbd0d3d65acdda0c53d5b64274">Iir::Butterworth::HighPass&lt; FilterOrder, StateType &gt;</a>
, <a class="el" href="structIir_1_1Butterworth_1_1HighShelf.html#af63e4f43001ae2d9faf44ba6496db562">Iir::Butterworth::HighShelf&lt; FilterOrder, StateType &gt;</a>
, <a class="el" href="structIir_1_1Butterworth_1_1LowPass.html#af8fc4401089d8d36508a468dc09e74a0">Iir::Butterworth::LowPass&lt; FilterOrder, StateType &gt;</a>
, <a class="el" href="structIir_1_1Butterworth_1_1LowShelf.html#a4637158be17c50cb14cf3686b30e135e">Iir::Butterworth::LowShelf&lt; FilterOrder, StateType &gt;</a>
, <a class="el" href="structIir_1_1ChebyshevI_1_1BandPass.html#aa10de012e157fb8ec55dcd0d0a2813d7">Iir::ChebyshevI::BandPass&lt; FilterOrder, StateType &gt;</a>
, <a class="el" href="structIir_1_1ChebyshevI_1_1BandShelf.html#a6aa48c5f0f3796ffc9b0384acc8c42fa">Iir::ChebyshevI::BandShelf&lt; FilterOrder, StateType &gt;</a>
, <a class="el" href="structIir_1_1ChebyshevI_1_1BandStop.html#a2391183ce4dda61f2feab75af3587af5">Iir::ChebyshevI::BandStop&lt; FilterOrder, StateType &gt;</a>
, <a class="el" href="structIir_1_1ChebyshevI_1_1HighPass.html#aea3ad8ee966575c3a6680758f3b22123">Iir::ChebyshevI::HighPass&lt; FilterOrder, StateType &gt;</a>
, <a class="el" href="structIir_1_1ChebyshevI_1_1HighShelf.html#aa078bdf604ac9feeedd282174bd0cea9">Iir::ChebyshevI::HighShelf&lt; FilterOrder, StateType &gt;</a>
, <a class="el" href="structIir_1_1ChebyshevI_1_1LowPass.html#a249a9236b0584edced2168a60b667106">Iir::ChebyshevI::LowPass&lt; FilterOrder, StateType &gt;</a>
, <a class="el" href="structIir_1_1ChebyshevI_1_1LowShelf.html#ae0caf0b8c0dc3e39e31eac6efb938547">Iir::ChebyshevI::LowShelf&lt; FilterOrder, StateType &gt;</a>
, <a class="el" href="structIir_1_1ChebyshevII_1_1BandPass.html#a6044cfbd6da7f2f6f78e9a2d969038d3">Iir::ChebyshevII::BandPass&lt; FilterOrder, StateType &gt;</a>
, <a class="el" href="structIir_1_1ChebyshevII_1_1BandShelf.html#a468f2edb4849f8f60b65bdfbb3e309e3">Iir::ChebyshevII::BandShelf&lt; FilterOrder, StateType &gt;</a>
, <a class="el" href="structIir_1_1ChebyshevII_1_1BandStop.html#ab91192c2d3bf48f69849aba72c0c1c31">Iir::ChebyshevII::BandStop&lt; FilterOrder, StateType &gt;</a>
, <a class="el" href="structIir_1_1ChebyshevII_1_1HighPass.html#aa1e4fe46e854bdfa9f1c26e901fcafbd">Iir::ChebyshevII::HighPass&lt; FilterOrder, StateType &gt;</a>
, <a class="el" href="structIir_1_1ChebyshevII_1_1HighShelf.html#a713d1708e646df97794eacc8541605e2">Iir::ChebyshevII::HighShelf&lt; FilterOrder, StateType &gt;</a>
, <a class="el" href="structIir_1_1ChebyshevII_1_1LowPass.html#a51d676e2d229e776ae04ed8b6d028612">Iir::ChebyshevII::LowPass&lt; FilterOrder, StateType &gt;</a>
, <a class="el" href="structIir_1_1ChebyshevII_1_1LowShelf.html#a29b862d094f91de1254d01c7d8d7df02">Iir::ChebyshevII::LowShelf&lt; FilterOrder, StateType &gt;</a>
, <a class="el" href="structIir_1_1RBJ_1_1AllPass.html#ab0ca96fe3066c0bc2206a47d51c6bf5a">Iir::RBJ::AllPass</a>
, <a class="el" href="structIir_1_1RBJ_1_1BandPass1.html#a3af22e73e4809afa1349aeeee2d6452b">Iir::RBJ::BandPass1</a>
, <a class="el" href="structIir_1_1RBJ_1_1BandPass2.html#aaad03d74806380bb4df721bceaed0249">Iir::RBJ::BandPass2</a>
, <a class="el" href="structIir_1_1RBJ_1_1BandShelf.html#ae5184f5a8c32602689c866128a3471c1">Iir::RBJ::BandShelf</a>
, <a class="el" href="structIir_1_1RBJ_1_1BandStop.html#a52326e7ac833ac14e7a8859548fb321d">Iir::RBJ::BandStop</a>
, <a class="el" href="structIir_1_1RBJ_1_1HighPass.html#aa990e5f563666cd2d3cdc48cd77d5f2b">Iir::RBJ::HighPass</a>
, <a class="el" href="structIir_1_1RBJ_1_1HighShelf.html#a0d5dbf5b05c08663bedf00e4a7f983db">Iir::RBJ::HighShelf</a>
, <a class="el" href="structIir_1_1RBJ_1_1IIRNotch.html#a824ce85d420097081e71b3888acb7862">Iir::RBJ::IIRNotch</a>
, <a class="el" href="structIir_1_1RBJ_1_1LowPass.html#a210539758e8e95da26b74079b4219388">Iir::RBJ::LowPass</a>
, <a class="el" href="structIir_1_1RBJ_1_1LowShelf.html#a006830eb278deb38a4cfc0c3240d4a9a">Iir::RBJ::LowShelf</a>
</li>
<li>SOSCascade()
: <a class="el" href="structIir_1_1Custom_1_1SOSCascade.html#a04671c1c84cacaf1c822adeb850ccc63">Iir::Custom::SOSCascade&lt; NSOS, StateType &gt;</a>
</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Fri Jan 6 2023 23:53:47 for iir1 by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
