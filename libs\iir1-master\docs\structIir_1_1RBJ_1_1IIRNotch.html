<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>iir1: Iir::RBJ::IIRNotch Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">iir1
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespaceIir.html">Iir</a></li><li class="navelem"><b>RBJ</b></li><li class="navelem"><a class="el" href="structIir_1_1RBJ_1_1IIRNotch.html">IIRNotch</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="structIir_1_1RBJ_1_1IIRNotch-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">Iir::RBJ::IIRNotch Struct Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;<a class="el" href="RBJ_8h_source.html">RBJ.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for Iir::RBJ::IIRNotch:</div>
<div class="dyncontent">
 <div class="center">
  <img src="structIir_1_1RBJ_1_1IIRNotch.png" usemap="#Iir::RBJ::IIRNotch_map" alt=""/>
  <map id="Iir::RBJ::IIRNotch_map" name="Iir::RBJ::IIRNotch_map">
<area href="structIir_1_1RBJ_1_1RBJbase.html" alt="Iir::RBJ::RBJbase" shape="rect" coords="0,56,108,80"/>
<area href="classIir_1_1Biquad.html" alt="Iir::Biquad" shape="rect" coords="0,0,108,24"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a824ce85d420097081e71b3888acb7862"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structIir_1_1RBJ_1_1IIRNotch.html#a824ce85d420097081e71b3888acb7862">setupN</a> (double centerFrequency, double q_factor=10)</td></tr>
<tr class="separator:a824ce85d420097081e71b3888acb7862"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aedecb6358bc0a907742c56d5235d35df"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structIir_1_1RBJ_1_1IIRNotch.html#aedecb6358bc0a907742c56d5235d35df">setup</a> (double sampleRate, double centerFrequency, double q_factor=10)</td></tr>
<tr class="separator:aedecb6358bc0a907742c56d5235d35df"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_methods_structIir_1_1RBJ_1_1RBJbase"><td colspan="2" onclick="javascript:toggleInherit('pub_methods_structIir_1_1RBJ_1_1RBJbase')"><img src="closed.png" alt="-"/>&#160;Public Member Functions inherited from <a class="el" href="structIir_1_1RBJ_1_1RBJbase.html">Iir::RBJ::RBJbase</a></td></tr>
<tr class="memitem:a5dd179c8491e29966d5c33036cabe151 inherit pub_methods_structIir_1_1RBJ_1_1RBJbase"><td class="memTemplParams" colspan="2"><a id="a5dd179c8491e29966d5c33036cabe151"></a>
template&lt;typename Sample &gt; </td></tr>
<tr class="memitem:a5dd179c8491e29966d5c33036cabe151 inherit pub_methods_structIir_1_1RBJ_1_1RBJbase"><td class="memTemplItemLeft" align="right" valign="top">Sample&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="structIir_1_1RBJ_1_1RBJbase.html#a5dd179c8491e29966d5c33036cabe151">filter</a> (Sample s)</td></tr>
<tr class="memdesc:a5dd179c8491e29966d5c33036cabe151 inherit pub_methods_structIir_1_1RBJ_1_1RBJbase"><td class="mdescLeft">&#160;</td><td class="mdescRight">filter operation <br /></td></tr>
<tr class="separator:a5dd179c8491e29966d5c33036cabe151 inherit pub_methods_structIir_1_1RBJ_1_1RBJbase"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0bd9a0b60e60b9326a14e43acbb430ab inherit pub_methods_structIir_1_1RBJ_1_1RBJbase"><td class="memItemLeft" align="right" valign="top"><a id="a0bd9a0b60e60b9326a14e43acbb430ab"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structIir_1_1RBJ_1_1RBJbase.html#a0bd9a0b60e60b9326a14e43acbb430ab">reset</a> ()</td></tr>
<tr class="memdesc:a0bd9a0b60e60b9326a14e43acbb430ab inherit pub_methods_structIir_1_1RBJ_1_1RBJbase"><td class="mdescLeft">&#160;</td><td class="mdescRight">resets the delay lines to zero <br /></td></tr>
<tr class="separator:a0bd9a0b60e60b9326a14e43acbb430ab inherit pub_methods_structIir_1_1RBJ_1_1RBJbase"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8093409edfce007a4972fa2992d69670 inherit pub_methods_structIir_1_1RBJ_1_1RBJbase"><td class="memItemLeft" align="right" valign="top"><a id="a8093409edfce007a4972fa2992d69670"></a>
const <a class="el" href="classIir_1_1DirectFormI.html">DirectFormI</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structIir_1_1RBJ_1_1RBJbase.html#a8093409edfce007a4972fa2992d69670">getState</a> ()</td></tr>
<tr class="memdesc:a8093409edfce007a4972fa2992d69670 inherit pub_methods_structIir_1_1RBJ_1_1RBJbase"><td class="mdescLeft">&#160;</td><td class="mdescRight">gets the delay lines (=state) of the filter <br /></td></tr>
<tr class="separator:a8093409edfce007a4972fa2992d69670 inherit pub_methods_structIir_1_1RBJ_1_1RBJbase"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_methods_classIir_1_1Biquad"><td colspan="2" onclick="javascript:toggleInherit('pub_methods_classIir_1_1Biquad')"><img src="closed.png" alt="-"/>&#160;Public Member Functions inherited from <a class="el" href="classIir_1_1Biquad.html">Iir::Biquad</a></td></tr>
<tr class="memitem:ace03653f66fe65eddc55d48840458440 inherit pub_methods_classIir_1_1Biquad"><td class="memItemLeft" align="right" valign="top">complex_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classIir_1_1Biquad.html#ace03653f66fe65eddc55d48840458440">response</a> (double normalizedFrequency) const</td></tr>
<tr class="separator:ace03653f66fe65eddc55d48840458440 inherit pub_methods_classIir_1_1Biquad"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a63c78d766bc40be10004a34aaebfb8e7 inherit pub_methods_classIir_1_1Biquad"><td class="memItemLeft" align="right" valign="top">std::vector&lt; <a class="el" href="structIir_1_1PoleZeroPair.html">PoleZeroPair</a> &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classIir_1_1Biquad.html#a63c78d766bc40be10004a34aaebfb8e7">getPoleZeros</a> () const</td></tr>
<tr class="separator:a63c78d766bc40be10004a34aaebfb8e7 inherit pub_methods_classIir_1_1Biquad"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7debc9f6ef5e64622c710b8c9ba96056 inherit pub_methods_classIir_1_1Biquad"><td class="memItemLeft" align="right" valign="top">double&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classIir_1_1Biquad.html#a7debc9f6ef5e64622c710b8c9ba96056">getA0</a> () const</td></tr>
<tr class="separator:a7debc9f6ef5e64622c710b8c9ba96056 inherit pub_methods_classIir_1_1Biquad"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a459116db7aa381281997f428f15334cf inherit pub_methods_classIir_1_1Biquad"><td class="memItemLeft" align="right" valign="top">double&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classIir_1_1Biquad.html#a459116db7aa381281997f428f15334cf">getA1</a> () const</td></tr>
<tr class="separator:a459116db7aa381281997f428f15334cf inherit pub_methods_classIir_1_1Biquad"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a061e2402d528312fc6095db6551c9141 inherit pub_methods_classIir_1_1Biquad"><td class="memItemLeft" align="right" valign="top">double&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classIir_1_1Biquad.html#a061e2402d528312fc6095db6551c9141">getA2</a> () const</td></tr>
<tr class="separator:a061e2402d528312fc6095db6551c9141 inherit pub_methods_classIir_1_1Biquad"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a84686da988e160a216f3e7057682ffbb inherit pub_methods_classIir_1_1Biquad"><td class="memItemLeft" align="right" valign="top">double&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classIir_1_1Biquad.html#a84686da988e160a216f3e7057682ffbb">getB0</a> () const</td></tr>
<tr class="separator:a84686da988e160a216f3e7057682ffbb inherit pub_methods_classIir_1_1Biquad"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af418a5f260baadbcffe5a7029f089937 inherit pub_methods_classIir_1_1Biquad"><td class="memItemLeft" align="right" valign="top">double&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classIir_1_1Biquad.html#af418a5f260baadbcffe5a7029f089937">getB1</a> () const</td></tr>
<tr class="separator:af418a5f260baadbcffe5a7029f089937 inherit pub_methods_classIir_1_1Biquad"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8f3d697bb7c2def508da648938f6afc3 inherit pub_methods_classIir_1_1Biquad"><td class="memItemLeft" align="right" valign="top">double&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classIir_1_1Biquad.html#a8f3d697bb7c2def508da648938f6afc3">getB2</a> () const</td></tr>
<tr class="separator:a8f3d697bb7c2def508da648938f6afc3 inherit pub_methods_classIir_1_1Biquad"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a527b9666d6aef0e576193fdce7e19750 inherit pub_methods_classIir_1_1Biquad"><td class="memTemplParams" colspan="2">template&lt;class StateType &gt; </td></tr>
<tr class="memitem:a527b9666d6aef0e576193fdce7e19750 inherit pub_methods_classIir_1_1Biquad"><td class="memTemplItemLeft" align="right" valign="top">double&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="classIir_1_1Biquad.html#a527b9666d6aef0e576193fdce7e19750">filter</a> (double s, StateType &amp;state) const</td></tr>
<tr class="separator:a527b9666d6aef0e576193fdce7e19750 inherit pub_methods_classIir_1_1Biquad"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7f005089c194c68aeecdc66a3e6c6a78 inherit pub_methods_classIir_1_1Biquad"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classIir_1_1Biquad.html#a7f005089c194c68aeecdc66a3e6c6a78">setCoefficients</a> (double a0, double a1, double a2, double b0, double b1, double b2)</td></tr>
<tr class="separator:a7f005089c194c68aeecdc66a3e6c6a78 inherit pub_methods_classIir_1_1Biquad"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a455fd42a2e99ac84b09184becf2d047f inherit pub_methods_classIir_1_1Biquad"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classIir_1_1Biquad.html#a455fd42a2e99ac84b09184becf2d047f">setOnePole</a> (complex_t pole, complex_t zero)</td></tr>
<tr class="separator:a455fd42a2e99ac84b09184becf2d047f inherit pub_methods_classIir_1_1Biquad"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6c614e84db8493b8495b314dd860d0bc inherit pub_methods_classIir_1_1Biquad"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classIir_1_1Biquad.html#a6c614e84db8493b8495b314dd860d0bc">setTwoPole</a> (complex_t pole1, complex_t zero1, complex_t pole2, complex_t zero2)</td></tr>
<tr class="separator:a6c614e84db8493b8495b314dd860d0bc inherit pub_methods_classIir_1_1Biquad"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a69b4a2eaedb4b51aea9fb46a99c3d3c2 inherit pub_methods_classIir_1_1Biquad"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classIir_1_1Biquad.html#a69b4a2eaedb4b51aea9fb46a99c3d3c2">setPoleZeroPair</a> (const <a class="el" href="structIir_1_1PoleZeroPair.html">PoleZeroPair</a> &amp;pair)</td></tr>
<tr class="separator:a69b4a2eaedb4b51aea9fb46a99c3d3c2 inherit pub_methods_classIir_1_1Biquad"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a53b066d077ce559a91f26fc3c4201c2c inherit pub_methods_classIir_1_1Biquad"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classIir_1_1Biquad.html#a53b066d077ce559a91f26fc3c4201c2c">setIdentity</a> ()</td></tr>
<tr class="separator:a53b066d077ce559a91f26fc3c4201c2c inherit pub_methods_classIir_1_1Biquad"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a38c8e327dca53dbfa9a25758b7be8227 inherit pub_methods_classIir_1_1Biquad"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classIir_1_1Biquad.html#a38c8e327dca53dbfa9a25758b7be8227">applyScale</a> (double scale)</td></tr>
<tr class="separator:a38c8e327dca53dbfa9a25758b7be8227 inherit pub_methods_classIir_1_1Biquad"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Bandstop with Q factor: the higher the Q factor the more narrow is the notch. However, a narrow notch has a long impulse response ( = ringing) and numerical problems might prevent perfect damping. Practical values of the Q factor are about Q = 10 to 20. In terms of the design the Q factor defines the radius of the poles as r = exp(- pi*(centerFrequency/sampleRate)/q_factor) whereas the angles of the poles/zeros define the bandstop frequency. The higher Q the closer r moves towards the unit circle. </p>
</div><h2 class="groupheader">Member Function Documentation</h2>
<a id="aedecb6358bc0a907742c56d5235d35df"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aedecb6358bc0a907742c56d5235d35df">&#9670;&nbsp;</a></span>setup()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void Iir::RBJ::IIRNotch::setup </td>
          <td>(</td>
          <td class="paramtype">double&#160;</td>
          <td class="paramname"><em>sampleRate</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">double&#160;</td>
          <td class="paramname"><em>centerFrequency</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">double&#160;</td>
          <td class="paramname"><em>q_factor</em> = <code>10</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Calculates the coefficients </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">sampleRate</td><td>Sampling rate </td></tr>
    <tr><td class="paramname">centerFrequency</td><td>Center frequency of the notch </td></tr>
    <tr><td class="paramname">q_factor</td><td>Q factor of the notch (1 to ~20) </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a824ce85d420097081e71b3888acb7862"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a824ce85d420097081e71b3888acb7862">&#9670;&nbsp;</a></span>setupN()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Iir::RBJ::IIRNotch::setupN </td>
          <td>(</td>
          <td class="paramtype">double&#160;</td>
          <td class="paramname"><em>centerFrequency</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">double&#160;</td>
          <td class="paramname"><em>q_factor</em> = <code>10</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Calculates the coefficients </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">centerFrequency</td><td>Normalised centre frequency of the notch </td></tr>
    <tr><td class="paramname">q_factor</td><td>Q factor of the notch (1 to ~20) </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<hr/>The documentation for this struct was generated from the following files:<ul>
<li>iir/<a class="el" href="RBJ_8h_source.html">RBJ.h</a></li>
<li>iir/RBJ.cpp</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Fri Jan 6 2023 23:53:46 for iir1 by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
