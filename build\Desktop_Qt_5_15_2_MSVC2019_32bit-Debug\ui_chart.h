/********************************************************************************
** Form generated from reading UI file 'chart.ui'
**
** Created by: Qt User Interface Compiler version 5.15.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_CHART_H
#define UI_CHART_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QTableWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_Chart
{
public:
    QVBoxLayout *verticalLayout;
    QHBoxLayout *horizontalLayout_3;
    QLabel *title;
    QPushButton *pushButton_5;
    QHBoxLayout *horizontalLayout_2;
    QVBoxLayout *verticalLayout_2;
    QWidget *graph;
    QHBoxLayout *horizontalLayout;
    QPushButton *mode;
    QPushButton *fitToView;
    QPushButton *tracer;
    QPushButton *nextCurve;
    QSpacerItem *horizontalSpacer_3;
    QSpacerItem *horizontalSpacer_4;
    QSpacerItem *horizontalSpacer_2;
    QSpacerItem *horizontalSpacer;
    QVBoxLayout *verticalLayout_3;
    QSpacerItem *verticalSpacer_2;
    QPushButton *showHide;
    QSpacerItem *verticalSpacer_3;
    QPushButton *pushButton_3;
    QSpacerItem *verticalSpacer_4;
    QSpacerItem *verticalSpacer;
    QVBoxLayout *verticalLayout_4;
    QTableWidget *controlGraph;

    void setupUi(QWidget *Chart)
    {
        if (Chart->objectName().isEmpty())
            Chart->setObjectName(QString::fromUtf8("Chart"));
        Chart->resize(834, 493);
        verticalLayout = new QVBoxLayout(Chart);
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        horizontalLayout_3 = new QHBoxLayout();
        horizontalLayout_3->setObjectName(QString::fromUtf8("horizontalLayout_3"));
        title = new QLabel(Chart);
        title->setObjectName(QString::fromUtf8("title"));

        horizontalLayout_3->addWidget(title);

        pushButton_5 = new QPushButton(Chart);
        pushButton_5->setObjectName(QString::fromUtf8("pushButton_5"));

        horizontalLayout_3->addWidget(pushButton_5);

        horizontalLayout_3->setStretch(0, 9);

        verticalLayout->addLayout(horizontalLayout_3);

        horizontalLayout_2 = new QHBoxLayout();
        horizontalLayout_2->setObjectName(QString::fromUtf8("horizontalLayout_2"));
        verticalLayout_2 = new QVBoxLayout();
        verticalLayout_2->setObjectName(QString::fromUtf8("verticalLayout_2"));
        graph = new QWidget(Chart);
        graph->setObjectName(QString::fromUtf8("graph"));

        verticalLayout_2->addWidget(graph);

        horizontalLayout = new QHBoxLayout();
        horizontalLayout->setSpacing(6);
        horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));
        horizontalLayout->setSizeConstraint(QLayout::SetFixedSize);
        mode = new QPushButton(Chart);
        mode->setObjectName(QString::fromUtf8("mode"));
        mode->setCheckable(true);
        mode->setChecked(false);

        horizontalLayout->addWidget(mode);

        fitToView = new QPushButton(Chart);
        fitToView->setObjectName(QString::fromUtf8("fitToView"));

        horizontalLayout->addWidget(fitToView);

        tracer = new QPushButton(Chart);
        tracer->setObjectName(QString::fromUtf8("tracer"));
        tracer->setCursor(QCursor(Qt::ArrowCursor));
        tracer->setCheckable(true);

        horizontalLayout->addWidget(tracer);

        nextCurve = new QPushButton(Chart);
        nextCurve->setObjectName(QString::fromUtf8("nextCurve"));

        horizontalLayout->addWidget(nextCurve);

        horizontalSpacer_3 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout->addItem(horizontalSpacer_3);

        horizontalSpacer_4 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout->addItem(horizontalSpacer_4);

        horizontalSpacer_2 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout->addItem(horizontalSpacer_2);

        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout->addItem(horizontalSpacer);


        verticalLayout_2->addLayout(horizontalLayout);

        verticalLayout_2->setStretch(0, 9);

        horizontalLayout_2->addLayout(verticalLayout_2);

        verticalLayout_3 = new QVBoxLayout();
        verticalLayout_3->setObjectName(QString::fromUtf8("verticalLayout_3"));
        verticalSpacer_2 = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout_3->addItem(verticalSpacer_2);

        showHide = new QPushButton(Chart);
        showHide->setObjectName(QString::fromUtf8("showHide"));
        showHide->setCheckable(true);

        verticalLayout_3->addWidget(showHide);

        verticalSpacer_3 = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout_3->addItem(verticalSpacer_3);

        pushButton_3 = new QPushButton(Chart);
        pushButton_3->setObjectName(QString::fromUtf8("pushButton_3"));

        verticalLayout_3->addWidget(pushButton_3);

        verticalSpacer_4 = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout_3->addItem(verticalSpacer_4);

        verticalSpacer = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout_3->addItem(verticalSpacer);


        horizontalLayout_2->addLayout(verticalLayout_3);

        verticalLayout_4 = new QVBoxLayout();
        verticalLayout_4->setObjectName(QString::fromUtf8("verticalLayout_4"));
        verticalLayout_4->setSizeConstraint(QLayout::SetFixedSize);
        controlGraph = new QTableWidget(Chart);
        if (controlGraph->columnCount() < 2)
            controlGraph->setColumnCount(2);
        QTableWidgetItem *__qtablewidgetitem = new QTableWidgetItem();
        controlGraph->setHorizontalHeaderItem(0, __qtablewidgetitem);
        QTableWidgetItem *__qtablewidgetitem1 = new QTableWidgetItem();
        controlGraph->setHorizontalHeaderItem(1, __qtablewidgetitem1);
        if (controlGraph->rowCount() < 6)
            controlGraph->setRowCount(6);
        QTableWidgetItem *__qtablewidgetitem2 = new QTableWidgetItem();
        controlGraph->setVerticalHeaderItem(0, __qtablewidgetitem2);
        QTableWidgetItem *__qtablewidgetitem3 = new QTableWidgetItem();
        controlGraph->setVerticalHeaderItem(1, __qtablewidgetitem3);
        QTableWidgetItem *__qtablewidgetitem4 = new QTableWidgetItem();
        controlGraph->setVerticalHeaderItem(2, __qtablewidgetitem4);
        QTableWidgetItem *__qtablewidgetitem5 = new QTableWidgetItem();
        controlGraph->setVerticalHeaderItem(3, __qtablewidgetitem5);
        QTableWidgetItem *__qtablewidgetitem6 = new QTableWidgetItem();
        controlGraph->setVerticalHeaderItem(4, __qtablewidgetitem6);
        QTableWidgetItem *__qtablewidgetitem7 = new QTableWidgetItem();
        controlGraph->setVerticalHeaderItem(5, __qtablewidgetitem7);
        controlGraph->setObjectName(QString::fromUtf8("controlGraph"));

        verticalLayout_4->addWidget(controlGraph);


        horizontalLayout_2->addLayout(verticalLayout_4);


        verticalLayout->addLayout(horizontalLayout_2);

        verticalLayout->setStretch(0, 1);
        verticalLayout->setStretch(1, 10);

        retranslateUi(Chart);

        QMetaObject::connectSlotsByName(Chart);
    } // setupUi

    void retranslateUi(QWidget *Chart)
    {
        Chart->setWindowTitle(QCoreApplication::translate("Chart", "Form", nullptr));
        title->setText(QCoreApplication::translate("Chart", "\345\220\215\345\255\227", nullptr));
        pushButton_5->setText(QCoreApplication::translate("Chart", "\345\267\245\345\205\267", nullptr));
#if QT_CONFIG(tooltip)
        mode->setToolTip(QCoreApplication::translate("Chart", "\345\210\207\346\215\242\351\274\240\346\240\207\346\250\241\345\274\217", nullptr));
#endif // QT_CONFIG(tooltip)
        mode->setText(QCoreApplication::translate("Chart", "+", nullptr));
#if QT_CONFIG(tooltip)
        fitToView->setToolTip(QCoreApplication::translate("Chart", "\346\240\271\346\215\256\346\225\260\346\215\256\350\260\203\346\225\264\350\247\206\345\233\276", nullptr));
#endif // QT_CONFIG(tooltip)
        fitToView->setText(QCoreApplication::translate("Chart", "[]", nullptr));
#if QT_CONFIG(tooltip)
        tracer->setToolTip(QCoreApplication::translate("Chart", "\346\230\276\347\244\272\346\210\226\350\200\205\351\232\220\350\227\217\346\270\270\346\240\207", nullptr));
#endif // QT_CONFIG(tooltip)
        tracer->setText(QCoreApplication::translate("Chart", "++", nullptr));
#if QT_CONFIG(tooltip)
        nextCurve->setToolTip(QCoreApplication::translate("Chart", "\345\210\207\346\215\242\344\270\213\344\270\200\346\235\241\345\217\257\350\247\201\346\233\262\347\272\277", nullptr));
#endif // QT_CONFIG(tooltip)
        nextCurve->setText(QCoreApplication::translate("Chart", "->", nullptr));
#if QT_CONFIG(tooltip)
        showHide->setToolTip(QCoreApplication::translate("Chart", "\346\230\276\347\244\272\346\210\226\350\200\205\351\232\220\350\227\217\345\233\276\344\276\213", nullptr));
#endif // QT_CONFIG(tooltip)
        showHide->setText(QCoreApplication::translate("Chart", "\345\205\250\345\261\217", nullptr));
        pushButton_3->setText(QCoreApplication::translate("Chart", "PushButton", nullptr));
        QTableWidgetItem *___qtablewidgetitem = controlGraph->horizontalHeaderItem(0);
        ___qtablewidgetitem->setText(QCoreApplication::translate("Chart", "\346\233\262\347\272\277\345\220\215\347\247\260", nullptr));
        QTableWidgetItem *___qtablewidgetitem1 = controlGraph->horizontalHeaderItem(1);
        ___qtablewidgetitem1->setText(QCoreApplication::translate("Chart", "\351\242\234\350\211\262", nullptr));
    } // retranslateUi

};

namespace Ui {
    class Chart: public Ui_Chart {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_CHART_H
