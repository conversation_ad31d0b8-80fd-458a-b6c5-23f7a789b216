/****************************************************************************
** Meta object code from reading C++ file 'chanalsetform.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.15.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../MainSet/channel/chanalsetform.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'chanalsetform.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.15.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_ChanalSetForm_t {
    QByteArrayData data[37];
    char stringdata0[526];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_ChanalSetForm_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_ChanalSetForm_t qt_meta_stringdata_ChanalSetForm = {
    {
QT_MOC_LITERAL(0, 0, 13), // "ChanalSetForm"
QT_MOC_LITERAL(1, 14, 12), // "nameConflict"
QT_MOC_LITERAL(2, 27, 0), // ""
QT_MOC_LITERAL(3, 28, 19), // "showContextMenu_OUT"
QT_MOC_LITERAL(4, 48, 3), // "pos"
QT_MOC_LITERAL(5, 52, 18), // "showContextMenu_IN"
QT_MOC_LITERAL(6, 71, 17), // "handleCellClicked"
QT_MOC_LITERAL(7, 89, 3), // "row"
QT_MOC_LITERAL(8, 93, 6), // "column"
QT_MOC_LITERAL(9, 100, 23), // "handleCellDoubleClicked"
QT_MOC_LITERAL(10, 124, 5), // "onAdd"
QT_MOC_LITERAL(11, 130, 13), // "QTableWidget*"
QT_MOC_LITERAL(12, 144, 5), // "table"
QT_MOC_LITERAL(13, 150, 6), // "onEdit"
QT_MOC_LITERAL(14, 157, 8), // "cellData"
QT_MOC_LITERAL(15, 166, 8), // "onDelete"
QT_MOC_LITERAL(16, 175, 6), // "onCopy"
QT_MOC_LITERAL(17, 182, 13), // "onCellClicked"
QT_MOC_LITERAL(18, 196, 19), // "onCellDoubleClicked"
QT_MOC_LITERAL(19, 216, 22), // "onChannelInfoConfirmed"
QT_MOC_LITERAL(20, 239, 20), // "SetChannelInfoStruct"
QT_MOC_LITERAL(21, 260, 4), // "info"
QT_MOC_LITERAL(22, 265, 9), // "operation"
QT_MOC_LITERAL(23, 275, 20), // "ChanalEditForm::Mode"
QT_MOC_LITERAL(24, 296, 4), // "mode"
QT_MOC_LITERAL(25, 301, 16), // "onSaveBtnClicked"
QT_MOC_LITERAL(26, 318, 16), // "onExitBtnClicked"
QT_MOC_LITERAL(27, 335, 33), // "on_OutChannelCalibrateBtn_cli..."
QT_MOC_LITERAL(28, 369, 32), // "on_InChannelCalibrateBtn_clicked"
QT_MOC_LITERAL(29, 402, 35), // "on_OUTINChannelCalibrateBtn_c..."
QT_MOC_LITERAL(30, 438, 10), // "updateGain"
QT_MOC_LITERAL(31, 449, 36), // "CalibrateOneChannelForm::Chan..."
QT_MOC_LITERAL(32, 486, 3), // "Mod"
QT_MOC_LITERAL(33, 490, 9), // "inchannel"
QT_MOC_LITERAL(34, 500, 10), // "outchannel"
QT_MOC_LITERAL(35, 511, 7), // "OutGain"
QT_MOC_LITERAL(36, 519, 6) // "InGain"

    },
    "ChanalSetForm\0nameConflict\0\0"
    "showContextMenu_OUT\0pos\0showContextMenu_IN\0"
    "handleCellClicked\0row\0column\0"
    "handleCellDoubleClicked\0onAdd\0"
    "QTableWidget*\0table\0onEdit\0cellData\0"
    "onDelete\0onCopy\0onCellClicked\0"
    "onCellDoubleClicked\0onChannelInfoConfirmed\0"
    "SetChannelInfoStruct\0info\0operation\0"
    "ChanalEditForm::Mode\0mode\0onSaveBtnClicked\0"
    "onExitBtnClicked\0on_OutChannelCalibrateBtn_clicked\0"
    "on_InChannelCalibrateBtn_clicked\0"
    "on_OUTINChannelCalibrateBtn_clicked\0"
    "updateGain\0CalibrateOneChannelForm::ChannelMode\0"
    "Mod\0inchannel\0outchannel\0OutGain\0"
    "InGain"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_ChanalSetForm[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      18,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       1,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    0,  104,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       3,    1,  105,    2, 0x08 /* Private */,
       5,    1,  108,    2, 0x08 /* Private */,
       6,    2,  111,    2, 0x08 /* Private */,
       9,    2,  116,    2, 0x08 /* Private */,
      10,    1,  121,    2, 0x08 /* Private */,
      13,    3,  124,    2, 0x08 /* Private */,
      15,    3,  131,    2, 0x08 /* Private */,
      16,    3,  138,    2, 0x08 /* Private */,
      17,    3,  145,    2, 0x08 /* Private */,
      18,    3,  152,    2, 0x08 /* Private */,
      19,    3,  159,    2, 0x08 /* Private */,
      25,    0,  166,    2, 0x08 /* Private */,
      26,    0,  167,    2, 0x08 /* Private */,
      27,    0,  168,    2, 0x08 /* Private */,
      28,    0,  169,    2, 0x08 /* Private */,
      29,    0,  170,    2, 0x08 /* Private */,
      30,    5,  171,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void,

 // slots: parameters
    QMetaType::Void, QMetaType::QPoint,    4,
    QMetaType::Void, QMetaType::QPoint,    4,
    QMetaType::Void, QMetaType::Int, QMetaType::Int,    7,    8,
    QMetaType::Void, QMetaType::Int, QMetaType::Int,    7,    8,
    QMetaType::Void, 0x80000000 | 11,   12,
    QMetaType::Void, 0x80000000 | 11, QMetaType::Int, QMetaType::QString,   12,    7,   14,
    QMetaType::Void, 0x80000000 | 11, QMetaType::Int, QMetaType::QString,   12,    7,   14,
    QMetaType::Void, 0x80000000 | 11, QMetaType::Int, QMetaType::QString,   12,    7,   14,
    QMetaType::Void, 0x80000000 | 11, QMetaType::Int, QMetaType::QString,   12,    7,   14,
    QMetaType::Void, 0x80000000 | 11, QMetaType::Int, QMetaType::QString,   12,    7,   14,
    QMetaType::Void, 0x80000000 | 20, QMetaType::QString, 0x80000000 | 23,   21,   22,   24,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 31, QMetaType::QString, QMetaType::QString, QMetaType::Double, QMetaType::Double,   32,   33,   34,   35,   36,

       0        // eod
};

void ChanalSetForm::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<ChanalSetForm *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->nameConflict(); break;
        case 1: _t->showContextMenu_OUT((*reinterpret_cast< const QPoint(*)>(_a[1]))); break;
        case 2: _t->showContextMenu_IN((*reinterpret_cast< const QPoint(*)>(_a[1]))); break;
        case 3: _t->handleCellClicked((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 4: _t->handleCellDoubleClicked((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 5: _t->onAdd((*reinterpret_cast< QTableWidget*(*)>(_a[1]))); break;
        case 6: _t->onEdit((*reinterpret_cast< QTableWidget*(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2])),(*reinterpret_cast< const QString(*)>(_a[3]))); break;
        case 7: _t->onDelete((*reinterpret_cast< QTableWidget*(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2])),(*reinterpret_cast< const QString(*)>(_a[3]))); break;
        case 8: _t->onCopy((*reinterpret_cast< QTableWidget*(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2])),(*reinterpret_cast< const QString(*)>(_a[3]))); break;
        case 9: _t->onCellClicked((*reinterpret_cast< QTableWidget*(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2])),(*reinterpret_cast< const QString(*)>(_a[3]))); break;
        case 10: _t->onCellDoubleClicked((*reinterpret_cast< QTableWidget*(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2])),(*reinterpret_cast< const QString(*)>(_a[3]))); break;
        case 11: _t->onChannelInfoConfirmed((*reinterpret_cast< const SetChannelInfoStruct(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2])),(*reinterpret_cast< ChanalEditForm::Mode(*)>(_a[3]))); break;
        case 12: _t->onSaveBtnClicked(); break;
        case 13: _t->onExitBtnClicked(); break;
        case 14: _t->on_OutChannelCalibrateBtn_clicked(); break;
        case 15: _t->on_InChannelCalibrateBtn_clicked(); break;
        case 16: _t->on_OUTINChannelCalibrateBtn_clicked(); break;
        case 17: _t->updateGain((*reinterpret_cast< CalibrateOneChannelForm::ChannelMode(*)>(_a[1])),(*reinterpret_cast< QString(*)>(_a[2])),(*reinterpret_cast< QString(*)>(_a[3])),(*reinterpret_cast< double(*)>(_a[4])),(*reinterpret_cast< double(*)>(_a[5]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 5:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QTableWidget* >(); break;
            }
            break;
        case 6:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QTableWidget* >(); break;
            }
            break;
        case 7:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QTableWidget* >(); break;
            }
            break;
        case 8:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QTableWidget* >(); break;
            }
            break;
        case 9:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QTableWidget* >(); break;
            }
            break;
        case 10:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QTableWidget* >(); break;
            }
            break;
        case 11:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< SetChannelInfoStruct >(); break;
            }
            break;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (ChanalSetForm::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ChanalSetForm::nameConflict)) {
                *result = 0;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject ChanalSetForm::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_meta_stringdata_ChanalSetForm.data,
    qt_meta_data_ChanalSetForm,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *ChanalSetForm::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ChanalSetForm::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_ChanalSetForm.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int ChanalSetForm::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 18)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 18;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 18)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 18;
    }
    return _id;
}

// SIGNAL 0
void ChanalSetForm::nameConflict()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
