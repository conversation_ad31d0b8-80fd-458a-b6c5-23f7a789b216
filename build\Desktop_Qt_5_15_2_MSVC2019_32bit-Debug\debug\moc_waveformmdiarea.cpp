/****************************************************************************
** Meta object code from reading C++ file 'waveformmdiarea.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.15.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../qcustomplot/waveformmdiarea.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'waveformmdiarea.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.15.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_CustomPlotTwo_t {
    QByteArrayData data[1];
    char stringdata0[14];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_CustomPlotTwo_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_CustomPlotTwo_t qt_meta_stringdata_CustomPlotTwo = {
    {
QT_MOC_LITERAL(0, 0, 13) // "CustomPlotTwo"

    },
    "CustomPlotTwo"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_CustomPlotTwo[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       0,    0, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

       0        // eod
};

void CustomPlotTwo::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    Q_UNUSED(_o);
    Q_UNUSED(_id);
    Q_UNUSED(_c);
    Q_UNUSED(_a);
}

QT_INIT_METAOBJECT const QMetaObject CustomPlotTwo::staticMetaObject = { {
    QMetaObject::SuperData::link<QCustomPlot::staticMetaObject>(),
    qt_meta_stringdata_CustomPlotTwo.data,
    qt_meta_data_CustomPlotTwo,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *CustomPlotTwo::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *CustomPlotTwo::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CustomPlotTwo.stringdata0))
        return static_cast<void*>(this);
    return QCustomPlot::qt_metacast(_clname);
}

int CustomPlotTwo::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QCustomPlot::qt_metacall(_c, _id, _a);
    return _id;
}
struct qt_meta_stringdata_WaveformMdiArea_t {
    QByteArrayData data[13];
    char stringdata0[138];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_WaveformMdiArea_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_WaveformMdiArea_t qt_meta_stringdata_WaveformMdiArea = {
    {
QT_MOC_LITERAL(0, 0, 15), // "WaveformMdiArea"
QT_MOC_LITERAL(1, 16, 12), // "aboutToClose"
QT_MOC_LITERAL(2, 29, 0), // ""
QT_MOC_LITERAL(3, 30, 12), // "tracerIsTrue"
QT_MOC_LITERAL(4, 43, 13), // "tracerIsFalse"
QT_MOC_LITERAL(5, 57, 8), // "modeZoom"
QT_MOC_LITERAL(6, 66, 8), // "modeNone"
QT_MOC_LITERAL(7, 75, 11), // "clearCurves"
QT_MOC_LITERAL(8, 87, 11), // "onMouseMove"
QT_MOC_LITERAL(9, 99, 12), // "QMouseEvent*"
QT_MOC_LITERAL(10, 112, 5), // "event"
QT_MOC_LITERAL(11, 118, 15), // "showContextMenu"
QT_MOC_LITERAL(12, 134, 3) // "pos"

    },
    "WaveformMdiArea\0aboutToClose\0\0"
    "tracerIsTrue\0tracerIsFalse\0modeZoom\0"
    "modeNone\0clearCurves\0onMouseMove\0"
    "QMouseEvent*\0event\0showContextMenu\0"
    "pos"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_WaveformMdiArea[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       8,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       6,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    0,   54,    2, 0x06 /* Public */,
       3,    0,   55,    2, 0x06 /* Public */,
       4,    0,   56,    2, 0x06 /* Public */,
       5,    0,   57,    2, 0x06 /* Public */,
       6,    0,   58,    2, 0x06 /* Public */,
       7,    0,   59,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       8,    1,   60,    2, 0x08 /* Private */,
      11,    1,   63,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

 // slots: parameters
    QMetaType::Void, 0x80000000 | 9,   10,
    QMetaType::Void, QMetaType::QPoint,   12,

       0        // eod
};

void WaveformMdiArea::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<WaveformMdiArea *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->aboutToClose(); break;
        case 1: _t->tracerIsTrue(); break;
        case 2: _t->tracerIsFalse(); break;
        case 3: _t->modeZoom(); break;
        case 4: _t->modeNone(); break;
        case 5: _t->clearCurves(); break;
        case 6: _t->onMouseMove((*reinterpret_cast< QMouseEvent*(*)>(_a[1]))); break;
        case 7: _t->showContextMenu((*reinterpret_cast< const QPoint(*)>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (WaveformMdiArea::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&WaveformMdiArea::aboutToClose)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (WaveformMdiArea::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&WaveformMdiArea::tracerIsTrue)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (WaveformMdiArea::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&WaveformMdiArea::tracerIsFalse)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (WaveformMdiArea::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&WaveformMdiArea::modeZoom)) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (WaveformMdiArea::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&WaveformMdiArea::modeNone)) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (WaveformMdiArea::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&WaveformMdiArea::clearCurves)) {
                *result = 5;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject WaveformMdiArea::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_meta_stringdata_WaveformMdiArea.data,
    qt_meta_data_WaveformMdiArea,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *WaveformMdiArea::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *WaveformMdiArea::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_WaveformMdiArea.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int WaveformMdiArea::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 8)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 8;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 8)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 8;
    }
    return _id;
}

// SIGNAL 0
void WaveformMdiArea::aboutToClose()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void WaveformMdiArea::tracerIsTrue()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void WaveformMdiArea::tracerIsFalse()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void WaveformMdiArea::modeZoom()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}

// SIGNAL 4
void WaveformMdiArea::modeNone()
{
    QMetaObject::activate(this, &staticMetaObject, 4, nullptr);
}

// SIGNAL 5
void WaveformMdiArea::clearCurves()
{
    QMetaObject::activate(this, &staticMetaObject, 5, nullptr);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
