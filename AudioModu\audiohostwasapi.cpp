﻿#include "audiohostwasapi.h" // 包含 WASAPI 音频主机头文件

#define FORMAT RTAUDIO_SINT32 // 定义音频格式为 32 位有符号整数

AudioHostWasapi::AudioHostWasapi(QObject *parent) : QObject(parent), // 构造函数，初始化父对象
    wasapi(RtAudio::WINDOWS_WASAPI, // 初始化 WASAPI RtAudio 实例
           std::function<void(RtAudioErrorType, const std::string&)>(
               std::bind(&AudioHostWasapi::errorCallback, this, std::placeholders::_1, std::placeholders::_2))), // 绑定错误回调函数
    isRunning(false), runningDeviceId(0), useBuffer1(true), outputFilled(false), inputRead(true), lastCallbackTime(0.0) // 初始化成员变量
{
    qDebug() << "WASAPI RtAudio instance initialized."; // 输出调试信息：WASAPI RtAudio 实例已初始化
} // AudioHostWasapi 构造函数结束

AudioHostWasapi::~AudioHostWasapi() { // 析构函数
    stopStream(); // 停止音频流
} // AudioHostWasapi 析构函数结束

std::vector<RtAudio::DeviceInfo> AudioHostWasapi::getDevices() { // 获取设备信息列表
    std::vector<RtAudio::DeviceInfo> devices; // 创建设备信息向量
    return devices; // 返回空列表（未实现具体逻辑）
} // getDevices 函数结束

bool AudioHostWasapi::openStream(unsigned int deviceId, const std::set<unsigned int>& outputChannels, // 打开音频流（第 1 行）
                                 const std::set<unsigned int>& inputChannels, unsigned int sampleRate) { // 打开音频流（第 2 行）
    if (wasapi.isStreamRunning()) { // 如果流已在运行
        qDebug() << "WASAPI stream already running"; // 输出调试信息：WASAPI 流已在运行
        return false; // 返回失败
    } // 结束 if 语句

    RtAudio::DeviceInfo info = wasapi.getDeviceInfo(deviceId); // 获取设备信息
    if (info.ID == 0 && info.name.empty()) { // 如果设备信息无效
        qDebug() << "Failed to get device info for ID:" << deviceId; // 输出调试信息：获取设备信息失败
        return false; // 返回失败
    } // 结束 if 语句

    if (!outputChannels.empty() && *outputChannels.rbegin() >= info.outputChannels) { // 如果输出通道超出设备能力
        qDebug() << "Output channels exceed device capability:" << *outputChannels.rbegin() << ">=" << info.outputChannels; // 输出调试信息
        return false; // 返回失败
    } // 结束 if 语句
    if (!inputChannels.empty() && *inputChannels.rbegin() >= info.inputChannels) { // 如果输入通道超出设备能力
        qDebug() << "Input channels exceed device capability:" << *inputChannels.rbegin() << ">=" << info.inputChannels; // 输出调试信息
        return false; // 返回失败
    } // 结束 if 语句

    if (std::find(info.sampleRates.begin(), info.sampleRates.end(), sampleRate) == info.sampleRates.end()) { // 如果采样率不支持
        qDebug() << "Sample rate not supported:" << sampleRate; // 输出调试信息：采样率不支持
        return false; // 返回失败
    } // 结束 if 语句

    RtAudio::StreamParameters outParams, inParams; // 定义输出和输入流参数
    outParams.deviceId = deviceId; // 设置输出设备 ID
    outParams.nChannels = outputChannels.empty() ? 0 : *outputChannels.rbegin() + 1; // 设置输出通道数
    outParams.firstChannel = 0; // 设置第一个输出通道
    inParams.deviceId = deviceId; // 设置输入设备 ID
    inParams.nChannels = inputChannels.empty() ? 0 : *inputChannels.rbegin() + 1; // 设置输入通道数
    inParams.firstChannel = 0; // 设置第一个输入通道
    unsigned int bufferFrames = 512; // 设置缓冲区帧数为 512

    RtAudio::StreamOptions options; // 定义流选项
    options.flags = RTAUDIO_HOG_DEVICE | RTAUDIO_SCHEDULE_REALTIME | RTAUDIO_MINIMIZE_LATENCY; // 设置流选项标志

    outputBuffer1.resize(bufferFrames * outParams.nChannels, 0); // 初始化输出缓冲区1
    outputBuffer2.resize(bufferFrames * outParams.nChannels, 0); // 初始化输出缓冲区2
    inputBuffer1.resize(bufferFrames * inParams.nChannels, 0); // 初始化输入缓冲区1
    inputBuffer2.resize(bufferFrames * inParams.nChannels, 0); // 初始化输入缓冲区2

    RtAudioErrorType result = wasapi.openStream( // 打开音频流
        outParams.nChannels ? &outParams : nullptr, // 输出参数（如果有输出通道）
        inParams.nChannels ? &inParams : nullptr, // 输入参数（如果有输入通道）
        FORMAT, sampleRate, &bufferFrames, // 格式、采样率和缓冲区帧数
        &callback, this, &options // 回调函数、用户数据和流选项
    ); // 结束 openStream 调用
    if (result != RTAUDIO_NO_ERROR) { // 如果打开流失败
        qDebug() << "Failed to open WASAPI stream for device ID:" << deviceId << "Error:" << wasapi.getErrorText().c_str(); // 输出调试信息：打开流失败
        return false; // 返回失败
    } // 结束 if 语句

    runningDeviceId = deviceId; // 设置当前运行设备 ID
    activeOutputChannels[deviceId] = outputChannels; // 设置活动输出通道
    activeInputChannels[deviceId] = inputChannels; // 设置活动输入通道
    currentConfig = {sampleRate, bufferFrames, outParams.nChannels, inParams.nChannels}; // 设置当前流配置

    if (outParams.nChannels > 0) { // 如果有输出通道
        emit requestOutputData(bufferFrames, outParams.nChannels); // 发出请求输出数据信号
    } // 结束 if 语句

    qDebug() << "Opening WASAPI stream - Input Channels:" << inputChannels.size() << "nChannels:" << inParams.nChannels; // 输出调试信息：打开 WASAPI 流
    return true; // 返回成功
} // openStream 函数结束

bool AudioHostWasapi::startStream(unsigned int deviceId, const std::set<unsigned int>& outputChannels, // 启动音频流（第 1 行）
                                  const std::set<unsigned int>& inputChannels, unsigned int sampleRate) { // 启动音频流（第 2 行）
    if (!wasapi.isStreamOpen()) { // 如果流未打开
        qDebug() << "WASAPI stream not open, please call openStream first"; // 输出调试信息：流未打开
        return false; // 返回失败
    } // 结束 if 语句

    if (runningDeviceId != deviceId) { // 如果设备 ID 不匹配
        qDebug() << "Cannot reuse WASAPI stream: different device ID"; // 输出调试信息：设备 ID 不同
        return false; // 返回失败
    } // 结束 if 语句

    RtAudioErrorType result = wasapi.startStream(); // 启动流
    if (result != RTAUDIO_NO_ERROR) { // 如果启动失败
        qDebug() << "Failed to start WASAPI stream for device ID:" << deviceId << "Error:" << wasapi.getErrorText().c_str(); // 输出调试信息：启动流失败
        wasapi.closeStream(); // 关闭流
        return false; // 返回失败
    } // 结束 if 语句

    isRunning = true; // 设置运行状态为 true
    activeOutputChannels[deviceId].insert(outputChannels.begin(), outputChannels.end()); // 更新活动输出通道
    activeInputChannels[deviceId].insert(inputChannels.begin(), inputChannels.end()); // 更新活动输入通道

    return true; // 返回成功
} // startStream 函数结束

void AudioHostWasapi::stopStream() { // 停止音频流
    if (wasapi.isStreamRunning()) { // 如果流正在运行
        wasapi.stopStream(); // 停止流
        wasapi.closeStream(); // 关闭流
        isRunning = false; // 设置运行状态为 false
        activeOutputChannels.clear(); // 清空活动输出通道
        activeInputChannels.clear(); // 清空活动输入通道
        runningDeviceId = 0; // 重置运行设备 ID
    } // 结束 if 语句
} // stopStream 函数结束

bool AudioHostWasapi::areChannelsAvailable(unsigned int deviceId, const std::set<unsigned int>& channels, bool isOutput) { // 检查通道是否可用
    auto& activeChannels = isOutput ? activeOutputChannels : activeInputChannels; // 根据类型选择活动通道
    if (activeChannels.find(deviceId) == activeChannels.end()) { // 如果设备 ID 未找到
        return true; // 返回通道可用
    } // 结束 if 语句
    for (const auto& ch : channels) { // 遍历请求的通道
        if (activeChannels[deviceId].count(ch)) { // 如果通道已被占用
            return false; // 返回通道不可用
        } // 结束 if 语句
    } // 结束 for 循环
    return true; // 返回通道可用
} // areChannelsAvailable 函数结束

unsigned int AudioHostWasapi::getRunningDeviceId() const { // 获取当前运行设备 ID
    return runningDeviceId; // 返回运行设备 ID
} // getRunningDeviceId 函数结束

void AudioHostWasapi::confirmInputDataRead() { // 确认输入数据已读取
    inputRead = true; // 设置输入已读取标志
} // confirmInputDataRead 函数结束

void AudioHostWasapi::fillOutputData(const std::vector<int32_t>& data) { // 填充输出数据
    std::lock_guard<std::mutex> lock(bufferMutex); // 加锁保护缓冲区
    std::vector<int32_t>& writeBuffer = useBuffer1 ? outputBuffer1 : outputBuffer2; // 选择当前写入缓冲区
    if (data.size() == writeBuffer.size()) { // 如果数据大小匹配
        writeBuffer = data; // 复制数据到缓冲区
        outputFilled = true; // 设置输出已填充标志
        qDebug() << "Filled output buffer - Size:" << data.size() << "Use Buffer1:" << useBuffer1; // 输出调试信息：填充输出缓冲区
    } else { // 如果数据大小不匹配
        qDebug() << "WASAPI output data size mismatch - Expected:" << writeBuffer.size() << "Actual:" << data.size(); // 输出调试信息：数据大小不匹配
    } // 结束 if 语句
} // fillOutputData 函数结束

int AudioHostWasapi::callback(void *outputBuffer, void *inputBuffer, unsigned int nBufferFrames, // 音频流回调函数（第 1 行）
                              double streamTime, RtAudioStreamStatus status, void *userData) { // 音频流回调函数（第 2 行）
    AudioHostWasapi *host = static_cast<AudioHostWasapi*>(userData); // 获取主机对象
    double frameTime = nBufferFrames / (double)host->currentConfig.sampleRate; // 计算帧时间
    if (host->lastCallbackTime > 0.0 && streamTime - host->lastCallbackTime > frameTime * 1.5) { // 如果回调间隔过长
        if (!host->outputFilled) { // 如果输出未填充
            emit host->timeoutWarning("WASAPI: Output data not filled in time!"); // 发出超时警告信号
        } // 结束 if 语句
        if (!host->inputRead && host->currentConfig.inputChannelCount > 0) { // 如果输入未读取
            emit host->timeoutWarning("WASAPI: Input data not read in time!"); // 发出超时警告信号
        } // 结束 if 语句
    } // 结束 if 语句
    host->lastCallbackTime = streamTime; // 更新上次回调时间

    std::lock_guard<std::mutex> lock(host->bufferMutex); // 加锁保护缓冲区
    std::vector<int32_t>& readOutputBuffer = host->useBuffer1 ? host->outputBuffer2 : host->outputBuffer1; // 选择读取输出缓冲区
    std::vector<int32_t>& writeInputBuffer = host->useBuffer1 ? host->inputBuffer1 : host->inputBuffer2; // 选择写入输入缓冲区

    if (outputBuffer && host->currentConfig.outputChannelCount > 0) { // 如果有输出缓冲区和输出通道
        int32_t *out = static_cast<int32_t*>(outputBuffer); // 转换为 int32_t 指针
        for (size_t i = 0; i < readOutputBuffer.size(); ++i) { // 复制输出数据
            out[i] = readOutputBuffer[i]; // 将缓冲区数据写入输出
        } // 结束 for 循环
        host->outputFilled = false; // 重置输出已填充标志
        emit host->requestOutputData(nBufferFrames, host->currentConfig.outputChannelCount); // 发出请求输出数据信号
    } // 结束 if 语句

    if (inputBuffer && host->currentConfig.inputChannelCount > 0) { // 如果有输入缓冲区和输入通道
        int32_t *in = static_cast<int32_t*>(inputBuffer); // 转换为 int32_t 指针
        writeInputBuffer.assign(in, in + nBufferFrames * host->currentConfig.inputChannelCount); // 复制输入数据
        host->inputRead = false; // 重置输入已读取标志
        emit host->inputDataAvailable(writeInputBuffer, host->currentConfig.inputChannelCount); // 发出输入数据可用信号
        qDebug() << "WASAPI callback - Input Frames:" << nBufferFrames; // 输出调试信息：回调输入帧数
    } // 结束 if 语句

    host->useBuffer1 = !host->useBuffer1; // 切换缓冲区
    return 0; // 返回 0 表示成功
} // callback 函数结束

void AudioHostWasapi::errorCallback(RtAudioErrorType type, const std::string &errorText) { // 错误回调函数
    qDebug() << "WASAPI Error:" << QString::fromStdString(errorText); // 输出错误信息
} // errorCallback 函数结束
