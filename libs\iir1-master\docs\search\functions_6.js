var searchData=
[
  ['setcoefficients_160',['setCoefficients',['../classIir_1_1Biquad.html#a7f005089c194c68aeecdc66a3e6c6a78',1,'Iir::Biquad']]],
  ['setidentity_161',['setIdentity',['../classIir_1_1Biquad.html#a53b066d077ce559a91f26fc3c4201c2c',1,'Iir::Biquad']]],
  ['setonepole_162',['setOnePole',['../classIir_1_1Biquad.html#a455fd42a2e99ac84b09184becf2d047f',1,'Iir::Biquad']]],
  ['setpolezeropair_163',['setPoleZeroPair',['../classIir_1_1Biquad.html#a69b4a2eaedb4b51aea9fb46a99c3d3c2',1,'Iir::Biquad']]],
  ['settwopole_164',['setTwoPole',['../classIir_1_1Biquad.html#a6c614e84db8493b8495b314dd860d0bc',1,'Iir::Biquad']]],
  ['setup_165',['setup',['../structIir_1_1ChebyshevII_1_1HighShelf.html#afdf4c782aa49f832bce8cdca5404aec4',1,'Iir::ChebyshevII::HighShelf::setup()'],['../structIir_1_1ChebyshevI_1_1BandShelf.html#abb0e9a1480addb5b4ce38b2d83888c95',1,'Iir::ChebyshevI::BandShelf::setup()'],['../structIir_1_1ChebyshevII_1_1LowShelf.html#a25365483119efa01be73c48fcf7dd75b',1,'Iir::ChebyshevII::LowShelf::setup(int reqOrder, double sampleRate, double cutoffFrequency, double gainDb, double stopBandDb)'],['../structIir_1_1ChebyshevII_1_1LowShelf.html#a7b93d59263561ce231f38293cc03b225',1,'Iir::ChebyshevII::LowShelf::setup(double sampleRate, double cutoffFrequency, double gainDb, double stopBandDb)'],['../structIir_1_1ChebyshevII_1_1BandStop.html#abb424ebd2398df91406042580b25a173',1,'Iir::ChebyshevII::BandStop::setup(int reqOrder, double sampleRate, double centerFrequency, double widthFrequency, double stopBandDb)'],['../structIir_1_1ChebyshevII_1_1BandStop.html#a2cced9fbf0ac69ac0f20192bc6d8375c',1,'Iir::ChebyshevII::BandStop::setup(double sampleRate, double centerFrequency, double widthFrequency, double stopBandDb)'],['../structIir_1_1ChebyshevII_1_1BandPass.html#a1157ea7f70718df1e2983fb0f576931f',1,'Iir::ChebyshevII::BandPass::setup(int reqOrder, double sampleRate, double centerFrequency, double widthFrequency, double stopBandDb)'],['../structIir_1_1ChebyshevII_1_1BandPass.html#a7fcada7f9376bd6cf0d99a18aa62ee32',1,'Iir::ChebyshevII::BandPass::setup(double sampleRate, double centerFrequency, double widthFrequency, double stopBandDb)'],['../structIir_1_1ChebyshevII_1_1HighPass.html#a415aac5f061addfcabe198bb017742de',1,'Iir::ChebyshevII::HighPass::setup(int reqOrder, double sampleRate, double cutoffFrequency, double stopBandDb)'],['../structIir_1_1ChebyshevII_1_1HighPass.html#a6fc4ac9b61747fb4cfb6d1ee7c775f31',1,'Iir::ChebyshevII::HighPass::setup(double sampleRate, double cutoffFrequency, double stopBandDb)'],['../structIir_1_1ChebyshevII_1_1LowPass.html#ae966204946d7ce91c9656cb20b2162ef',1,'Iir::ChebyshevII::LowPass::setup(int reqOrder, double sampleRate, double cutoffFrequency, double stopBandDb)'],['../structIir_1_1ChebyshevII_1_1LowPass.html#a2137a9586df475b31d1402edfdd9e74a',1,'Iir::ChebyshevII::LowPass::setup(double sampleRate, double cutoffFrequency, double stopBandDb)'],['../structIir_1_1RBJ_1_1BandShelf.html#ae4b2c50b6cf6715d881f9c3681530870',1,'Iir::RBJ::BandShelf::setup()'],['../structIir_1_1ChebyshevII_1_1HighShelf.html#a3efb4a8649c08ab5095174804cd2edfa',1,'Iir::ChebyshevII::HighShelf::setup()'],['../structIir_1_1ChebyshevII_1_1BandShelf.html#adca87357a1e04fe298144cae7b200670',1,'Iir::ChebyshevII::BandShelf::setup(double sampleRate, double centerFrequency, double widthFrequency, double gainDb, double stopBandDb)'],['../structIir_1_1ChebyshevII_1_1BandShelf.html#a335408ec59e8673fd18dfd603096c7f5',1,'Iir::ChebyshevII::BandShelf::setup(int reqOrder, double sampleRate, double centerFrequency, double widthFrequency, double gainDb, double stopBandDb)'],['../structIir_1_1Custom_1_1SOSCascade.html#aae603eb1bd4330411a6003e09ba36efc',1,'Iir::Custom::SOSCascade::setup()'],['../structIir_1_1RBJ_1_1LowPass.html#adc6ffd4bab0cdc4f665f667841b96cea',1,'Iir::RBJ::LowPass::setup()'],['../structIir_1_1RBJ_1_1HighPass.html#a445c9bba06d82c972e389fbae45962e9',1,'Iir::RBJ::HighPass::setup()'],['../structIir_1_1RBJ_1_1BandPass1.html#a82ba843c68435a5a09cc16fdbf358afc',1,'Iir::RBJ::BandPass1::setup()'],['../structIir_1_1RBJ_1_1BandPass2.html#a2173d305036d1e9d842c249aa34e4cde',1,'Iir::RBJ::BandPass2::setup()'],['../structIir_1_1RBJ_1_1BandStop.html#afe29086ad05ecfe2c0697a09bfa45c49',1,'Iir::RBJ::BandStop::setup()'],['../structIir_1_1RBJ_1_1IIRNotch.html#aedecb6358bc0a907742c56d5235d35df',1,'Iir::RBJ::IIRNotch::setup()'],['../structIir_1_1RBJ_1_1LowShelf.html#a9bac813cc8399f3c274d9d10dc3a5a1e',1,'Iir::RBJ::LowShelf::setup()'],['../structIir_1_1RBJ_1_1HighShelf.html#a4887616ad520c27fce55d76d915d5bf6',1,'Iir::RBJ::HighShelf::setup()'],['../structIir_1_1RBJ_1_1AllPass.html#ae68e5b4a22b3256b52798cddc3ff653c',1,'Iir::RBJ::AllPass::setup()'],['../structIir_1_1Butterworth_1_1BandStop.html#a88300cc9eef7b5eda35f0381f7238c16',1,'Iir::Butterworth::BandStop::setup()'],['../structIir_1_1ChebyshevI_1_1BandShelf.html#a959d521af6b77dc269814c85543cff2f',1,'Iir::ChebyshevI::BandShelf::setup()'],['../structIir_1_1Butterworth_1_1LowPass.html#a21da9f4c29a8dbf80f4611677a86d777',1,'Iir::Butterworth::LowPass::setup(double sampleRate, double cutoffFrequency)'],['../structIir_1_1Butterworth_1_1LowPass.html#a0e10c27ec72f076bf7d0b21c0281860c',1,'Iir::Butterworth::LowPass::setup(int reqOrder, double sampleRate, double cutoffFrequency)'],['../structIir_1_1Butterworth_1_1HighPass.html#af7f7939a2e78245305cccd35aae4c75f',1,'Iir::Butterworth::HighPass::setup(double sampleRate, double cutoffFrequency)'],['../structIir_1_1Butterworth_1_1HighPass.html#ac9835800f12a982c160720cebef2f963',1,'Iir::Butterworth::HighPass::setup(int reqOrder, double sampleRate, double cutoffFrequency)'],['../structIir_1_1Butterworth_1_1BandPass.html#a73d7b106f2da5ccd5defe5e86f7b8ffd',1,'Iir::Butterworth::BandPass::setup(double sampleRate, double centerFrequency, double widthFrequency)'],['../structIir_1_1Butterworth_1_1BandPass.html#a4e01abd204275ae2223e5197bfc149dc',1,'Iir::Butterworth::BandPass::setup(int reqOrder, double sampleRate, double centerFrequency, double widthFrequency)'],['../structIir_1_1Butterworth_1_1LowShelf.html#a74f72a17ffe747ad5b0e9f36235fcd34',1,'Iir::Butterworth::LowShelf::setup(double sampleRate, double cutoffFrequency, double gainDb)'],['../structIir_1_1Butterworth_1_1LowShelf.html#a660509a6bac1d3b7c10215806d5be5e2',1,'Iir::Butterworth::LowShelf::setup(int reqOrder, double sampleRate, double cutoffFrequency, double gainDb)'],['../structIir_1_1Butterworth_1_1HighShelf.html#a19a78cb96c9b2454ccb48745ed50500d',1,'Iir::Butterworth::HighShelf::setup(double sampleRate, double cutoffFrequency, double gainDb)'],['../structIir_1_1Butterworth_1_1HighShelf.html#aa7fecdca7646acf8962c5d0106fb8b3c',1,'Iir::Butterworth::HighShelf::setup(int reqOrder, double sampleRate, double cutoffFrequency, double gainDb)'],['../structIir_1_1Butterworth_1_1BandShelf.html#a0de40c55dae63d23127cf4dd21bcf050',1,'Iir::Butterworth::BandShelf::setup(double sampleRate, double centerFrequency, double widthFrequency, double gainDb)'],['../structIir_1_1Butterworth_1_1BandShelf.html#aa3f716cf9d393509c2caefb9e10ae8cc',1,'Iir::Butterworth::BandShelf::setup(int reqOrder, double sampleRate, double centerFrequency, double widthFrequency, double gainDb)'],['../structIir_1_1ChebyshevI_1_1LowPass.html#aea095d559db3f6dc7f9df5c8294ab696',1,'Iir::ChebyshevI::LowPass::setup()'],['../structIir_1_1ChebyshevI_1_1HighShelf.html#a6e8901d8a4c2b804f4a4df780f1937cc',1,'Iir::ChebyshevI::HighShelf::setup(int reqOrder, double sampleRate, double cutoffFrequency, double gainDb, double rippleDb)'],['../structIir_1_1ChebyshevI_1_1HighShelf.html#a9fe4f6cee3b4b1aa4688ea21d3f5202f',1,'Iir::ChebyshevI::HighShelf::setup(double sampleRate, double cutoffFrequency, double gainDb, double rippleDb)'],['../structIir_1_1ChebyshevI_1_1LowShelf.html#af6a210d8a2199456748e14efd3e5e05c',1,'Iir::ChebyshevI::LowShelf::setup(int reqOrder, double sampleRate, double cutoffFrequency, double gainDb, double rippleDb)'],['../structIir_1_1ChebyshevI_1_1LowShelf.html#a310d49b7a15137b915969e8d8b12379d',1,'Iir::ChebyshevI::LowShelf::setup(double sampleRate, double cutoffFrequency, double gainDb, double rippleDb)'],['../structIir_1_1ChebyshevI_1_1BandStop.html#a78e0e02cc47abebbd2271345605b12a2',1,'Iir::ChebyshevI::BandStop::setup(int reqOrder, double sampleRate, double centerFrequency, double widthFrequency, double rippleDb)'],['../structIir_1_1ChebyshevI_1_1BandStop.html#af269e777d9c7bae864571bd0db1db6ea',1,'Iir::ChebyshevI::BandStop::setup(double sampleRate, double centerFrequency, double widthFrequency, double rippleDb)'],['../classIir_1_1CascadeStages.html#a56d24da4cf09f898b534b1b05578244d',1,'Iir::CascadeStages::setup()'],['../structIir_1_1ChebyshevI_1_1BandPass.html#ac52fcb50fb3022ac8290e07e954a0ab0',1,'Iir::ChebyshevI::BandPass::setup()'],['../structIir_1_1ChebyshevI_1_1HighPass.html#a0ecdb8e5692663a043f49db30277ba37',1,'Iir::ChebyshevI::HighPass::setup(int reqOrder, double sampleRate, double cutoffFrequency, double rippleDb)'],['../structIir_1_1ChebyshevI_1_1HighPass.html#a031b43bd2545c4b0aba616c2464e1a65',1,'Iir::ChebyshevI::HighPass::setup(double sampleRate, double cutoffFrequency, double rippleDb)'],['../structIir_1_1ChebyshevI_1_1LowPass.html#a6ff5b40576b962bc35fa10eb2903873e',1,'Iir::ChebyshevI::LowPass::setup()'],['../structIir_1_1ChebyshevI_1_1BandPass.html#afbd6dea93c75cdb5b7af375aa7810eff',1,'Iir::ChebyshevI::BandPass::setup()']]],
  ['setupn_166',['setupN',['../structIir_1_1ChebyshevII_1_1LowPass.html#a04ca8a1f1c96318e564108056b4b49a9',1,'Iir::ChebyshevII::LowPass::setupN()'],['../structIir_1_1ChebyshevII_1_1LowShelf.html#a29b862d094f91de1254d01c7d8d7df02',1,'Iir::ChebyshevII::LowShelf::setupN(int reqOrder, double cutoffFrequency, double gainDb, double stopBandDb)'],['../structIir_1_1ChebyshevII_1_1LowShelf.html#acde3502dc4ee16cdf786e14036d70d1f',1,'Iir::ChebyshevII::LowShelf::setupN(double cutoffFrequency, double gainDb, double stopBandDb)'],['../structIir_1_1ChebyshevII_1_1BandStop.html#a4abf9f03fa285f15001d8d99f0f8fab4',1,'Iir::ChebyshevII::BandStop::setupN(int reqOrder, double centerFrequency, double widthFrequency, double stopBandDb)'],['../structIir_1_1ChebyshevII_1_1BandStop.html#ab91192c2d3bf48f69849aba72c0c1c31',1,'Iir::ChebyshevII::BandStop::setupN(double centerFrequency, double widthFrequency, double stopBandDb)'],['../structIir_1_1ChebyshevII_1_1BandPass.html#ad17b6f448cb0395d0a4fe4843ac0c44b',1,'Iir::ChebyshevII::BandPass::setupN(int reqOrder, double centerFrequency, double widthFrequency, double stopBandDb)'],['../structIir_1_1ChebyshevII_1_1BandPass.html#a6044cfbd6da7f2f6f78e9a2d969038d3',1,'Iir::ChebyshevII::BandPass::setupN(double centerFrequency, double widthFrequency, double stopBandDb)'],['../structIir_1_1ChebyshevII_1_1HighPass.html#a8e7a7015cd832b4bd99f002389fbc8e0',1,'Iir::ChebyshevII::HighPass::setupN(int reqOrder, double cutoffFrequency, double stopBandDb)'],['../structIir_1_1ChebyshevII_1_1HighPass.html#aa1e4fe46e854bdfa9f1c26e901fcafbd',1,'Iir::ChebyshevII::HighPass::setupN(double cutoffFrequency, double stopBandDb)'],['../structIir_1_1ChebyshevII_1_1LowPass.html#a51d676e2d229e776ae04ed8b6d028612',1,'Iir::ChebyshevII::LowPass::setupN()'],['../structIir_1_1ChebyshevI_1_1BandShelf.html#a6aa48c5f0f3796ffc9b0384acc8c42fa',1,'Iir::ChebyshevI::BandShelf::setupN()'],['../structIir_1_1Butterworth_1_1BandShelf.html#a96cc173942cd6789b5aff603b37e5b2f',1,'Iir::Butterworth::BandShelf::setupN()'],['../structIir_1_1ChebyshevI_1_1BandShelf.html#aef32bdf8e3209f0ab169d2641781ad6c',1,'Iir::ChebyshevI::BandShelf::setupN()'],['../structIir_1_1ChebyshevII_1_1HighShelf.html#a092b00347c75913eb845080d753853ee',1,'Iir::ChebyshevII::HighShelf::setupN(double cutoffFrequency, double gainDb, double stopBandDb)'],['../structIir_1_1ChebyshevII_1_1HighShelf.html#a713d1708e646df97794eacc8541605e2',1,'Iir::ChebyshevII::HighShelf::setupN(int reqOrder, double cutoffFrequency, double gainDb, double stopBandDb)'],['../structIir_1_1ChebyshevII_1_1BandShelf.html#a78ef454e71eb75c85c74e2cdb66e08fd',1,'Iir::ChebyshevII::BandShelf::setupN(double centerFrequency, double widthFrequency, double gainDb, double stopBandDb)'],['../structIir_1_1ChebyshevII_1_1BandShelf.html#a468f2edb4849f8f60b65bdfbb3e309e3',1,'Iir::ChebyshevII::BandShelf::setupN(int reqOrder, double centerFrequency, double widthFrequency, double gainDb, double stopBandDb)'],['../structIir_1_1RBJ_1_1LowPass.html#a210539758e8e95da26b74079b4219388',1,'Iir::RBJ::LowPass::setupN()'],['../structIir_1_1RBJ_1_1HighPass.html#aa990e5f563666cd2d3cdc48cd77d5f2b',1,'Iir::RBJ::HighPass::setupN()'],['../structIir_1_1RBJ_1_1BandPass1.html#a3af22e73e4809afa1349aeeee2d6452b',1,'Iir::RBJ::BandPass1::setupN()'],['../structIir_1_1RBJ_1_1BandPass2.html#aaad03d74806380bb4df721bceaed0249',1,'Iir::RBJ::BandPass2::setupN()'],['../structIir_1_1RBJ_1_1BandStop.html#a52326e7ac833ac14e7a8859548fb321d',1,'Iir::RBJ::BandStop::setupN()'],['../structIir_1_1RBJ_1_1IIRNotch.html#a824ce85d420097081e71b3888acb7862',1,'Iir::RBJ::IIRNotch::setupN()'],['../structIir_1_1RBJ_1_1LowShelf.html#a006830eb278deb38a4cfc0c3240d4a9a',1,'Iir::RBJ::LowShelf::setupN()'],['../structIir_1_1RBJ_1_1HighShelf.html#a0d5dbf5b05c08663bedf00e4a7f983db',1,'Iir::RBJ::HighShelf::setupN()'],['../structIir_1_1RBJ_1_1BandShelf.html#ae5184f5a8c32602689c866128a3471c1',1,'Iir::RBJ::BandShelf::setupN()'],['../structIir_1_1RBJ_1_1AllPass.html#ab0ca96fe3066c0bc2206a47d51c6bf5a',1,'Iir::RBJ::AllPass::setupN()'],['../structIir_1_1ChebyshevI_1_1HighShelf.html#aa078bdf604ac9feeedd282174bd0cea9',1,'Iir::ChebyshevI::HighShelf::setupN()'],['../structIir_1_1Butterworth_1_1LowPass.html#a29cb3cdc2e4e8f8f5e76f8942dcbd249',1,'Iir::Butterworth::LowPass::setupN(double cutoffFrequency)'],['../structIir_1_1Butterworth_1_1LowPass.html#af8fc4401089d8d36508a468dc09e74a0',1,'Iir::Butterworth::LowPass::setupN(int reqOrder, double cutoffFrequency)'],['../structIir_1_1Butterworth_1_1HighPass.html#ac33912cbd0d3d65acdda0c53d5b64274',1,'Iir::Butterworth::HighPass::setupN(double cutoffFrequency)'],['../structIir_1_1Butterworth_1_1HighPass.html#a7602019a900b288a16963126de49c2f1',1,'Iir::Butterworth::HighPass::setupN(int reqOrder, double cutoffFrequency)'],['../structIir_1_1Butterworth_1_1BandPass.html#ac33b003f6c77021f90a998d0e03b0e4d',1,'Iir::Butterworth::BandPass::setupN(double centerFrequency, double widthFrequency)'],['../structIir_1_1Butterworth_1_1BandPass.html#a50e74a28deeaaa6d4df4c68b3ae562a7',1,'Iir::Butterworth::BandPass::setupN(int reqOrder, double centerFrequency, double widthFrequency)'],['../structIir_1_1Butterworth_1_1BandStop.html#a7d72486d044fef3dce19c9756de995ba',1,'Iir::Butterworth::BandStop::setupN(int reqOrder, double sampleRate, double centerFrequency, double widthFrequency)'],['../structIir_1_1Butterworth_1_1BandStop.html#aff496377a74e44a7c5e88e5d9b5abe5c',1,'Iir::Butterworth::BandStop::setupN(double centerFrequency, double widthFrequency)'],['../structIir_1_1Butterworth_1_1BandStop.html#ab88fa7f17faa3de891c9db4766b3cba6',1,'Iir::Butterworth::BandStop::setupN(int reqOrder, double centerFrequency, double widthFrequency)'],['../structIir_1_1Butterworth_1_1LowShelf.html#a8bc2be489d0a5b901b02b9e2b62d7053',1,'Iir::Butterworth::LowShelf::setupN(double cutoffFrequency, double gainDb)'],['../structIir_1_1Butterworth_1_1LowShelf.html#a4637158be17c50cb14cf3686b30e135e',1,'Iir::Butterworth::LowShelf::setupN(int reqOrder, double cutoffFrequency, double gainDb)'],['../structIir_1_1Butterworth_1_1HighShelf.html#af63e4f43001ae2d9faf44ba6496db562',1,'Iir::Butterworth::HighShelf::setupN(double cutoffFrequency, double gainDb)'],['../structIir_1_1Butterworth_1_1HighShelf.html#a7df553be87ce22541c6ffa8af7508898',1,'Iir::Butterworth::HighShelf::setupN(int reqOrder, double cutoffFrequency, double gainDb)'],['../structIir_1_1Butterworth_1_1BandShelf.html#a43e02ec9dcfcd382b87ce20bc48f2ee2',1,'Iir::Butterworth::BandShelf::setupN()'],['../structIir_1_1ChebyshevI_1_1LowPass.html#a52ea5dcac2a5cdff2a73c3622ac55ce1',1,'Iir::ChebyshevI::LowPass::setupN(double cutoffFrequency, double rippleDb)'],['../structIir_1_1ChebyshevI_1_1LowPass.html#a249a9236b0584edced2168a60b667106',1,'Iir::ChebyshevI::LowPass::setupN(int reqOrder, double cutoffFrequency, double rippleDb)'],['../structIir_1_1ChebyshevI_1_1HighPass.html#aea3ad8ee966575c3a6680758f3b22123',1,'Iir::ChebyshevI::HighPass::setupN(double cutoffFrequency, double rippleDb)'],['../structIir_1_1ChebyshevI_1_1HighPass.html#a634d827d4c59b83e4eb29ad5a2c97eb5',1,'Iir::ChebyshevI::HighPass::setupN(int reqOrder, double cutoffFrequency, double rippleDb)'],['../structIir_1_1ChebyshevI_1_1BandPass.html#aa10de012e157fb8ec55dcd0d0a2813d7',1,'Iir::ChebyshevI::BandPass::setupN(double centerFrequency, double widthFrequency, double rippleDb)'],['../structIir_1_1ChebyshevI_1_1BandPass.html#a1cbadc704fcb119e98bf13a62f61e0b7',1,'Iir::ChebyshevI::BandPass::setupN(int reqOrder, double centerFrequency, double widthFrequency, double rippleDb)'],['../structIir_1_1ChebyshevI_1_1BandStop.html#a2391183ce4dda61f2feab75af3587af5',1,'Iir::ChebyshevI::BandStop::setupN(double centerFrequency, double widthFrequency, double rippleDb)'],['../structIir_1_1ChebyshevI_1_1BandStop.html#add75db747d3b2ce89394337927603997',1,'Iir::ChebyshevI::BandStop::setupN(int reqOrder, double centerFrequency, double widthFrequency, double rippleDb)'],['../structIir_1_1ChebyshevI_1_1LowShelf.html#a610b49d218c77ab80438a7ada4fc93a5',1,'Iir::ChebyshevI::LowShelf::setupN(double cutoffFrequency, double gainDb, double rippleDb)'],['../structIir_1_1ChebyshevI_1_1LowShelf.html#ae0caf0b8c0dc3e39e31eac6efb938547',1,'Iir::ChebyshevI::LowShelf::setupN(int reqOrder, double cutoffFrequency, double gainDb, double rippleDb)'],['../structIir_1_1ChebyshevI_1_1HighShelf.html#adbfa0e3487393075e3f178d50293f0bb',1,'Iir::ChebyshevI::HighShelf::setupN()']]],
  ['soscascade_167',['SOSCascade',['../structIir_1_1Custom_1_1SOSCascade.html#a04671c1c84cacaf1c822adeb850ccc63',1,'Iir::Custom::SOSCascade::SOSCascade()=default'],['../structIir_1_1Custom_1_1SOSCascade.html#a60654fe4bd861799dff82a6dce9dfd42',1,'Iir::Custom::SOSCascade::SOSCascade(const double(&amp;sosCoefficients)[NSOS][6])']]]
];
