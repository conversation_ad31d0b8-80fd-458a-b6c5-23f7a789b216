\hypertarget{structIir_1_1Butterworth_1_1BandPass}{}\doxysubsection{Iir\+::Butterworth\+::Band\+Pass$<$ Filter\+Order, State\+Type $>$ Struct Template Reference}
\label{structIir_1_1Butterworth_1_1BandPass}\index{Iir::Butterworth::BandPass$<$ FilterOrder, StateType $>$@{Iir::Butterworth::BandPass$<$ FilterOrder, StateType $>$}}


{\ttfamily \#include $<$Butterworth.\+h$>$}

Inheritance diagram for Iir\+::Butterworth\+::Band\+Pass$<$ Filter\+Order, State\+Type $>$\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=2.434783cm]{structIir_1_1Butterworth_1_1BandPass}
\end{center}
\end{figure}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
void \mbox{\hyperlink{structIir_1_1Butterworth_1_1BandPass_a73d7b106f2da5ccd5defe5e86f7b8ffd}{setup}} (double sample\+Rate, double center\+Frequency, double width\+Frequency)
\item 
void \mbox{\hyperlink{structIir_1_1Butterworth_1_1BandPass_a4e01abd204275ae2223e5197bfc149dc}{setup}} (int req\+Order, double sample\+Rate, double center\+Frequency, double width\+Frequency)
\item 
void \mbox{\hyperlink{structIir_1_1Butterworth_1_1BandPass_ac33b003f6c77021f90a998d0e03b0e4d}{setupN}} (double center\+Frequency, double width\+Frequency)
\item 
void \mbox{\hyperlink{structIir_1_1Butterworth_1_1BandPass_a50e74a28deeaaa6d4df4c68b3ae562a7}{setupN}} (int req\+Order, double center\+Frequency, double width\+Frequency)
\end{DoxyCompactItemize}


\doxysubsubsection{Detailed Description}
\subsubsection*{template$<$int Filter\+Order = 4, class State\+Type = Direct\+Form\+II$>$\newline
struct Iir\+::\+Butterworth\+::\+Band\+Pass$<$ Filter\+Order, State\+Type $>$}

\mbox{\hyperlink{namespaceIir_1_1Butterworth}{Butterworth}} Bandpass filter. 
\begin{DoxyParams}{Parameters}
{\em Filter\+Order} & Reserves memory for a filter of the order Filter\+Order \\
\hline
{\em State\+Type} & The filter topology\+: \mbox{\hyperlink{classIir_1_1DirectFormI}{Direct\+FormI}}, \mbox{\hyperlink{classIir_1_1DirectFormII}{Direct\+Form\+II}}, ... \\
\hline
\end{DoxyParams}


\doxysubsubsection{Member Function Documentation}
\mbox{\Hypertarget{structIir_1_1Butterworth_1_1BandPass_a73d7b106f2da5ccd5defe5e86f7b8ffd}\label{structIir_1_1Butterworth_1_1BandPass_a73d7b106f2da5ccd5defe5e86f7b8ffd}} 
\index{Iir::Butterworth::BandPass$<$ FilterOrder, StateType $>$@{Iir::Butterworth::BandPass$<$ FilterOrder, StateType $>$}!setup@{setup}}
\index{setup@{setup}!Iir::Butterworth::BandPass$<$ FilterOrder, StateType $>$@{Iir::Butterworth::BandPass$<$ FilterOrder, StateType $>$}}
\doxyparagraph{\texorpdfstring{setup()}{setup()}\hspace{0.1cm}{\footnotesize\ttfamily [1/2]}}
{\footnotesize\ttfamily template$<$int Filter\+Order = 4, class State\+Type  = Direct\+Form\+II$>$ \\
void \mbox{\hyperlink{structIir_1_1Butterworth_1_1BandPass}{Iir\+::\+Butterworth\+::\+Band\+Pass}}$<$ Filter\+Order, State\+Type $>$\+::setup (\begin{DoxyParamCaption}\item[{double}]{sample\+Rate,  }\item[{double}]{center\+Frequency,  }\item[{double}]{width\+Frequency }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients with the filter order provided by the instantiation 
\begin{DoxyParams}{Parameters}
{\em sample\+Rate} & Sampling rate \\
\hline
{\em center\+Frequency} & Centre frequency of the bandpass \\
\hline
{\em width\+Frequency} & Width of the bandpass \\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{structIir_1_1Butterworth_1_1BandPass_a4e01abd204275ae2223e5197bfc149dc}\label{structIir_1_1Butterworth_1_1BandPass_a4e01abd204275ae2223e5197bfc149dc}} 
\index{Iir::Butterworth::BandPass$<$ FilterOrder, StateType $>$@{Iir::Butterworth::BandPass$<$ FilterOrder, StateType $>$}!setup@{setup}}
\index{setup@{setup}!Iir::Butterworth::BandPass$<$ FilterOrder, StateType $>$@{Iir::Butterworth::BandPass$<$ FilterOrder, StateType $>$}}
\doxyparagraph{\texorpdfstring{setup()}{setup()}\hspace{0.1cm}{\footnotesize\ttfamily [2/2]}}
{\footnotesize\ttfamily template$<$int Filter\+Order = 4, class State\+Type  = Direct\+Form\+II$>$ \\
void \mbox{\hyperlink{structIir_1_1Butterworth_1_1BandPass}{Iir\+::\+Butterworth\+::\+Band\+Pass}}$<$ Filter\+Order, State\+Type $>$\+::setup (\begin{DoxyParamCaption}\item[{int}]{req\+Order,  }\item[{double}]{sample\+Rate,  }\item[{double}]{center\+Frequency,  }\item[{double}]{width\+Frequency }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients 
\begin{DoxyParams}{Parameters}
{\em req\+Order} & The actual order which can be less than the instantiated one \\
\hline
{\em sample\+Rate} & Sampling rate \\
\hline
{\em center\+Frequency} & Centre frequency of the bandpass \\
\hline
{\em width\+Frequency} & Width of the bandpass \\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{structIir_1_1Butterworth_1_1BandPass_ac33b003f6c77021f90a998d0e03b0e4d}\label{structIir_1_1Butterworth_1_1BandPass_ac33b003f6c77021f90a998d0e03b0e4d}} 
\index{Iir::Butterworth::BandPass$<$ FilterOrder, StateType $>$@{Iir::Butterworth::BandPass$<$ FilterOrder, StateType $>$}!setupN@{setupN}}
\index{setupN@{setupN}!Iir::Butterworth::BandPass$<$ FilterOrder, StateType $>$@{Iir::Butterworth::BandPass$<$ FilterOrder, StateType $>$}}
\doxyparagraph{\texorpdfstring{setupN()}{setupN()}\hspace{0.1cm}{\footnotesize\ttfamily [1/2]}}
{\footnotesize\ttfamily template$<$int Filter\+Order = 4, class State\+Type  = Direct\+Form\+II$>$ \\
void \mbox{\hyperlink{structIir_1_1Butterworth_1_1BandPass}{Iir\+::\+Butterworth\+::\+Band\+Pass}}$<$ Filter\+Order, State\+Type $>$\+::setupN (\begin{DoxyParamCaption}\item[{double}]{center\+Frequency,  }\item[{double}]{width\+Frequency }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients with the filter order provided by the instantiation 
\begin{DoxyParams}{Parameters}
{\em center\+Frequency} & Normalised centre frequency (0..1/2) of the bandpass \\
\hline
{\em width\+Frequency} & Width of the bandpass in normalised freq \\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{structIir_1_1Butterworth_1_1BandPass_a50e74a28deeaaa6d4df4c68b3ae562a7}\label{structIir_1_1Butterworth_1_1BandPass_a50e74a28deeaaa6d4df4c68b3ae562a7}} 
\index{Iir::Butterworth::BandPass$<$ FilterOrder, StateType $>$@{Iir::Butterworth::BandPass$<$ FilterOrder, StateType $>$}!setupN@{setupN}}
\index{setupN@{setupN}!Iir::Butterworth::BandPass$<$ FilterOrder, StateType $>$@{Iir::Butterworth::BandPass$<$ FilterOrder, StateType $>$}}
\doxyparagraph{\texorpdfstring{setupN()}{setupN()}\hspace{0.1cm}{\footnotesize\ttfamily [2/2]}}
{\footnotesize\ttfamily template$<$int Filter\+Order = 4, class State\+Type  = Direct\+Form\+II$>$ \\
void \mbox{\hyperlink{structIir_1_1Butterworth_1_1BandPass}{Iir\+::\+Butterworth\+::\+Band\+Pass}}$<$ Filter\+Order, State\+Type $>$\+::setupN (\begin{DoxyParamCaption}\item[{int}]{req\+Order,  }\item[{double}]{center\+Frequency,  }\item[{double}]{width\+Frequency }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients 
\begin{DoxyParams}{Parameters}
{\em req\+Order} & The actual order which can be less than the instantiated one \\
\hline
{\em center\+Frequency} & Normalised centre frequency (0..1/2) of the bandpass \\
\hline
{\em width\+Frequency} & Width of the bandpass in normalised freq \\
\hline
\end{DoxyParams}


The documentation for this struct was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
iir/Butterworth.\+h\end{DoxyCompactItemize}
