﻿#ifndef AUDIOPROCESSOR_H // 防止头文件重复包含的宏定义
#define AUDIOPROCESSOR_H // 定义 AUDIOPROCESSOR_H 宏

// 核心功能（Core）：提供 Qt 对象模型和信号槽机制
#include <QObject>

// 数据结构（Data Structures）：支持键值对映射、字符串列表和智能指针
#include <QMap>
#include <QSharedPointer>
#include <QStringList>

// 线程与时间（Threading and Timing）：处理线程同步、时间操作和并发队列
#include <QMutex>
#include <QTimer>
#include <QDateTime>
#include "concurrentqueue.h" // 包含 concurrentqueue 头文件，用于无锁队列

// 用户交互（User Interaction）：处理弹窗提示
#include <QMessageBox>

// 数学与算法（Math and Algorithms）：提供数学计算、随机数和内存操作
#include <cmath>
#include <random>
#include <cstring>
#include <cstdlib> // 用于 rand() 备用方案
#include <ctime>   // 用于 srand()

// 调试与工具（Debugging and Utilities）：调试输出和外部库支持
#include <QDebug>
#include <dr_wav.h> // 假设使用 dr_wav 库加载 WAV 文件

// 音频处理（Audio Processing）：实时音频处理和通道配置管理
#include "RtAudio.h" // 包含 RtAudio 头文件，用于实时音频处理
#include "audiochannelconfigmanager.h" // 包含 AudioChannelConfigManager 头文件，用于通道配置管理

class AudioProcessor : public QObject { // 定义 AudioProcessor 类，继承自 QObject
    Q_OBJECT // 启用 Qt 的元对象编译器，支持信号和槽

public: // 公共成员部分
    enum ChannelType { isInput, isOutput }; // 定义通道类型枚举：输入通道或输出通道
    enum SignalType { Sine, Sweep, Noise, Wav }; // 定义信号类型枚举：正弦波、扫频、噪声、WAV

    AudioChannelConfigManager configManager_; // 通道配置管理器实例

    struct ChannelInfo { // 定义 ChannelInfo 结构体，存储通道信息
        QString deviceName; // 通道名称，例如 InChannel_0 或 OutChannel_0
        QString deviceKey; // 设备键，格式为 Driver_Name_Driver_Type_DriverID
        unsigned int deviceId; // 设备 ID，从 DriverID 获取
        RtAudio::Api api; // 音频 API 类型，ASIO 或 WASAPI
        unsigned int channelIndex; // 通道索引，从 0 开始
        float gain; // 通道增益
        bool active; // 通道是否活跃（正在使用）
        unsigned int sampleRate; // 采样率，例如 44100 Hz
        unsigned int displayPoints_ = static_cast<unsigned int>(44100 * 0.05);
        QSharedPointer<moodycamel::ConcurrentQueue<float>> inputQueue; // 输入无锁队列，用于数据采集
        QSharedPointer<moodycamel::ConcurrentQueue<float>> outputQueue; // 输出无锁队列，用于数据播放
        SignalType signalType; // 信号类型（正弦波、扫频等）
        std::vector<float> sineWaveData; // 正弦波预生成数据
        unsigned int sampleOffset; // 正弦波样本偏移量，用于循环播放
        std::vector<float> tempData; // 扫频、噪声或 WAV 的预生成数据
        unsigned int tempDataIndex; // 临时数据当前索引
        unsigned int validSamples; // 有效样本数（剩余未播放样本）
        unsigned int totalSamples; // 总样本数（初始样本数）

        // 新增字段
         bool pendingRemoval = false; // 标记通道是否待移除
    };

    struct DeviceStream { // 定义 DeviceStream 结构体，存储设备流信息
        RtAudio::DeviceInfo Devi;
        RtAudio* dac; // RtAudio 实例指针，用于声卡操作
        bool isRunning; // 流是否正在运行
        unsigned int sampleRate; // 流的采样率
        unsigned int streamLatency; // 流延迟，单位为样本数
        double lastStreamTime; // 上次流时间，用于监控
        int activeChannelCount; // 活跃通道计数
        QList<QSharedPointer<ChannelInfo>> inputChannels; // 输入通道列表
        QList<QSharedPointer<ChannelInfo>> outputChannels; // 输出通道列表

        DeviceStream() : dac(nullptr), isRunning(false), sampleRate(0), // 构造函数，初始化默认值
                         streamLatency(0), lastStreamTime(-1.0), activeChannelCount(0) {} // 默认值设置
    };

    class AsioHost { // 定义 AsioHost 类，管理 ASIO 声卡
    public: // 公共成员
        AsioHost(const QString& key, AudioProcessor* parent); // 构造函数，接收设备键和 AudioProcessor 指针
        bool addChannel(const QString& channelName, ChannelType type, const AudioChannalInfoConfig& config, // 添加通道
                        unsigned int sampleRate, QString& errorMessage); // 参数：通道名、类型、配置、采样率、错误信息
        bool startStream(unsigned int sampleRate, QString& errorMessage); // 启动音频流
        void stopStream(); // 停止音频流
        bool isRunning() const { return stream.isRunning; } // 检查流是否运行

        DeviceStream& getStream() { return stream; } // 获取设备流对象
        QString getKey() const { return key; } // 获取设备键

    private: // 私有成员
        QString key; // 设备键，唯一标识声卡
        AudioProcessor* parent; // AudioProcessor 指针，用于信号传递
        QSharedPointer<RtAudio> rtAudio; // RtAudio 实例，智能指针管理
        DeviceStream stream; // 设备流对象
        void errorCallback(RtAudioErrorType type, const std::string& errorText); // 错误回调函数，处理 ASIO 错误
        static int callback(void* outputBuffer, void* inputBuffer, unsigned int nFrames, // 静态回调函数，处理音频数据
                            double streamTime, RtAudioStreamStatus status, void* userData); // 参数：输出/输入缓冲区、帧数、流时间、状态、用户数据
    };

    class WasapiHost { // 定义 WasapiHost 类，管理 WASAPI 声卡
    public: // 公共成员
        WasapiHost(const QString& key, AudioProcessor* parent); // 构造函数，接收设备键和 AudioProcessor 指针
        bool addChannel(const QString& channelName, ChannelType type, const AudioChannalInfoConfig& config, // 添加通道
                        unsigned int sampleRate, QString& errorMessage); // 参数：通道名、类型、配置、采样率、错误信息
        bool startStream(unsigned int sampleRate, QString& errorMessage); // 启动音频流
        void stopStream(); // 停止音频流
        bool isRunning() const { return stream.isRunning; } // 检查流是否运行

        DeviceStream& getStream() { return stream; } // 获取设备流对象
        QString getKey() const { return key; } // 获取设备键

    private: // 私有成员
        QString key; // 设备键，唯一标识声卡
        AudioProcessor* parent; // AudioProcessor 指针，用于信号传递
        QSharedPointer<RtAudio> rtAudio; // RtAudio 实例，智能指针管理
        DeviceStream stream; // 设备流对象
        void errorCallback(RtAudioErrorType type, const std::string& errorText); // 错误回调函数，处理 WASAPI 错误
        static int callback(void* outputBuffer, void* inputBuffer, unsigned int nFrames, // 静态回调函数，处理音频数据
                            double streamTime, RtAudioStreamStatus status, void* userData); // 参数：输出/输入缓冲区、帧数、流时间、状态、用户数据
    };

    explicit AudioProcessor(QObject* parent = nullptr); // 构造函数，接收可选父对象
    ~AudioProcessor(); // 析构函数，释放资源

    bool addChannels(const QStringList& inputChannels, const QStringList& outputChannels, // 注册输入/输出通道
                     unsigned int sampleRate, QString& errorMessage); // 参数：输入通道列表、输出通道列表、采样率、错误信息
    bool startStream(const QStringList& inputChannels, const QStringList& outputChannels, // 启动音频流
                     unsigned int sampleRate, QString& errorMessage); // 参数：输入通道列表、输出通道列表、采样率、错误信息
    void stopStream(const QStringList& inputChannels, const QStringList& outputChannels); // 停止音频流
    bool configureSignal(const QString& channelName, SignalType type, const QVariantMap& params); // 配置输出信号
    bool getDisplayData(ChannelType type, const QString& channelName,unsigned int Len, std::vector<double>& data); // 获取显示数据
    bool isDeviceRunning(const QString& deviceName) const; // 检查设备是否正在运行
    bool updateSoundCardConfig(); // 新增：搜索并更新声卡配置
    void cleanupFailedStream(const QStringList& inputChannels, const QStringList& outputChannels); // 新增：清理失败的流和通道
    void cleanupInFailedStream(const QStringList &inputChannels);
    void cleanupOutFailedStream(const QStringList &outputChannels);
   // bool configureSignalforCaculate(const QString &channelName, SignalType type, const QVariantMap &params);
    bool configureSignalforCaculate(const QString &channelName, SignalType type, const QVariantMap &params, bool EnGain);
    bool configureSignalForSweep(const QString &channelName, QVector<float> data);
   // bool getAllDisplayData(ChannelType type, const QString &channelName, std::vector<double> &data);
    bool getAllDisplayData(ChannelType type, const QString &channelName, QVector<double> &data);
    unsigned int getStreamLatencyForChannel(const QString& channelName);

signals: // 信号定义
    void errorOccurred(const QString& msg); // 错误发生信号，传递错误消息
    void dataUpdated(ChannelType type, const QString& channelName); // 数据更新信号，通知波形数据更新
    void playbackFinished(ChannelType type, const QString& channelName); // 播放完成信号，通知通道播放结束
    //void streamLatencyUpdated(const QStringList& channelNames, unsigned int latency); //发送延迟
    void streamLatencyReported(const QString& channelName, unsigned int latencySamples);
private: // 私有成员
    QMap<QString, QSharedPointer<AsioHost>> asioHosts; // ASIO 声卡主机映射，键为设备键
    QMap<QString, QSharedPointer<WasapiHost>> wasapiHosts; // WASAPI 声卡主机映射，键为设备键
    QMap<QString, QSharedPointer<ChannelInfo>> registeredChannels; // 已注册通道映射，键为通道名称
    //QMap<ChannelType, QMap<QString, std::vector<float>>> displayBuffer1_, displayBuffer2_; // 双缓冲区，用于存储显示数据
    QMap<ChannelType, QMap<QString, QSharedPointer<moodycamel::ConcurrentQueue<float>>>> displayQueues_;
    QMutex mutex_;//, displayMutex; // 互斥锁，保护主机/通道和显示缓冲区
    QTimer* displayTimer; // 定时器，定期更新显示数据
    unsigned int bufferFrames_ = 512; // 音频缓冲区帧数，默认 512
   // bool useBuffer1_ = true; // 显示缓冲区切换标志，true 表示使用 displayBuffer1_
    //unsigned int displayPoints_ = static_cast<unsigned int>(44100 * 0.05); // 显示点数，默认 0.2 秒（8820 样本 @ 44100 Hz）


    bool validateChannels(const QStringList& inputChannels, const QStringList& outputChannels, // 验证通道有效性
                          unsigned int sampleRate, QString& errorMessage); // 参数：输入/输出通道列表、采样率、错误信息
    void updateAllDisplayBuffers(); // 更新所有显示缓冲区
    static QString apiToString(RtAudio::Api api); // 将 RtAudio::Api 转换为字符串
};

#endif // AUDIOPROCESSOR_H // 结束 AUDIOPROCESSOR_H 宏定义
