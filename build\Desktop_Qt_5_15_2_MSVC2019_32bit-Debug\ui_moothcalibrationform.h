/********************************************************************************
** Form generated from reading UI file 'moothcalibrationform.ui'
**
** Created by: Qt User Interface Compiler version 5.15.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_MOOTHCALIBRATIONFORM_H
#define UI_MOOTHCALIBRATIONFORM_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QDoubleSpinBox>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QRadioButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QSpinBox>
#include <QtWidgets/QTabWidget>
#include <QtWidgets/QTableWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_MoothCalibrationForm
{
public:
    QWidget *widget_Wave;
    QWidget *widget_2;
    QVBoxLayout *verticalLayout;
    QLabel *label;
    QRadioButton *radioButton_WaveFreRaspose;
    QRadioButton *radioButton_WaveTHD;
    QRadioButton *radioButton_Signal;
    QTabWidget *tabWidget;
    QWidget *tab;
    QGridLayout *gridLayout;
    QWidget *widget_4;
    QVBoxLayout *verticalLayout_4;
    QLabel *label_2;
    QComboBox *OutChannelcomboBox;
    QWidget *widget_5;
    QVBoxLayout *verticalLayout_5;
    QLabel *label_3;
    QComboBox *InChannelcomboBox;
    QSpacerItem *horizontalSpacer;
    QWidget *widget_7;
    QVBoxLayout *verticalLayout_7;
    QLabel *label_5;
    QComboBox *MouthcomboBox;
    QWidget *widget_6;
    QVBoxLayout *verticalLayout_6;
    QLabel *label_4;
    QComboBox *MiccomboBox;
    QSpacerItem *verticalSpacer_2;
    QWidget *tab_2;
    QWidget *widget_13;
    QVBoxLayout *verticalLayout_14;
    QLabel *label_6;
    QComboBox *SignalSourcecomboBox;
    QWidget *widget_14;
    QVBoxLayout *verticalLayout_15;
    QLabel *label_7;
    QComboBox *StiListcomboBox;
    QWidget *tab_3;
    QWidget *widget_8;
    QVBoxLayout *verticalLayout_8;
    QLabel *label_8;
    QDoubleSpinBox *doubleSpinBox_TargetValuedB;
    QWidget *widget_9;
    QVBoxLayout *verticalLayout_9;
    QRadioButton *radioButton_SetValue;
    QRadioButton *radioButton_CustomList;
    QWidget *layoutWidget;
    QVBoxLayout *verticalLayout_10;
    QHBoxLayout *horizontalLayout;
    QPushButton *pushButton_6;
    QPushButton *pushButton_7;
    QSpacerItem *horizontalSpacer_2;
    QTableWidget *tableWidget;
    QWidget *tab_4;
    QWidget *widget_10;
    QVBoxLayout *verticalLayout_11;
    QLabel *label_9;
    QDoubleSpinBox *doubleSpinBox_FreResponseLimit;
    QWidget *widget_11;
    QVBoxLayout *verticalLayout_12;
    QLabel *label_10;
    QDoubleSpinBox *doubleSpinBox_THDLimit;
    QWidget *widget_12;
    QVBoxLayout *verticalLayout_13;
    QLabel *label_11;
    QSpinBox *spinBox_MaxTestNum;
    QWidget *widget_3;
    QVBoxLayout *verticalLayout_2;
    QPushButton *pushButton_AutoCalibration;
    QSpacerItem *verticalSpacer;
    QGroupBox *groupBox;
    QVBoxLayout *verticalLayout_3;
    QPushButton *pushButton_Fist;
    QPushButton *pushButton_Sco;
    QPushButton *pushButton_Save;
    QPushButton *pushButton_Exit;

    void setupUi(QWidget *MoothCalibrationForm)
    {
        if (MoothCalibrationForm->objectName().isEmpty())
            MoothCalibrationForm->setObjectName(QString::fromUtf8("MoothCalibrationForm"));
        MoothCalibrationForm->resize(1112, 699);
        widget_Wave = new QWidget(MoothCalibrationForm);
        widget_Wave->setObjectName(QString::fromUtf8("widget_Wave"));
        widget_Wave->setGeometry(QRect(80, 20, 791, 300));
        widget_Wave->setMinimumSize(QSize(680, 300));
        widget_2 = new QWidget(MoothCalibrationForm);
        widget_2->setObjectName(QString::fromUtf8("widget_2"));
        widget_2->setGeometry(QRect(870, 20, 111, 131));
        verticalLayout = new QVBoxLayout(widget_2);
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        label = new QLabel(widget_2);
        label->setObjectName(QString::fromUtf8("label"));

        verticalLayout->addWidget(label);

        radioButton_WaveFreRaspose = new QRadioButton(widget_2);
        radioButton_WaveFreRaspose->setObjectName(QString::fromUtf8("radioButton_WaveFreRaspose"));

        verticalLayout->addWidget(radioButton_WaveFreRaspose);

        radioButton_WaveTHD = new QRadioButton(widget_2);
        radioButton_WaveTHD->setObjectName(QString::fromUtf8("radioButton_WaveTHD"));

        verticalLayout->addWidget(radioButton_WaveTHD);

        radioButton_Signal = new QRadioButton(widget_2);
        radioButton_Signal->setObjectName(QString::fromUtf8("radioButton_Signal"));

        verticalLayout->addWidget(radioButton_Signal);

        tabWidget = new QTabWidget(MoothCalibrationForm);
        tabWidget->setObjectName(QString::fromUtf8("tabWidget"));
        tabWidget->setGeometry(QRect(60, 360, 731, 321));
        tab = new QWidget();
        tab->setObjectName(QString::fromUtf8("tab"));
        gridLayout = new QGridLayout(tab);
        gridLayout->setObjectName(QString::fromUtf8("gridLayout"));
        widget_4 = new QWidget(tab);
        widget_4->setObjectName(QString::fromUtf8("widget_4"));
        verticalLayout_4 = new QVBoxLayout(widget_4);
        verticalLayout_4->setObjectName(QString::fromUtf8("verticalLayout_4"));
        label_2 = new QLabel(widget_4);
        label_2->setObjectName(QString::fromUtf8("label_2"));

        verticalLayout_4->addWidget(label_2);

        OutChannelcomboBox = new QComboBox(widget_4);
        OutChannelcomboBox->setObjectName(QString::fromUtf8("OutChannelcomboBox"));

        verticalLayout_4->addWidget(OutChannelcomboBox);


        gridLayout->addWidget(widget_4, 0, 0, 1, 1);

        widget_5 = new QWidget(tab);
        widget_5->setObjectName(QString::fromUtf8("widget_5"));
        verticalLayout_5 = new QVBoxLayout(widget_5);
        verticalLayout_5->setObjectName(QString::fromUtf8("verticalLayout_5"));
        label_3 = new QLabel(widget_5);
        label_3->setObjectName(QString::fromUtf8("label_3"));

        verticalLayout_5->addWidget(label_3);

        InChannelcomboBox = new QComboBox(widget_5);
        InChannelcomboBox->setObjectName(QString::fromUtf8("InChannelcomboBox"));

        verticalLayout_5->addWidget(InChannelcomboBox);


        gridLayout->addWidget(widget_5, 1, 0, 1, 1);

        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        gridLayout->addItem(horizontalSpacer, 0, 2, 1, 1);

        widget_7 = new QWidget(tab);
        widget_7->setObjectName(QString::fromUtf8("widget_7"));
        verticalLayout_7 = new QVBoxLayout(widget_7);
        verticalLayout_7->setObjectName(QString::fromUtf8("verticalLayout_7"));
        label_5 = new QLabel(widget_7);
        label_5->setObjectName(QString::fromUtf8("label_5"));

        verticalLayout_7->addWidget(label_5);

        MouthcomboBox = new QComboBox(widget_7);
        MouthcomboBox->setObjectName(QString::fromUtf8("MouthcomboBox"));

        verticalLayout_7->addWidget(MouthcomboBox);


        gridLayout->addWidget(widget_7, 0, 1, 1, 1);

        widget_6 = new QWidget(tab);
        widget_6->setObjectName(QString::fromUtf8("widget_6"));
        verticalLayout_6 = new QVBoxLayout(widget_6);
        verticalLayout_6->setObjectName(QString::fromUtf8("verticalLayout_6"));
        label_4 = new QLabel(widget_6);
        label_4->setObjectName(QString::fromUtf8("label_4"));

        verticalLayout_6->addWidget(label_4);

        MiccomboBox = new QComboBox(widget_6);
        MiccomboBox->setObjectName(QString::fromUtf8("MiccomboBox"));

        verticalLayout_6->addWidget(MiccomboBox);


        gridLayout->addWidget(widget_6, 1, 1, 1, 1);

        verticalSpacer_2 = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        gridLayout->addItem(verticalSpacer_2, 2, 0, 1, 1);

        tabWidget->addTab(tab, QString());
        tab_2 = new QWidget();
        tab_2->setObjectName(QString::fromUtf8("tab_2"));
        widget_13 = new QWidget(tab_2);
        widget_13->setObjectName(QString::fromUtf8("widget_13"));
        widget_13->setGeometry(QRect(20, 30, 211, 65));
        verticalLayout_14 = new QVBoxLayout(widget_13);
        verticalLayout_14->setObjectName(QString::fromUtf8("verticalLayout_14"));
        label_6 = new QLabel(widget_13);
        label_6->setObjectName(QString::fromUtf8("label_6"));

        verticalLayout_14->addWidget(label_6);

        SignalSourcecomboBox = new QComboBox(widget_13);
        SignalSourcecomboBox->addItem(QString());
        SignalSourcecomboBox->setObjectName(QString::fromUtf8("SignalSourcecomboBox"));

        verticalLayout_14->addWidget(SignalSourcecomboBox);

        widget_14 = new QWidget(tab_2);
        widget_14->setObjectName(QString::fromUtf8("widget_14"));
        widget_14->setGeometry(QRect(20, 110, 301, 65));
        verticalLayout_15 = new QVBoxLayout(widget_14);
        verticalLayout_15->setObjectName(QString::fromUtf8("verticalLayout_15"));
        label_7 = new QLabel(widget_14);
        label_7->setObjectName(QString::fromUtf8("label_7"));

        verticalLayout_15->addWidget(label_7);

        StiListcomboBox = new QComboBox(widget_14);
        StiListcomboBox->setObjectName(QString::fromUtf8("StiListcomboBox"));

        verticalLayout_15->addWidget(StiListcomboBox);

        tabWidget->addTab(tab_2, QString());
        tab_3 = new QWidget();
        tab_3->setObjectName(QString::fromUtf8("tab_3"));
        widget_8 = new QWidget(tab_3);
        widget_8->setObjectName(QString::fromUtf8("widget_8"));
        widget_8->setGeometry(QRect(190, 20, 193, 65));
        verticalLayout_8 = new QVBoxLayout(widget_8);
        verticalLayout_8->setObjectName(QString::fromUtf8("verticalLayout_8"));
        label_8 = new QLabel(widget_8);
        label_8->setObjectName(QString::fromUtf8("label_8"));

        verticalLayout_8->addWidget(label_8);

        doubleSpinBox_TargetValuedB = new QDoubleSpinBox(widget_8);
        doubleSpinBox_TargetValuedB->setObjectName(QString::fromUtf8("doubleSpinBox_TargetValuedB"));

        verticalLayout_8->addWidget(doubleSpinBox_TargetValuedB);

        widget_9 = new QWidget(tab_3);
        widget_9->setObjectName(QString::fromUtf8("widget_9"));
        widget_9->setGeometry(QRect(40, 20, 124, 67));
        verticalLayout_9 = new QVBoxLayout(widget_9);
        verticalLayout_9->setObjectName(QString::fromUtf8("verticalLayout_9"));
        radioButton_SetValue = new QRadioButton(widget_9);
        radioButton_SetValue->setObjectName(QString::fromUtf8("radioButton_SetValue"));

        verticalLayout_9->addWidget(radioButton_SetValue);

        radioButton_CustomList = new QRadioButton(widget_9);
        radioButton_CustomList->setObjectName(QString::fromUtf8("radioButton_CustomList"));

        verticalLayout_9->addWidget(radioButton_CustomList);

        layoutWidget = new QWidget(tab_3);
        layoutWidget->setObjectName(QString::fromUtf8("layoutWidget"));
        layoutWidget->setGeometry(QRect(30, 100, 661, 141));
        verticalLayout_10 = new QVBoxLayout(layoutWidget);
        verticalLayout_10->setObjectName(QString::fromUtf8("verticalLayout_10"));
        verticalLayout_10->setContentsMargins(0, 0, 0, 0);
        horizontalLayout = new QHBoxLayout();
        horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));
        pushButton_6 = new QPushButton(layoutWidget);
        pushButton_6->setObjectName(QString::fromUtf8("pushButton_6"));

        horizontalLayout->addWidget(pushButton_6);

        pushButton_7 = new QPushButton(layoutWidget);
        pushButton_7->setObjectName(QString::fromUtf8("pushButton_7"));

        horizontalLayout->addWidget(pushButton_7);

        horizontalSpacer_2 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout->addItem(horizontalSpacer_2);


        verticalLayout_10->addLayout(horizontalLayout);

        tableWidget = new QTableWidget(layoutWidget);
        if (tableWidget->rowCount() < 2)
            tableWidget->setRowCount(2);
        QTableWidgetItem *__qtablewidgetitem = new QTableWidgetItem();
        tableWidget->setVerticalHeaderItem(0, __qtablewidgetitem);
        QTableWidgetItem *__qtablewidgetitem1 = new QTableWidgetItem();
        tableWidget->setVerticalHeaderItem(1, __qtablewidgetitem1);
        tableWidget->setObjectName(QString::fromUtf8("tableWidget"));

        verticalLayout_10->addWidget(tableWidget);

        tabWidget->addTab(tab_3, QString());
        tab_4 = new QWidget();
        tab_4->setObjectName(QString::fromUtf8("tab_4"));
        widget_10 = new QWidget(tab_4);
        widget_10->setObjectName(QString::fromUtf8("widget_10"));
        widget_10->setGeometry(QRect(410, 50, 193, 65));
        verticalLayout_11 = new QVBoxLayout(widget_10);
        verticalLayout_11->setObjectName(QString::fromUtf8("verticalLayout_11"));
        label_9 = new QLabel(widget_10);
        label_9->setObjectName(QString::fromUtf8("label_9"));

        verticalLayout_11->addWidget(label_9);

        doubleSpinBox_FreResponseLimit = new QDoubleSpinBox(widget_10);
        doubleSpinBox_FreResponseLimit->setObjectName(QString::fromUtf8("doubleSpinBox_FreResponseLimit"));

        verticalLayout_11->addWidget(doubleSpinBox_FreResponseLimit);

        widget_11 = new QWidget(tab_4);
        widget_11->setObjectName(QString::fromUtf8("widget_11"));
        widget_11->setGeometry(QRect(410, 110, 193, 65));
        verticalLayout_12 = new QVBoxLayout(widget_11);
        verticalLayout_12->setObjectName(QString::fromUtf8("verticalLayout_12"));
        label_10 = new QLabel(widget_11);
        label_10->setObjectName(QString::fromUtf8("label_10"));

        verticalLayout_12->addWidget(label_10);

        doubleSpinBox_THDLimit = new QDoubleSpinBox(widget_11);
        doubleSpinBox_THDLimit->setObjectName(QString::fromUtf8("doubleSpinBox_THDLimit"));

        verticalLayout_12->addWidget(doubleSpinBox_THDLimit);

        widget_12 = new QWidget(tab_4);
        widget_12->setObjectName(QString::fromUtf8("widget_12"));
        widget_12->setGeometry(QRect(420, 180, 193, 65));
        verticalLayout_13 = new QVBoxLayout(widget_12);
        verticalLayout_13->setObjectName(QString::fromUtf8("verticalLayout_13"));
        label_11 = new QLabel(widget_12);
        label_11->setObjectName(QString::fromUtf8("label_11"));

        verticalLayout_13->addWidget(label_11);

        spinBox_MaxTestNum = new QSpinBox(widget_12);
        spinBox_MaxTestNum->setObjectName(QString::fromUtf8("spinBox_MaxTestNum"));

        verticalLayout_13->addWidget(spinBox_MaxTestNum);

        tabWidget->addTab(tab_4, QString());
        widget_3 = new QWidget(MoothCalibrationForm);
        widget_3->setObjectName(QString::fromUtf8("widget_3"));
        widget_3->setGeometry(QRect(910, 390, 121, 231));
        verticalLayout_2 = new QVBoxLayout(widget_3);
        verticalLayout_2->setObjectName(QString::fromUtf8("verticalLayout_2"));
        pushButton_AutoCalibration = new QPushButton(widget_3);
        pushButton_AutoCalibration->setObjectName(QString::fromUtf8("pushButton_AutoCalibration"));

        verticalLayout_2->addWidget(pushButton_AutoCalibration);

        verticalSpacer = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout_2->addItem(verticalSpacer);

        groupBox = new QGroupBox(widget_3);
        groupBox->setObjectName(QString::fromUtf8("groupBox"));
        verticalLayout_3 = new QVBoxLayout(groupBox);
        verticalLayout_3->setObjectName(QString::fromUtf8("verticalLayout_3"));
        pushButton_Fist = new QPushButton(groupBox);
        pushButton_Fist->setObjectName(QString::fromUtf8("pushButton_Fist"));

        verticalLayout_3->addWidget(pushButton_Fist);

        pushButton_Sco = new QPushButton(groupBox);
        pushButton_Sco->setObjectName(QString::fromUtf8("pushButton_Sco"));

        verticalLayout_3->addWidget(pushButton_Sco);


        verticalLayout_2->addWidget(groupBox);

        pushButton_Save = new QPushButton(widget_3);
        pushButton_Save->setObjectName(QString::fromUtf8("pushButton_Save"));

        verticalLayout_2->addWidget(pushButton_Save);

        pushButton_Exit = new QPushButton(widget_3);
        pushButton_Exit->setObjectName(QString::fromUtf8("pushButton_Exit"));

        verticalLayout_2->addWidget(pushButton_Exit);


        retranslateUi(MoothCalibrationForm);

        tabWidget->setCurrentIndex(0);


        QMetaObject::connectSlotsByName(MoothCalibrationForm);
    } // setupUi

    void retranslateUi(QWidget *MoothCalibrationForm)
    {
        MoothCalibrationForm->setWindowTitle(QCoreApplication::translate("MoothCalibrationForm", "\346\240\241\345\207\206\344\272\272\345\267\245\345\230\264", nullptr));
        label->setText(QCoreApplication::translate("MoothCalibrationForm", "\351\200\211\346\213\251\346\263\242\345\275\242", nullptr));
        radioButton_WaveFreRaspose->setText(QCoreApplication::translate("MoothCalibrationForm", "\351\242\221\345\223\215", nullptr));
        radioButton_WaveTHD->setText(QCoreApplication::translate("MoothCalibrationForm", "\345\244\261\347\234\237", nullptr));
        radioButton_Signal->setText(QCoreApplication::translate("MoothCalibrationForm", "\344\277\241\345\217\267", nullptr));
        label_2->setText(QCoreApplication::translate("MoothCalibrationForm", "\350\276\223\345\207\272\351\200\232\351\201\223", nullptr));
        label_3->setText(QCoreApplication::translate("MoothCalibrationForm", "\350\276\223\345\205\245\351\200\232\351\201\223", nullptr));
        label_5->setText(QCoreApplication::translate("MoothCalibrationForm", "\344\272\272\345\267\245\345\230\264", nullptr));
        label_4->setText(QCoreApplication::translate("MoothCalibrationForm", "\351\272\246\345\205\213\351\243\216", nullptr));
        tabWidget->setTabText(tabWidget->indexOf(tab), QCoreApplication::translate("MoothCalibrationForm", "1.\347\241\254\344\273\266\350\256\276\347\275\256", nullptr));
        label_6->setText(QCoreApplication::translate("MoothCalibrationForm", "\344\277\241\345\217\267\346\272\220", nullptr));
        SignalSourcecomboBox->setItemText(0, QCoreApplication::translate("MoothCalibrationForm", "\344\273\216\346\277\200\345\212\261\344\277\241\345\217\267\345\210\227\350\241\250", nullptr));

        label_7->setText(QCoreApplication::translate("MoothCalibrationForm", "\351\200\211\346\213\251\346\277\200\345\212\261\344\277\241\345\217\267", nullptr));
        tabWidget->setTabText(tabWidget->indexOf(tab_2), QCoreApplication::translate("MoothCalibrationForm", "2.\344\277\241\345\217\267\346\272\220\350\256\276\347\275\256", nullptr));
        label_8->setText(QCoreApplication::translate("MoothCalibrationForm", "\347\233\256\346\240\207\345\243\260\345\216\213", nullptr));
        radioButton_SetValue->setText(QCoreApplication::translate("MoothCalibrationForm", "\350\276\223\345\205\245\345\200\274", nullptr));
        radioButton_CustomList->setText(QCoreApplication::translate("MoothCalibrationForm", "\350\207\252\345\256\232\344\271\211\345\210\227\350\241\250", nullptr));
        pushButton_6->setText(QCoreApplication::translate("MoothCalibrationForm", "\345\257\274\345\205\245", nullptr));
        pushButton_7->setText(QCoreApplication::translate("MoothCalibrationForm", "\345\257\274\345\207\272", nullptr));
        QTableWidgetItem *___qtablewidgetitem = tableWidget->verticalHeaderItem(0);
        ___qtablewidgetitem->setText(QCoreApplication::translate("MoothCalibrationForm", "\351\242\221\347\216\207", nullptr));
        QTableWidgetItem *___qtablewidgetitem1 = tableWidget->verticalHeaderItem(1);
        ___qtablewidgetitem1->setText(QCoreApplication::translate("MoothCalibrationForm", "\345\243\260\345\216\213", nullptr));
        tabWidget->setTabText(tabWidget->indexOf(tab_3), QCoreApplication::translate("MoothCalibrationForm", "3.\347\233\256\346\240\207\345\243\260\345\216\213\350\256\276\347\275\256", nullptr));
        label_9->setText(QCoreApplication::translate("MoothCalibrationForm", "\351\242\221\345\223\215\351\231\220\345\200\274+/- dB", nullptr));
        label_10->setText(QCoreApplication::translate("MoothCalibrationForm", "THD\351\231\220\345\200\274+/- %", nullptr));
        label_11->setText(QCoreApplication::translate("MoothCalibrationForm", "\346\234\200\345\244\247\346\254\241\346\225\260", nullptr));
        tabWidget->setTabText(tabWidget->indexOf(tab_4), QCoreApplication::translate("MoothCalibrationForm", "4.\351\253\230\347\272\247\350\256\276\347\275\256", nullptr));
        pushButton_AutoCalibration->setText(QCoreApplication::translate("MoothCalibrationForm", "\345\205\250\350\207\252\345\212\250\346\211\247\350\241\214", nullptr));
        groupBox->setTitle(QCoreApplication::translate("MoothCalibrationForm", "\346\211\213\345\212\250\345\210\206\346\255\245\346\211\247\350\241\214", nullptr));
        pushButton_Fist->setText(QCoreApplication::translate("MoothCalibrationForm", "\347\254\254\344\270\200\346\254\241", nullptr));
        pushButton_Sco->setText(QCoreApplication::translate("MoothCalibrationForm", "\347\254\254\344\272\214\346\254\241", nullptr));
        pushButton_Save->setText(QCoreApplication::translate("MoothCalibrationForm", "\344\277\235\345\255\230", nullptr));
        pushButton_Exit->setText(QCoreApplication::translate("MoothCalibrationForm", "\351\200\200\345\207\272", nullptr));
    } // retranslateUi

};

namespace Ui {
    class MoothCalibrationForm: public Ui_MoothCalibrationForm {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_MOOTHCALIBRATIONFORM_H
