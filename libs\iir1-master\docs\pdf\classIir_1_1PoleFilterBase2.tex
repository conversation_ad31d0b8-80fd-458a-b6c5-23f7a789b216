\hypertarget{classIir_1_1PoleFilterBase2}{}\doxysubsection{Iir\+::Pole\+Filter\+Base2 Class Reference}
\label{classIir_1_1PoleFilterBase2}\index{Iir::PoleFilterBase2@{Iir::PoleFilterBase2}}


{\ttfamily \#include $<$Pole\+Filter.\+h$>$}

Inheritance diagram for Iir\+::Pole\+Filter\+Base2\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=8.860760cm]{classIir_1_1PoleFilterBase2}
\end{center}
\end{figure}
\doxysubsubsection*{Additional Inherited Members}


\doxysubsubsection{Detailed Description}
Factored implementations to reduce template instantiations 

The documentation for this class was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
iir/Pole\+Filter.\+h\end{DoxyCompactItemize}
