<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>iir1: iir/Types.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">iir1
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_5231e4e0b49b19c482755f817f3ac47e.html">iir</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">Types.h</div>  </div>
</div><!--header-->
<div class="contents">
<div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160; </div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;<span class="preprocessor">#ifndef IIR1_TYPES_H</span></div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;<span class="preprocessor">#define IIR1_TYPES_H</span></div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160; </div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;<span class="preprocessor">#include &quot;Common.h&quot;</span></div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;<span class="preprocessor">#include &quot;MathSupplement.h&quot;</span></div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160; </div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespaceIir.html">Iir</a> {</div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160; </div>
<div class="line"><a name="l00047"></a><span class="lineno"><a class="line" href="structIir_1_1ComplexPair.html">   47</a></span>&#160;        <span class="keyword">struct </span>IIR_EXPORT <a class="code" href="structIir_1_1ComplexPair.html">ComplexPair</a> : complex_pair_t</div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;        {</div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;                <a class="code" href="structIir_1_1ComplexPair.html">ComplexPair</a>() = <span class="keywordflow">default</span>;</div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160; </div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;                <span class="keyword">explicit</span> <a class="code" href="structIir_1_1ComplexPair.html">ComplexPair</a> (<span class="keyword">const</span> complex_t&amp; c1)</div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;                        : complex_pair_t (c1, 0.)</div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;                {</div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;                        <span class="keywordflow">if</span> (!isReal()) throw_invalid_argument(<span class="stringliteral">&quot;A single complex number needs to be real.&quot;</span>);</div>
<div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;                }</div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160; </div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;                <a class="code" href="structIir_1_1ComplexPair.html">ComplexPair</a> (<span class="keyword">const</span> complex_t&amp; c1,</div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;                             <span class="keyword">const</span> complex_t&amp; c2)</div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;                        : complex_pair_t (c1, c2)</div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;                {</div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;                }</div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160; </div>
<div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;                <span class="keywordtype">bool</span> isReal ()<span class="keyword"> const</span></div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;<span class="keyword">                </span>{</div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;                        <span class="keywordflow">return</span> first.imag() == 0 &amp;&amp; second.imag() == 0;</div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;                }</div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160; </div>
<div class="line"><a name="l00072"></a><span class="lineno"><a class="line" href="structIir_1_1ComplexPair.html#a79d121320c8b042faebcc0364398b071">   72</a></span>&#160;                <span class="keywordtype">bool</span> <a class="code" href="structIir_1_1ComplexPair.html#a79d121320c8b042faebcc0364398b071">isMatchedPair</a> ()<span class="keyword"> const</span></div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;<span class="keyword">                </span>{</div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;                        <span class="keywordflow">if</span> (first.imag() != 0)</div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;                                <span class="keywordflow">return</span> second == std::conj (first);</div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;                        <span class="keywordflow">else</span></div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;                                <span class="keywordflow">return</span> second.imag () == 0 &amp;&amp;</div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;                                        second.real () != 0 &amp;&amp;</div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;                                        first.real () != 0;</div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;                }</div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160; </div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;                <span class="keywordtype">bool</span> is_nan ()<span class="keyword"> const</span></div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;<span class="keyword">                </span>{</div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;                        <span class="keywordflow">return</span> Iir::is_nan (first) || Iir::is_nan (second);</div>
<div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;                }</div>
<div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;        };</div>
<div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160; </div>
<div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160; </div>
<div class="line"><a name="l00092"></a><span class="lineno"><a class="line" href="structIir_1_1PoleZeroPair.html">   92</a></span>&#160;        <span class="keyword">struct </span>IIR_EXPORT <a class="code" href="structIir_1_1PoleZeroPair.html">PoleZeroPair</a></div>
<div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;        {</div>
<div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;                <a class="code" href="structIir_1_1ComplexPair.html">ComplexPair</a> poles = <a class="code" href="structIir_1_1ComplexPair.html">ComplexPair</a>();</div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;                <a class="code" href="structIir_1_1ComplexPair.html">ComplexPair</a> zeros = <a class="code" href="structIir_1_1ComplexPair.html">ComplexPair</a>();</div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160; </div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;                <a class="code" href="structIir_1_1PoleZeroPair.html">PoleZeroPair</a> () = <span class="keywordflow">default</span>;</div>
<div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160; </div>
<div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;                <span class="comment">// single pole/zero</span></div>
<div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;                <a class="code" href="structIir_1_1PoleZeroPair.html">PoleZeroPair</a> (<span class="keyword">const</span> complex_t&amp; p, <span class="keyword">const</span> complex_t&amp; z)</div>
<div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;                        : poles (p), zeros (z)</div>
<div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;                {</div>
<div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;                }</div>
<div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160; </div>
<div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;                <span class="comment">// pole/zero pair</span></div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;                <a class="code" href="structIir_1_1PoleZeroPair.html">PoleZeroPair</a> (<span class="keyword">const</span> complex_t&amp; p1, <span class="keyword">const</span> complex_t&amp; z1,</div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;                              <span class="keyword">const</span> complex_t&amp; p2, <span class="keyword">const</span> complex_t&amp; z2)</div>
<div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;                        : poles (p1, p2)</div>
<div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;                        , zeros (z1, z2)</div>
<div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;                {</div>
<div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;                }</div>
<div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160; </div>
<div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;                <span class="keywordtype">bool</span> isSinglePole ()<span class="keyword"> const</span></div>
<div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;<span class="keyword">                </span>{</div>
<div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;                        <span class="keywordflow">return</span> poles.second == 0. &amp;&amp; zeros.second == 0.;</div>
<div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;                }</div>
<div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160; </div>
<div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;                <span class="keywordtype">bool</span> is_nan ()<span class="keyword"> const</span></div>
<div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;<span class="keyword">                </span>{</div>
<div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;                        <span class="keywordflow">return</span> poles.is_nan() || zeros.is_nan();</div>
<div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;                }</div>
<div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;        };</div>
<div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160; </div>
<div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160; </div>
<div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;}</div>
<div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160; </div>
<div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="ttc" id="anamespaceIir_html"><div class="ttname"><a href="namespaceIir.html">Iir</a></div><div class="ttdef"><b>Definition:</b> Biquad.cpp:40</div></div>
<div class="ttc" id="astructIir_1_1ComplexPair_html"><div class="ttname"><a href="structIir_1_1ComplexPair.html">Iir::ComplexPair</a></div><div class="ttdef"><b>Definition:</b> Types.h:48</div></div>
<div class="ttc" id="astructIir_1_1ComplexPair_html_a79d121320c8b042faebcc0364398b071"><div class="ttname"><a href="structIir_1_1ComplexPair.html#a79d121320c8b042faebcc0364398b071">Iir::ComplexPair::isMatchedPair</a></div><div class="ttdeci">bool isMatchedPair() const</div><div class="ttdef"><b>Definition:</b> Types.h:72</div></div>
<div class="ttc" id="astructIir_1_1PoleZeroPair_html"><div class="ttname"><a href="structIir_1_1PoleZeroPair.html">Iir::PoleZeroPair</a></div><div class="ttdef"><b>Definition:</b> Types.h:93</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Fri Jan 6 2023 23:53:32 for iir1 by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
