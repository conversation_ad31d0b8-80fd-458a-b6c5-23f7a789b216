/****************************************************************************
** Meta object code from reading C++ file 'seqeditbtnfun.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.15.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../SeqEditALL/LSSTreeBtnUserTree/seqeditbtnfun.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'seqeditbtnfun.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.15.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_SeqEditBtnFun_t {
    QByteArrayData data[10];
    char stringdata0[152];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_SeqEditBtnFun_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_SeqEditBtnFun_t qt_meta_stringdata_SeqEditBtnFun = {
    {
QT_MOC_LITERAL(0, 0, 13), // "SeqEditBtnFun"
QT_MOC_LITERAL(1, 14, 15), // "SendTreeCharged"
QT_MOC_LITERAL(2, 30, 0), // ""
QT_MOC_LITERAL(3, 31, 13), // "clearTreeItem"
QT_MOC_LITERAL(4, 45, 16), // "QTreeWidgetItem*"
QT_MOC_LITERAL(5, 62, 4), // "item"
QT_MOC_LITERAL(6, 67, 18), // "onMoveInBtnClicked"
QT_MOC_LITERAL(7, 86, 19), // "onMoveOutBtnClicked"
QT_MOC_LITERAL(8, 106, 21), // "onSeqEditUpBtnClicked"
QT_MOC_LITERAL(9, 128, 23) // "onSeqEditDownBtnClicked"

    },
    "SeqEditBtnFun\0SendTreeCharged\0\0"
    "clearTreeItem\0QTreeWidgetItem*\0item\0"
    "onMoveInBtnClicked\0onMoveOutBtnClicked\0"
    "onSeqEditUpBtnClicked\0onSeqEditDownBtnClicked"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_SeqEditBtnFun[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       6,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       2,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    0,   44,    2, 0x06 /* Public */,
       3,    1,   45,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       6,    0,   48,    2, 0x08 /* Private */,
       7,    0,   49,    2, 0x08 /* Private */,
       8,    0,   50,    2, 0x08 /* Private */,
       9,    0,   51,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 4,    5,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void SeqEditBtnFun::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<SeqEditBtnFun *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->SendTreeCharged(); break;
        case 1: _t->clearTreeItem((*reinterpret_cast< QTreeWidgetItem*(*)>(_a[1]))); break;
        case 2: _t->onMoveInBtnClicked(); break;
        case 3: _t->onMoveOutBtnClicked(); break;
        case 4: _t->onSeqEditUpBtnClicked(); break;
        case 5: _t->onSeqEditDownBtnClicked(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (SeqEditBtnFun::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SeqEditBtnFun::SendTreeCharged)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (SeqEditBtnFun::*)(QTreeWidgetItem * );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SeqEditBtnFun::clearTreeItem)) {
                *result = 1;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject SeqEditBtnFun::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_SeqEditBtnFun.data,
    qt_meta_data_SeqEditBtnFun,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *SeqEditBtnFun::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *SeqEditBtnFun::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_SeqEditBtnFun.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int SeqEditBtnFun::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 6)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 6;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 6)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 6;
    }
    return _id;
}

// SIGNAL 0
void SeqEditBtnFun::SendTreeCharged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void SeqEditBtnFun::clearTreeItem(QTreeWidgetItem * _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
