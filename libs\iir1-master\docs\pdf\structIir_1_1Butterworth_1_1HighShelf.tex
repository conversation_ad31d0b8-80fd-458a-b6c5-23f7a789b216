\hypertarget{structIir_1_1Butterworth_1_1HighShelf}{}\doxysubsection{Iir\+::Butterworth\+::High\+Shelf$<$ Filter\+Order, State\+Type $>$ Struct Template Reference}
\label{structIir_1_1Butterworth_1_1HighShelf}\index{Iir::Butterworth::HighShelf$<$ FilterOrder, StateType $>$@{Iir::Butterworth::HighShelf$<$ FilterOrder, StateType $>$}}


{\ttfamily \#include $<$Butterworth.\+h$>$}

Inheritance diagram for Iir\+::Butterworth\+::High\+Shelf$<$ Filter\+Order, State\+Type $>$\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=2.400000cm]{structIir_1_1Butterworth_1_1HighShelf}
\end{center}
\end{figure}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
void \mbox{\hyperlink{structIir_1_1Butterworth_1_1HighShelf_a19a78cb96c9b2454ccb48745ed50500d}{setup}} (double sample\+Rate, double cutoff\+Frequency, double gain\+Db)
\item 
void \mbox{\hyperlink{structIir_1_1Butterworth_1_1HighShelf_aa7fecdca7646acf8962c5d0106fb8b3c}{setup}} (int req\+Order, double sample\+Rate, double cutoff\+Frequency, double gain\+Db)
\item 
void \mbox{\hyperlink{structIir_1_1Butterworth_1_1HighShelf_af63e4f43001ae2d9faf44ba6496db562}{setupN}} (double cutoff\+Frequency, double gain\+Db)
\item 
void \mbox{\hyperlink{structIir_1_1Butterworth_1_1HighShelf_a7df553be87ce22541c6ffa8af7508898}{setupN}} (int req\+Order, double cutoff\+Frequency, double gain\+Db)
\end{DoxyCompactItemize}


\doxysubsubsection{Detailed Description}
\subsubsection*{template$<$int Filter\+Order = 4, class State\+Type = Direct\+Form\+II$>$\newline
struct Iir\+::\+Butterworth\+::\+High\+Shelf$<$ Filter\+Order, State\+Type $>$}

\mbox{\hyperlink{namespaceIir_1_1Butterworth}{Butterworth}} high shelf filter. Above the cutoff the filter has a specified gain and below it has 0 dB. 
\begin{DoxyParams}{Parameters}
{\em Filter\+Order} & Reserves memory for a filter of the order Filter\+Order \\
\hline
{\em State\+Type} & The filter topology\+: \mbox{\hyperlink{classIir_1_1DirectFormI}{Direct\+FormI}}, \mbox{\hyperlink{classIir_1_1DirectFormII}{Direct\+Form\+II}}, ... \\
\hline
\end{DoxyParams}


\doxysubsubsection{Member Function Documentation}
\mbox{\Hypertarget{structIir_1_1Butterworth_1_1HighShelf_a19a78cb96c9b2454ccb48745ed50500d}\label{structIir_1_1Butterworth_1_1HighShelf_a19a78cb96c9b2454ccb48745ed50500d}} 
\index{Iir::Butterworth::HighShelf$<$ FilterOrder, StateType $>$@{Iir::Butterworth::HighShelf$<$ FilterOrder, StateType $>$}!setup@{setup}}
\index{setup@{setup}!Iir::Butterworth::HighShelf$<$ FilterOrder, StateType $>$@{Iir::Butterworth::HighShelf$<$ FilterOrder, StateType $>$}}
\doxyparagraph{\texorpdfstring{setup()}{setup()}\hspace{0.1cm}{\footnotesize\ttfamily [1/2]}}
{\footnotesize\ttfamily template$<$int Filter\+Order = 4, class State\+Type  = Direct\+Form\+II$>$ \\
void \mbox{\hyperlink{structIir_1_1Butterworth_1_1HighShelf}{Iir\+::\+Butterworth\+::\+High\+Shelf}}$<$ Filter\+Order, State\+Type $>$\+::setup (\begin{DoxyParamCaption}\item[{double}]{sample\+Rate,  }\item[{double}]{cutoff\+Frequency,  }\item[{double}]{gain\+Db }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients with the filter order provided by the instantiation 
\begin{DoxyParams}{Parameters}
{\em sample\+Rate} & Sampling rate \\
\hline
{\em cutoff\+Frequency} & Cutoff \\
\hline
{\em gain\+Db} & Gain in dB of the filter in the passband \\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{structIir_1_1Butterworth_1_1HighShelf_aa7fecdca7646acf8962c5d0106fb8b3c}\label{structIir_1_1Butterworth_1_1HighShelf_aa7fecdca7646acf8962c5d0106fb8b3c}} 
\index{Iir::Butterworth::HighShelf$<$ FilterOrder, StateType $>$@{Iir::Butterworth::HighShelf$<$ FilterOrder, StateType $>$}!setup@{setup}}
\index{setup@{setup}!Iir::Butterworth::HighShelf$<$ FilterOrder, StateType $>$@{Iir::Butterworth::HighShelf$<$ FilterOrder, StateType $>$}}
\doxyparagraph{\texorpdfstring{setup()}{setup()}\hspace{0.1cm}{\footnotesize\ttfamily [2/2]}}
{\footnotesize\ttfamily template$<$int Filter\+Order = 4, class State\+Type  = Direct\+Form\+II$>$ \\
void \mbox{\hyperlink{structIir_1_1Butterworth_1_1HighShelf}{Iir\+::\+Butterworth\+::\+High\+Shelf}}$<$ Filter\+Order, State\+Type $>$\+::setup (\begin{DoxyParamCaption}\item[{int}]{req\+Order,  }\item[{double}]{sample\+Rate,  }\item[{double}]{cutoff\+Frequency,  }\item[{double}]{gain\+Db }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients 
\begin{DoxyParams}{Parameters}
{\em req\+Order} & The actual order which can be less than the instantiated one \\
\hline
{\em sample\+Rate} & Sampling rate \\
\hline
{\em cutoff\+Frequency} & Cutoff \\
\hline
{\em gain\+Db} & Gain in dB of the filter in the passband \\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{structIir_1_1Butterworth_1_1HighShelf_af63e4f43001ae2d9faf44ba6496db562}\label{structIir_1_1Butterworth_1_1HighShelf_af63e4f43001ae2d9faf44ba6496db562}} 
\index{Iir::Butterworth::HighShelf$<$ FilterOrder, StateType $>$@{Iir::Butterworth::HighShelf$<$ FilterOrder, StateType $>$}!setupN@{setupN}}
\index{setupN@{setupN}!Iir::Butterworth::HighShelf$<$ FilterOrder, StateType $>$@{Iir::Butterworth::HighShelf$<$ FilterOrder, StateType $>$}}
\doxyparagraph{\texorpdfstring{setupN()}{setupN()}\hspace{0.1cm}{\footnotesize\ttfamily [1/2]}}
{\footnotesize\ttfamily template$<$int Filter\+Order = 4, class State\+Type  = Direct\+Form\+II$>$ \\
void \mbox{\hyperlink{structIir_1_1Butterworth_1_1HighShelf}{Iir\+::\+Butterworth\+::\+High\+Shelf}}$<$ Filter\+Order, State\+Type $>$\+::setupN (\begin{DoxyParamCaption}\item[{double}]{cutoff\+Frequency,  }\item[{double}]{gain\+Db }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients with the filter order provided by the instantiation 
\begin{DoxyParams}{Parameters}
{\em cutoff\+Frequency} & Normalised cutoff frequency (0..1/2) \\
\hline
{\em gain\+Db} & Gain in dB of the filter in the passband \\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{structIir_1_1Butterworth_1_1HighShelf_a7df553be87ce22541c6ffa8af7508898}\label{structIir_1_1Butterworth_1_1HighShelf_a7df553be87ce22541c6ffa8af7508898}} 
\index{Iir::Butterworth::HighShelf$<$ FilterOrder, StateType $>$@{Iir::Butterworth::HighShelf$<$ FilterOrder, StateType $>$}!setupN@{setupN}}
\index{setupN@{setupN}!Iir::Butterworth::HighShelf$<$ FilterOrder, StateType $>$@{Iir::Butterworth::HighShelf$<$ FilterOrder, StateType $>$}}
\doxyparagraph{\texorpdfstring{setupN()}{setupN()}\hspace{0.1cm}{\footnotesize\ttfamily [2/2]}}
{\footnotesize\ttfamily template$<$int Filter\+Order = 4, class State\+Type  = Direct\+Form\+II$>$ \\
void \mbox{\hyperlink{structIir_1_1Butterworth_1_1HighShelf}{Iir\+::\+Butterworth\+::\+High\+Shelf}}$<$ Filter\+Order, State\+Type $>$\+::setupN (\begin{DoxyParamCaption}\item[{int}]{req\+Order,  }\item[{double}]{cutoff\+Frequency,  }\item[{double}]{gain\+Db }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients 
\begin{DoxyParams}{Parameters}
{\em req\+Order} & The actual order which can be less than the instantiated one \\
\hline
{\em cutoff\+Frequency} & Normalised cutoff frequency (0..1/2) \\
\hline
{\em gain\+Db} & Gain in dB of the filter in the passband \\
\hline
\end{DoxyParams}


The documentation for this struct was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
iir/Butterworth.\+h\end{DoxyCompactItemize}
