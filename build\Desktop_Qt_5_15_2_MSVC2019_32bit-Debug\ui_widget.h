/********************************************************************************
** Form generated from reading UI file 'widget.ui'
**
** Created by: Qt User Interface Compiler version 5.15.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_WIDGET_H
#define UI_WIDGET_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QCheckBox>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QFormLayout>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QSplitter>
#include <QtWidgets/QTableWidget>
#include <QtWidgets/QTextBrowser>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_Widget
{
public:
    QVBoxLayout *verticalLayout_3;
    QWidget *widget;
    QFormLayout *formLayout_2;
    QLabel *UserLabel;
    QWidget *widget_3;
    QHBoxLayout *horizontalLayout;
    QSpacerItem *horizontalSpacer;
    QPushButton *ManualTestBtn;
    QPushButton *AutoTestBtn;
    QPushButton *PauseTestBtn;
    QPushButton *StopTestBtn;
    QPushButton *LoginBtn;
    QPushButton *UserMessgeBtn;
    QPushButton *SeqEditBtn;
    QPushButton *SetBtn;
    QPushButton *QuickToolBtn;
    QSpacerItem *horizontalSpacer_2;
    QSplitter *splitter;
    QWidget *widget_2;
    QHBoxLayout *horizontalLayout_2;
    QVBoxLayout *verticalLayout;
    QLabel *label_2;
    QComboBox *TestStacomboBox;
    QLabel *TestStaLabel;
    QWidget *widget_6;
    QVBoxLayout *verticalLayout_4;
    QWidget *widget_7;
    QVBoxLayout *verticalLayout_6;
    QHBoxLayout *horizontalLayout_4;
    QLabel *YieldLabel;
    QVBoxLayout *verticalLayout_5;
    QPushButton *pushButton_3;
    QPushButton *pushButton;
    QPushButton *pushButton_2;
    QTableWidget *TestResulttableWidget;
    QTableWidget *YieldtableWidget;
    QTableWidget *RuntableWidget;
    QTextBrowser *textBrowser;
    QWidget *layoutWidget;
    QVBoxLayout *verticalLayout_2;
    QWidget *widget_4;
    QFormLayout *formLayout;
    QHBoxLayout *horizontalLayout_3;
    QPushButton *pushButton_10;
    QPushButton *eliminateData;
    QCheckBox *lockWindows;
    QPushButton *hideWindows;
    QPushButton *showWindows;
    QWidget *mdiArea;

    void setupUi(QWidget *Widget)
    {
        if (Widget->objectName().isEmpty())
            Widget->setObjectName(QString::fromUtf8("Widget"));
        Widget->resize(1254, 1020);
        verticalLayout_3 = new QVBoxLayout(Widget);
        verticalLayout_3->setObjectName(QString::fromUtf8("verticalLayout_3"));
        widget = new QWidget(Widget);
        widget->setObjectName(QString::fromUtf8("widget"));
        widget->setMaximumSize(QSize(16777215, 50));
        widget->setStyleSheet(QString::fromUtf8("background-color: rgb(213, 213, 213);"));
        formLayout_2 = new QFormLayout(widget);
        formLayout_2->setObjectName(QString::fromUtf8("formLayout_2"));
        UserLabel = new QLabel(widget);
        UserLabel->setObjectName(QString::fromUtf8("UserLabel"));
        UserLabel->setStyleSheet(QString::fromUtf8("font: 45 20pt \"\347\255\211\347\272\277\";\n"
"color: rgb(255, 170, 0);"));

        formLayout_2->setWidget(0, QFormLayout::LabelRole, UserLabel);


        verticalLayout_3->addWidget(widget);

        widget_3 = new QWidget(Widget);
        widget_3->setObjectName(QString::fromUtf8("widget_3"));
        widget_3->setMinimumSize(QSize(0, 80));
        widget_3->setMaximumSize(QSize(16777215, 80));
        widget_3->setStyleSheet(QString::fromUtf8("\n"
"#widget_3 { border: 2px solid  rgb(208, 208, 208); }"));
        horizontalLayout = new QHBoxLayout(widget_3);
        horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));
        horizontalSpacer = new QSpacerItem(20, 10, QSizePolicy::Minimum, QSizePolicy::Minimum);

        horizontalLayout->addItem(horizontalSpacer);

        ManualTestBtn = new QPushButton(widget_3);
        ManualTestBtn->setObjectName(QString::fromUtf8("ManualTestBtn"));
        ManualTestBtn->setStyleSheet(QString::fromUtf8("QPushButton {\n"
"    border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 15px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"    background-color: white;  /* \351\273\230\350\256\244\350\203\214\346\231\257\350\211\262 */	\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"}\n"
"QPushButton:hover {\n"
"     border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 5px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"    background-color: lightblue; /* \346\202\254\346\265\256\346\227\266\350\203\214\346\231\257\345\217\230\350\211\262 */\n"
"}"));

        horizontalLayout->addWidget(ManualTestBtn);

        AutoTestBtn = new QPushButton(widget_3);
        AutoTestBtn->setObjectName(QString::fromUtf8("AutoTestBtn"));
        AutoTestBtn->setStyleSheet(QString::fromUtf8("QPushButton {\n"
"    border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 15px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"    background-color: white;  /* \351\273\230\350\256\244\350\203\214\346\231\257\350\211\262 */	\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"}\n"
"QPushButton:hover {\n"
"     border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 5px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"    background-color: lightblue; /* \346\202\254\346\265\256\346\227\266\350\203\214\346\231\257\345\217\230\350\211\262 */\n"
"}"));

        horizontalLayout->addWidget(AutoTestBtn);

        PauseTestBtn = new QPushButton(widget_3);
        PauseTestBtn->setObjectName(QString::fromUtf8("PauseTestBtn"));
        PauseTestBtn->setStyleSheet(QString::fromUtf8("QPushButton {\n"
"    border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"    padding: 15px; /* \347\273\237\344\270\200\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\217\230\345\214\226 */\n"
"    background-color: white;  /* \351\273\230\350\256\244\350\203\214\346\231\257\350\211\262 */	\n"
"    border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    border: 1px solid lightblack; /* \344\277\235\346\214\201\344\270\200\350\207\264 */\n"
"    padding: 15px; /* \347\273\237\344\270\200\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\244\247\345\260\217\345\217\230\345\214\226 */\n"
"    border-radius: 10px; /* \344\277\235\346\214\201\344\270\200\350\207\264 */\n"
"    background-color: white; /* \347\247\273\351\231\244\350\203\214\346\231\257\345\217\230\345\214\226\357\274\214\344\277\235\346\214\201\344\270\215\345\217\230 */\n"
"}\n"
"\n"
"QPushButt"
                        "on:pressed {\n"
"    border: 1px solid lightblack; /* \347\202\271\345\207\273\346\227\266\344\277\235\346\214\201\344\270\200\350\207\264\357\274\214\351\201\277\345\205\215\344\270\213\346\262\211\346\225\210\346\236\234 */\n"
"    padding: 15px; /* \347\273\237\344\270\200\345\206\205\350\276\271\350\267\235 */\n"
"    background-color: white; /* \344\277\235\346\214\201\344\270\215\345\217\230 */\n"
"    border-radius: 10px;\n"
"}"));

        horizontalLayout->addWidget(PauseTestBtn);

        StopTestBtn = new QPushButton(widget_3);
        StopTestBtn->setObjectName(QString::fromUtf8("StopTestBtn"));
        StopTestBtn->setStyleSheet(QString::fromUtf8("QPushButton {\n"
"    border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"    padding: 15px; /* \347\273\237\344\270\200\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\217\230\345\214\226 */\n"
"    background-color: white;  /* \351\273\230\350\256\244\350\203\214\346\231\257\350\211\262 */	\n"
"    border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    border: 1px solid lightblack; /* \344\277\235\346\214\201\344\270\200\350\207\264 */\n"
"    padding: 15px; /* \347\273\237\344\270\200\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\244\247\345\260\217\345\217\230\345\214\226 */\n"
"    border-radius: 10px; /* \344\277\235\346\214\201\344\270\200\350\207\264 */\n"
"    background-color: white; /* \347\247\273\351\231\244\350\203\214\346\231\257\345\217\230\345\214\226\357\274\214\344\277\235\346\214\201\344\270\215\345\217\230 */\n"
"}\n"
"\n"
"QPushButt"
                        "on:pressed {\n"
"    border: 1px solid lightblack; /* \347\202\271\345\207\273\346\227\266\344\277\235\346\214\201\344\270\200\350\207\264\357\274\214\351\201\277\345\205\215\344\270\213\346\262\211\346\225\210\346\236\234 */\n"
"    padding: 15px; /* \347\273\237\344\270\200\345\206\205\350\276\271\350\267\235 */\n"
"    background-color: white; /* \344\277\235\346\214\201\344\270\215\345\217\230 */\n"
"    border-radius: 10px;\n"
"}"));

        horizontalLayout->addWidget(StopTestBtn);

        LoginBtn = new QPushButton(widget_3);
        LoginBtn->setObjectName(QString::fromUtf8("LoginBtn"));
        LoginBtn->setStyleSheet(QString::fromUtf8("QPushButton {\n"
"    border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 15px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"    background-color: white;  /* \351\273\230\350\256\244\350\203\214\346\231\257\350\211\262 */	\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"}\n"
"QPushButton:hover {\n"
"     border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 5px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"    background-color: lightblue; /* \346\202\254\346\265\256\346\227\266\350\203\214\346\231\257\345\217\230\350\211\262 */\n"
"}"));

        horizontalLayout->addWidget(LoginBtn);

        UserMessgeBtn = new QPushButton(widget_3);
        UserMessgeBtn->setObjectName(QString::fromUtf8("UserMessgeBtn"));
        UserMessgeBtn->setStyleSheet(QString::fromUtf8("QPushButton {\n"
"    border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 15px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"    background-color: white;  /* \351\273\230\350\256\244\350\203\214\346\231\257\350\211\262 */	\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"}\n"
"QPushButton:hover {\n"
"     border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 5px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"    background-color: lightblue; /* \346\202\254\346\265\256\346\227\266\350\203\214\346\231\257\345\217\230\350\211\262 */\n"
"}"));

        horizontalLayout->addWidget(UserMessgeBtn);

        SeqEditBtn = new QPushButton(widget_3);
        SeqEditBtn->setObjectName(QString::fromUtf8("SeqEditBtn"));
        SeqEditBtn->setMinimumSize(QSize(0, 40));
        QFont font;
        font.setFamily(QString::fromUtf8("3ds"));
        SeqEditBtn->setFont(font);
        SeqEditBtn->setCursor(QCursor(Qt::ArrowCursor));
        SeqEditBtn->setStyleSheet(QString::fromUtf8("QPushButton:enabled {\n"
"    border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 15px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"    background-color: white;  /* \351\273\230\350\256\244\350\203\214\346\231\257\350\211\262 */	\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"}\n"
"QPushButton:enabled:hover {\n"
"     border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 5px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"    background-color: lightblue; /* \346\202\254\346\265\256\346\227\266\350\203\214\346\231\257\345\217\230\350\211\262 "
                        "*/\n"
"}\n"
"/* \347\246\201\347\224\250\347\212\266\346\200\201\344\270\213\347\232\204\346\240\267\345\274\217 */\n"
"    QPushButton:disabled {\n"
"        border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"        padding: 15px; /* \344\270\216\345\220\257\347\224\250\347\212\266\346\200\201\344\270\200\350\207\264 */\n"
"        background-color: rgb(229, 229, 229); /* \347\201\260\350\211\262\350\203\214\346\231\257 */\n"
"        border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"    }"));

        horizontalLayout->addWidget(SeqEditBtn);

        SetBtn = new QPushButton(widget_3);
        SetBtn->setObjectName(QString::fromUtf8("SetBtn"));
        SetBtn->setStyleSheet(QString::fromUtf8("QPushButton:enabled {\n"
"    border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 15px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"    background-color: white;  /* \351\273\230\350\256\244\350\203\214\346\231\257\350\211\262 */	\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"}\n"
"QPushButton:enabled:hover {\n"
"     border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 5px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"    background-color: lightblue; /* \346\202\254\346\265\256\346\227\266\350\203\214\346\231\257\345\217\230\350\211\262 "
                        "*/\n"
"}\n"
"/* \347\246\201\347\224\250\347\212\266\346\200\201\344\270\213\347\232\204\346\240\267\345\274\217 */\n"
"    QPushButton:disabled {\n"
"        border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"        padding: 15px; /* \344\270\216\345\220\257\347\224\250\347\212\266\346\200\201\344\270\200\350\207\264 */\n"
"        background-color: rgb(229, 229, 229); /* \347\201\260\350\211\262\350\203\214\346\231\257 */\n"
"        border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"    }"));

        horizontalLayout->addWidget(SetBtn);

        QuickToolBtn = new QPushButton(widget_3);
        QuickToolBtn->setObjectName(QString::fromUtf8("QuickToolBtn"));
        QuickToolBtn->setStyleSheet(QString::fromUtf8("QPushButton:enabled {\n"
"    border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 15px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"    background-color: white;  /* \351\273\230\350\256\244\350\203\214\346\231\257\350\211\262 */	\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"}\n"
"QPushButton:enabled:hover {\n"
"     border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 5px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"    background-color: lightblue; /* \346\202\254\346\265\256\346\227\266\350\203\214\346\231\257\345\217\230\350\211\262 "
                        "*/\n"
"}\n"
"/* \347\246\201\347\224\250\347\212\266\346\200\201\344\270\213\347\232\204\346\240\267\345\274\217 */\n"
"    QPushButton:disabled {\n"
"        border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"        padding: 15px; /* \344\270\216\345\220\257\347\224\250\347\212\266\346\200\201\344\270\200\350\207\264 */\n"
"        background-color: rgb(229, 229, 229); /* \347\201\260\350\211\262\350\203\214\346\231\257 */\n"
"        border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"    }"));

        horizontalLayout->addWidget(QuickToolBtn);

        horizontalSpacer_2 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout->addItem(horizontalSpacer_2);


        verticalLayout_3->addWidget(widget_3);

        splitter = new QSplitter(Widget);
        splitter->setObjectName(QString::fromUtf8("splitter"));
        splitter->setOrientation(Qt::Horizontal);
        widget_2 = new QWidget(splitter);
        widget_2->setObjectName(QString::fromUtf8("widget_2"));
        widget_2->setMinimumSize(QSize(300, 0));
        widget_2->setMaximumSize(QSize(300, 16777215));
        widget_2->setStyleSheet(QString::fromUtf8("#widget_2 { border: 2px solid  rgb(208, 208, 208); }"));
        horizontalLayout_2 = new QHBoxLayout(widget_2);
        horizontalLayout_2->setObjectName(QString::fromUtf8("horizontalLayout_2"));
        verticalLayout = new QVBoxLayout();
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        label_2 = new QLabel(widget_2);
        label_2->setObjectName(QString::fromUtf8("label_2"));
        label_2->setMaximumSize(QSize(16777215, 40));
        label_2->setStyleSheet(QString::fromUtf8("background-color: rgb(255, 170, 255);"));

        verticalLayout->addWidget(label_2);

        TestStacomboBox = new QComboBox(widget_2);
        TestStacomboBox->addItem(QString());
        TestStacomboBox->addItem(QString());
        TestStacomboBox->addItem(QString());
        TestStacomboBox->setObjectName(QString::fromUtf8("TestStacomboBox"));

        verticalLayout->addWidget(TestStacomboBox);

        TestStaLabel = new QLabel(widget_2);
        TestStaLabel->setObjectName(QString::fromUtf8("TestStaLabel"));
        TestStaLabel->setMinimumSize(QSize(0, 60));
        TestStaLabel->setMaximumSize(QSize(16777215, 60));
        TestStaLabel->setStyleSheet(QString::fromUtf8("background-color: rgb(0, 255, 127);\n"
"font: 24pt \"3ds\";"));
        TestStaLabel->setAlignment(Qt::AlignCenter);

        verticalLayout->addWidget(TestStaLabel);

        widget_6 = new QWidget(widget_2);
        widget_6->setObjectName(QString::fromUtf8("widget_6"));
        verticalLayout_4 = new QVBoxLayout(widget_6);
        verticalLayout_4->setObjectName(QString::fromUtf8("verticalLayout_4"));
        verticalLayout_4->setContentsMargins(0, 0, 0, 0);
        widget_7 = new QWidget(widget_6);
        widget_7->setObjectName(QString::fromUtf8("widget_7"));
        verticalLayout_6 = new QVBoxLayout(widget_7);
        verticalLayout_6->setObjectName(QString::fromUtf8("verticalLayout_6"));
        verticalLayout_6->setContentsMargins(0, 0, 0, 0);
        horizontalLayout_4 = new QHBoxLayout();
        horizontalLayout_4->setObjectName(QString::fromUtf8("horizontalLayout_4"));
        YieldLabel = new QLabel(widget_7);
        YieldLabel->setObjectName(QString::fromUtf8("YieldLabel"));

        horizontalLayout_4->addWidget(YieldLabel);

        verticalLayout_5 = new QVBoxLayout();
        verticalLayout_5->setObjectName(QString::fromUtf8("verticalLayout_5"));
        pushButton_3 = new QPushButton(widget_7);
        pushButton_3->setObjectName(QString::fromUtf8("pushButton_3"));

        verticalLayout_5->addWidget(pushButton_3);

        pushButton = new QPushButton(widget_7);
        pushButton->setObjectName(QString::fromUtf8("pushButton"));

        verticalLayout_5->addWidget(pushButton);

        pushButton_2 = new QPushButton(widget_7);
        pushButton_2->setObjectName(QString::fromUtf8("pushButton_2"));

        verticalLayout_5->addWidget(pushButton_2);


        horizontalLayout_4->addLayout(verticalLayout_5);


        verticalLayout_6->addLayout(horizontalLayout_4);


        verticalLayout_4->addWidget(widget_7);

        TestResulttableWidget = new QTableWidget(widget_6);
        if (TestResulttableWidget->columnCount() < 1)
            TestResulttableWidget->setColumnCount(1);
        QTableWidgetItem *__qtablewidgetitem = new QTableWidgetItem();
        TestResulttableWidget->setHorizontalHeaderItem(0, __qtablewidgetitem);
        TestResulttableWidget->setObjectName(QString::fromUtf8("TestResulttableWidget"));

        verticalLayout_4->addWidget(TestResulttableWidget);

        YieldtableWidget = new QTableWidget(widget_6);
        if (YieldtableWidget->columnCount() < 2)
            YieldtableWidget->setColumnCount(2);
        QTableWidgetItem *__qtablewidgetitem1 = new QTableWidgetItem();
        YieldtableWidget->setHorizontalHeaderItem(0, __qtablewidgetitem1);
        QTableWidgetItem *__qtablewidgetitem2 = new QTableWidgetItem();
        YieldtableWidget->setHorizontalHeaderItem(1, __qtablewidgetitem2);
        YieldtableWidget->setObjectName(QString::fromUtf8("YieldtableWidget"));

        verticalLayout_4->addWidget(YieldtableWidget);

        RuntableWidget = new QTableWidget(widget_6);
        if (RuntableWidget->columnCount() < 2)
            RuntableWidget->setColumnCount(2);
        QTableWidgetItem *__qtablewidgetitem3 = new QTableWidgetItem();
        RuntableWidget->setHorizontalHeaderItem(0, __qtablewidgetitem3);
        QTableWidgetItem *__qtablewidgetitem4 = new QTableWidgetItem();
        RuntableWidget->setHorizontalHeaderItem(1, __qtablewidgetitem4);
        RuntableWidget->setObjectName(QString::fromUtf8("RuntableWidget"));
        RuntableWidget->viewport()->setProperty("cursor", QVariant(QCursor(Qt::ArrowCursor)));

        verticalLayout_4->addWidget(RuntableWidget);


        verticalLayout->addWidget(widget_6);

        textBrowser = new QTextBrowser(widget_2);
        textBrowser->setObjectName(QString::fromUtf8("textBrowser"));
        textBrowser->setMaximumSize(QSize(16777215, 200));

        verticalLayout->addWidget(textBrowser);


        horizontalLayout_2->addLayout(verticalLayout);

        splitter->addWidget(widget_2);
        layoutWidget = new QWidget(splitter);
        layoutWidget->setObjectName(QString::fromUtf8("layoutWidget"));
        verticalLayout_2 = new QVBoxLayout(layoutWidget);
        verticalLayout_2->setSpacing(4);
        verticalLayout_2->setObjectName(QString::fromUtf8("verticalLayout_2"));
        verticalLayout_2->setContentsMargins(0, 0, 0, 0);
        widget_4 = new QWidget(layoutWidget);
        widget_4->setObjectName(QString::fromUtf8("widget_4"));
        widget_4->setMaximumSize(QSize(16777215, 50));
        formLayout = new QFormLayout(widget_4);
        formLayout->setObjectName(QString::fromUtf8("formLayout"));
        horizontalLayout_3 = new QHBoxLayout();
        horizontalLayout_3->setObjectName(QString::fromUtf8("horizontalLayout_3"));
        pushButton_10 = new QPushButton(widget_4);
        pushButton_10->setObjectName(QString::fromUtf8("pushButton_10"));

        horizontalLayout_3->addWidget(pushButton_10);

        eliminateData = new QPushButton(widget_4);
        eliminateData->setObjectName(QString::fromUtf8("eliminateData"));

        horizontalLayout_3->addWidget(eliminateData);

        lockWindows = new QCheckBox(widget_4);
        lockWindows->setObjectName(QString::fromUtf8("lockWindows"));

        horizontalLayout_3->addWidget(lockWindows);

        hideWindows = new QPushButton(widget_4);
        hideWindows->setObjectName(QString::fromUtf8("hideWindows"));

        horizontalLayout_3->addWidget(hideWindows);

        showWindows = new QPushButton(widget_4);
        showWindows->setObjectName(QString::fromUtf8("showWindows"));

        horizontalLayout_3->addWidget(showWindows);


        formLayout->setLayout(0, QFormLayout::LabelRole, horizontalLayout_3);


        verticalLayout_2->addWidget(widget_4);

        mdiArea = new QWidget(layoutWidget);
        mdiArea->setObjectName(QString::fromUtf8("mdiArea"));
        QSizePolicy sizePolicy(QSizePolicy::Preferred, QSizePolicy::Preferred);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(mdiArea->sizePolicy().hasHeightForWidth());
        mdiArea->setSizePolicy(sizePolicy);

        verticalLayout_2->addWidget(mdiArea);

        splitter->addWidget(layoutWidget);

        verticalLayout_3->addWidget(splitter);


        retranslateUi(Widget);

        QMetaObject::connectSlotsByName(Widget);
    } // setupUi

    void retranslateUi(QWidget *Widget)
    {
        Widget->setWindowTitle(QCoreApplication::translate("Widget", "Widget", nullptr));
        UserLabel->setText(QCoreApplication::translate("Widget", "\346\234\252\347\231\273\345\275\225", nullptr));
        ManualTestBtn->setText(QCoreApplication::translate("Widget", "\346\211\213\345\212\250\346\265\213\350\257\225", nullptr));
        AutoTestBtn->setText(QCoreApplication::translate("Widget", "\350\207\252\345\212\250\346\265\213\350\257\225", nullptr));
        PauseTestBtn->setText(QCoreApplication::translate("Widget", "\346\232\202\345\201\234", nullptr));
        StopTestBtn->setText(QCoreApplication::translate("Widget", "\345\201\234\346\255\242", nullptr));
        LoginBtn->setText(QCoreApplication::translate("Widget", "\347\231\273\345\275\225", nullptr));
        UserMessgeBtn->setText(QCoreApplication::translate("Widget", "\347\224\250\346\210\267\347\256\241\347\220\206", nullptr));
        SeqEditBtn->setText(QCoreApplication::translate("Widget", "\345\272\217\345\210\227\347\274\226\350\276\221", nullptr));
        SetBtn->setText(QCoreApplication::translate("Widget", "\350\256\276\347\275\256", nullptr));
        QuickToolBtn->setText(QCoreApplication::translate("Widget", "\345\277\253\351\200\237\345\267\245\345\205\267", nullptr));
        label_2->setText(QCoreApplication::translate("Widget", "<html><head/><body><p align=\"center\"><span style=\" font-size:24pt;\">01:02:03</span></p></body></html>", nullptr));
        TestStacomboBox->setItemText(0, QCoreApplication::translate("Widget", "\346\265\213\350\257\225\347\212\266\346\200\201", nullptr));
        TestStacomboBox->setItemText(1, QCoreApplication::translate("Widget", "\346\265\213\350\257\225\347\273\223\346\236\234", nullptr));
        TestStacomboBox->setItemText(2, QCoreApplication::translate("Widget", "\350\211\257\347\216\207\347\273\237\350\256\241", nullptr));

        TestStaLabel->setText(QCoreApplication::translate("Widget", "\347\251\272\351\227\262", nullptr));
        YieldLabel->setText(QCoreApplication::translate("Widget", "<html><head/><body><p>Total:0</p><p>Pass:0</p><p>Fail:0</p><p>Yield:0%</p></body></html>", nullptr));
        pushButton_3->setText(QCoreApplication::translate("Widget", "\347\273\237\350\256\241\351\235\242\346\235\277", nullptr));
        pushButton->setText(QCoreApplication::translate("Widget", "\345\257\274\345\207\272", nullptr));
        pushButton_2->setText(QCoreApplication::translate("Widget", "\351\207\215\347\275\256", nullptr));
        QTableWidgetItem *___qtablewidgetitem = TestResulttableWidget->horizontalHeaderItem(0);
        ___qtablewidgetitem->setText(QCoreApplication::translate("Widget", "\347\273\223\346\236\234", nullptr));
        QTableWidgetItem *___qtablewidgetitem1 = YieldtableWidget->horizontalHeaderItem(0);
        ___qtablewidgetitem1->setText(QCoreApplication::translate("Widget", "\346\265\213\350\257\225\345\206\205\345\256\271", nullptr));
        QTableWidgetItem *___qtablewidgetitem2 = YieldtableWidget->horizontalHeaderItem(1);
        ___qtablewidgetitem2->setText(QCoreApplication::translate("Widget", "\350\211\257\347\216\207", nullptr));
        QTableWidgetItem *___qtablewidgetitem3 = RuntableWidget->horizontalHeaderItem(0);
        ___qtablewidgetitem3->setText(QCoreApplication::translate("Widget", "\347\212\266\346\200\201", nullptr));
        QTableWidgetItem *___qtablewidgetitem4 = RuntableWidget->horizontalHeaderItem(1);
        ___qtablewidgetitem4->setText(QCoreApplication::translate("Widget", "\346\227\266\351\227\264", nullptr));
        textBrowser->setHtml(QCoreApplication::translate("Widget", "<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.0//EN\" \"http://www.w3.org/TR/REC-html40/strict.dtd\">\n"
"<html><head><meta name=\"qrichtext\" content=\"1\" /><style type=\"text/css\">\n"
"p, li { white-space: pre-wrap; }\n"
"</style></head><body style=\" font-family:'SimSun'; font-size:9pt; font-weight:400; font-style:normal;\">\n"
"<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\">\346\230\276\347\244\272\350\277\220\350\241\214\351\224\231\350\257\257\344\277\241\346\201\257</p></body></html>", nullptr));
        pushButton_10->setText(QCoreApplication::translate("Widget", "\347\225\214\351\235\242\346\216\247\345\210\266", nullptr));
        eliminateData->setText(QCoreApplication::translate("Widget", "\346\270\205\351\231\244\347\225\214\351\235\242\346\225\260\346\215\256", nullptr));
        lockWindows->setText(QCoreApplication::translate("Widget", "\351\224\201\345\256\232\347\225\214\351\235\242", nullptr));
        hideWindows->setText(QCoreApplication::translate("Widget", "\351\232\220\350\227\217\347\225\214\351\235\242", nullptr));
        showWindows->setText(QCoreApplication::translate("Widget", "\346\230\276\347\244\272\347\225\214\351\235\242", nullptr));
    } // retranslateUi

};

namespace Ui {
    class Widget: public Ui_Widget {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_WIDGET_H
