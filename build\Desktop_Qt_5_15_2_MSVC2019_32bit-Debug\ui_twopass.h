/********************************************************************************
** Form generated from reading UI file 'twopass.ui'
**
** Created by: Qt User Interface Compiler version 5.15.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_TWOPASS_H
#define UI_TWOPASS_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QDoubleSpinBox>
#include <QtWidgets/QFrame>
#include <QtWidgets/QLabel>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QRadioButton>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_twoPass
{
public:
    QLabel *label_6;
    QPushButton *pB_start;
    QFrame *frame;
    QRadioButton *rB_end;
    QComboBox *cB_out1;
    QLabel *label_3;
    QComboBox *cB_signal;
    QLabel *label;
    QLabel *label_2;
    QComboBox *cB_out2;
    QFrame *frame_2;
    QRadioButton *rB_noise;
    QLabel *label_4;
    QDoubleSpinBox *dSB_value;
    QLabel *label_5;
    QDoubleSpinBox *dSB_time;
    QComboBox *cB_trigger;

    void setupUi(QWidget *twoPass)
    {
        if (twoPass->objectName().isEmpty())
            twoPass->setObjectName(QString::fromUtf8("twoPass"));
        twoPass->resize(639, 323);
        label_6 = new QLabel(twoPass);
        label_6->setObjectName(QString::fromUtf8("label_6"));
        label_6->setGeometry(QRect(530, 30, 54, 12));
        pB_start = new QPushButton(twoPass);
        pB_start->setObjectName(QString::fromUtf8("pB_start"));
        pB_start->setGeometry(QRect(50, 240, 121, 41));
        frame = new QFrame(twoPass);
        frame->setObjectName(QString::fromUtf8("frame"));
        frame->setGeometry(QRect(10, 10, 261, 171));
        frame->setFrameShape(QFrame::StyledPanel);
        frame->setFrameShadow(QFrame::Raised);
        rB_end = new QRadioButton(frame);
        rB_end->setObjectName(QString::fromUtf8("rB_end"));
        rB_end->setGeometry(QRect(140, 130, 89, 16));
        cB_out1 = new QComboBox(frame);
        cB_out1->addItem(QString());
        cB_out1->addItem(QString());
        cB_out1->addItem(QString());
        cB_out1->addItem(QString());
        cB_out1->addItem(QString());
        cB_out1->addItem(QString());
        cB_out1->addItem(QString());
        cB_out1->setObjectName(QString::fromUtf8("cB_out1"));
        cB_out1->setGeometry(QRect(8, 50, 101, 22));
        label_3 = new QLabel(frame);
        label_3->setObjectName(QString::fromUtf8("label_3"));
        label_3->setGeometry(QRect(20, 90, 71, 31));
        cB_signal = new QComboBox(frame);
        cB_signal->addItem(QString());
        cB_signal->addItem(QString());
        cB_signal->addItem(QString());
        cB_signal->addItem(QString());
        cB_signal->addItem(QString());
        cB_signal->addItem(QString());
        cB_signal->addItem(QString());
        cB_signal->addItem(QString());
        cB_signal->addItem(QString());
        cB_signal->setObjectName(QString::fromUtf8("cB_signal"));
        cB_signal->setGeometry(QRect(20, 130, 69, 22));
        label = new QLabel(frame);
        label->setObjectName(QString::fromUtf8("label"));
        label->setGeometry(QRect(20, 10, 71, 31));
        label_2 = new QLabel(frame);
        label_2->setObjectName(QString::fromUtf8("label_2"));
        label_2->setGeometry(QRect(160, 10, 71, 31));
        cB_out2 = new QComboBox(frame);
        cB_out2->addItem(QString());
        cB_out2->addItem(QString());
        cB_out2->addItem(QString());
        cB_out2->addItem(QString());
        cB_out2->addItem(QString());
        cB_out2->addItem(QString());
        cB_out2->addItem(QString());
        cB_out2->setObjectName(QString::fromUtf8("cB_out2"));
        cB_out2->setGeometry(QRect(138, 50, 101, 22));
        frame_2 = new QFrame(twoPass);
        frame_2->setObjectName(QString::fromUtf8("frame_2"));
        frame_2->setGeometry(QRect(280, 0, 221, 181));
        frame_2->setFrameShape(QFrame::StyledPanel);
        frame_2->setFrameShadow(QFrame::Raised);
        rB_noise = new QRadioButton(frame_2);
        rB_noise->setObjectName(QString::fromUtf8("rB_noise"));
        rB_noise->setGeometry(QRect(30, 30, 101, 21));
        label_4 = new QLabel(frame_2);
        label_4->setObjectName(QString::fromUtf8("label_4"));
        label_4->setGeometry(QRect(30, 110, 91, 16));
        dSB_value = new QDoubleSpinBox(frame_2);
        dSB_value->setObjectName(QString::fromUtf8("dSB_value"));
        dSB_value->setGeometry(QRect(40, 140, 62, 22));
        label_5 = new QLabel(frame_2);
        label_5->setObjectName(QString::fromUtf8("label_5"));
        label_5->setGeometry(QRect(130, 110, 91, 16));
        dSB_time = new QDoubleSpinBox(frame_2);
        dSB_time->setObjectName(QString::fromUtf8("dSB_time"));
        dSB_time->setGeometry(QRect(140, 140, 62, 22));
        cB_trigger = new QComboBox(twoPass);
        cB_trigger->addItem(QString());
        cB_trigger->addItem(QString());
        cB_trigger->addItem(QString());
        cB_trigger->setObjectName(QString::fromUtf8("cB_trigger"));
        cB_trigger->setGeometry(QRect(530, 60, 69, 22));

        retranslateUi(twoPass);

        QMetaObject::connectSlotsByName(twoPass);
    } // setupUi

    void retranslateUi(QWidget *twoPass)
    {
        twoPass->setWindowTitle(QCoreApplication::translate("twoPass", "Form", nullptr));
        label_6->setText(QCoreApplication::translate("twoPass", "\350\247\246\345\217\221\347\261\273\345\236\213", nullptr));
        pB_start->setText(QCoreApplication::translate("twoPass", "\345\274\200\345\247\213", nullptr));
        rB_end->setText(QCoreApplication::translate("twoPass", "\347\255\211\345\276\205\350\277\220\350\241\214\347\273\223\346\235\237", nullptr));
        cB_out1->setItemText(0, QCoreApplication::translate("twoPass", "<65535>", nullptr));
        cB_out1->setItemText(1, QCoreApplication::translate("twoPass", "PM 6143-AO1", nullptr));
        cB_out1->setItemText(2, QCoreApplication::translate("twoPass", "PM 6143-AO2", nullptr));
        cB_out1->setItemText(3, QCoreApplication::translate("twoPass", "PM 6143-AO3", nullptr));
        cB_out1->setItemText(4, QCoreApplication::translate("twoPass", "PM 6143-AO4", nullptr));
        cB_out1->setItemText(5, QCoreApplication::translate("twoPass", "U 982-AO1", nullptr));
        cB_out1->setItemText(6, QCoreApplication::translate("twoPass", "U 982-AO2", nullptr));

        label_3->setText(QCoreApplication::translate("twoPass", "\346\277\200\345\212\261\344\277\241\345\217\267", nullptr));
        cB_signal->setItemText(0, QCoreApplication::translate("twoPass", "<65535>", nullptr));
        cB_signal->setItemText(1, QCoreApplication::translate("twoPass", "1K", nullptr));
        cB_signal->setItemText(2, QCoreApplication::translate("twoPass", "48K", nullptr));
        cB_signal->setItemText(3, QCoreApplication::translate("twoPass", "ANC Tool sig", nullptr));
        cB_signal->setItemText(4, QCoreApplication::translate("twoPass", "BNC Sig", nullptr));
        cB_signal->setItemText(5, QCoreApplication::translate("twoPass", "MIC", nullptr));
        cB_signal->setItemText(6, QCoreApplication::translate("twoPass", "SPK", nullptr));
        cB_signal->setItemText(7, QCoreApplication::translate("twoPass", "\345\226\207\345\217\255", nullptr));
        cB_signal->setItemText(8, QCoreApplication::translate("twoPass", "\351\272\246\345\205\213\351\243\216", nullptr));

        label->setText(QCoreApplication::translate("twoPass", "\350\276\223\345\207\272\351\200\232\351\201\2231\357\274\232", nullptr));
        label_2->setText(QCoreApplication::translate("twoPass", "\350\276\223\345\207\272\351\200\232\351\201\2232\357\274\232", nullptr));
        cB_out2->setItemText(0, QCoreApplication::translate("twoPass", "<65535>", nullptr));
        cB_out2->setItemText(1, QCoreApplication::translate("twoPass", "PM 6143-AO1", nullptr));
        cB_out2->setItemText(2, QCoreApplication::translate("twoPass", "PM 6143-AO2", nullptr));
        cB_out2->setItemText(3, QCoreApplication::translate("twoPass", "PM 6143-AO3", nullptr));
        cB_out2->setItemText(4, QCoreApplication::translate("twoPass", "PM 6143-AO4", nullptr));
        cB_out2->setItemText(5, QCoreApplication::translate("twoPass", "U 982-AO1", nullptr));
        cB_out2->setItemText(6, QCoreApplication::translate("twoPass", "U 982-AO2", nullptr));

        rB_noise->setText(QCoreApplication::translate("twoPass", "\351\242\204\346\222\255\345\231\252\345\243\260\357\274\237", nullptr));
        label_4->setText(QCoreApplication::translate("twoPass", "\345\231\252\345\243\260\345\271\205\345\200\274\357\274\210V\357\274\211", nullptr));
        label_5->setText(QCoreApplication::translate("twoPass", "\345\231\252\345\243\260\346\227\266\351\225\277\357\274\210S\357\274\211", nullptr));
        cB_trigger->setItemText(0, QCoreApplication::translate("twoPass", "\344\270\215\350\247\246\345\217\221", nullptr));
        cB_trigger->setItemText(1, QCoreApplication::translate("twoPass", "\345\206\205\351\203\250\350\247\246\345\217\221", nullptr));
        cB_trigger->setItemText(2, QCoreApplication::translate("twoPass", "\345\244\226\351\203\250\350\247\246\345\217\221", nullptr));

    } // retranslateUi

};

namespace Ui {
    class twoPass: public Ui_twoPass {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_TWOPASS_H
