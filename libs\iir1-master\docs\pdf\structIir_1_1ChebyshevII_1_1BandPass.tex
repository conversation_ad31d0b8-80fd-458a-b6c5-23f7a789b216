\hypertarget{structIir_1_1ChebyshevII_1_1BandPass}{}\doxysubsection{Iir\+::Chebyshev\+II\+::Band\+Pass$<$ Filter\+Order, State\+Type $>$ Struct Template Reference}
\label{structIir_1_1ChebyshevII_1_1BandPass}\index{Iir::ChebyshevII::BandPass$<$ FilterOrder, StateType $>$@{Iir::ChebyshevII::BandPass$<$ FilterOrder, StateType $>$}}


{\ttfamily \#include $<$Chebyshev\+II.\+h$>$}

Inheritance diagram for Iir\+::Chebyshev\+II\+::Band\+Pass$<$ Filter\+Order, State\+Type $>$\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=2.434783cm]{structIir_1_1ChebyshevII_1_1BandPass}
\end{center}
\end{figure}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
void \mbox{\hyperlink{structIir_1_1ChebyshevII_1_1BandPass_a7fcada7f9376bd6cf0d99a18aa62ee32}{setup}} (double sample\+Rate, double center\+Frequency, double width\+Frequency, double stop\+Band\+Db)
\item 
void \mbox{\hyperlink{structIir_1_1ChebyshevII_1_1BandPass_a1157ea7f70718df1e2983fb0f576931f}{setup}} (int req\+Order, double sample\+Rate, double center\+Frequency, double width\+Frequency, double stop\+Band\+Db)
\item 
void \mbox{\hyperlink{structIir_1_1ChebyshevII_1_1BandPass_a6044cfbd6da7f2f6f78e9a2d969038d3}{setupN}} (double center\+Frequency, double width\+Frequency, double stop\+Band\+Db)
\item 
void \mbox{\hyperlink{structIir_1_1ChebyshevII_1_1BandPass_ad17b6f448cb0395d0a4fe4843ac0c44b}{setupN}} (int req\+Order, double center\+Frequency, double width\+Frequency, double stop\+Band\+Db)
\end{DoxyCompactItemize}


\doxysubsubsection{Detailed Description}
\subsubsection*{template$<$int Filter\+Order = 4, class State\+Type = Direct\+Form\+II$>$\newline
struct Iir\+::\+Chebyshev\+II\+::\+Band\+Pass$<$ Filter\+Order, State\+Type $>$}

\mbox{\hyperlink{namespaceIir_1_1ChebyshevII}{Chebyshev\+II}} bandpass filter 
\begin{DoxyParams}{Parameters}
{\em Filter\+Order} & Reserves memory for a filter of the order Filter\+Order \\
\hline
{\em State\+Type} & The filter topology\+: \mbox{\hyperlink{classIir_1_1DirectFormI}{Direct\+FormI}}, \mbox{\hyperlink{classIir_1_1DirectFormII}{Direct\+Form\+II}}, ... \\
\hline
\end{DoxyParams}


\doxysubsubsection{Member Function Documentation}
\mbox{\Hypertarget{structIir_1_1ChebyshevII_1_1BandPass_a7fcada7f9376bd6cf0d99a18aa62ee32}\label{structIir_1_1ChebyshevII_1_1BandPass_a7fcada7f9376bd6cf0d99a18aa62ee32}} 
\index{Iir::ChebyshevII::BandPass$<$ FilterOrder, StateType $>$@{Iir::ChebyshevII::BandPass$<$ FilterOrder, StateType $>$}!setup@{setup}}
\index{setup@{setup}!Iir::ChebyshevII::BandPass$<$ FilterOrder, StateType $>$@{Iir::ChebyshevII::BandPass$<$ FilterOrder, StateType $>$}}
\doxyparagraph{\texorpdfstring{setup()}{setup()}\hspace{0.1cm}{\footnotesize\ttfamily [1/2]}}
{\footnotesize\ttfamily template$<$int Filter\+Order = 4, class State\+Type  = Direct\+Form\+II$>$ \\
void \mbox{\hyperlink{structIir_1_1ChebyshevII_1_1BandPass}{Iir\+::\+Chebyshev\+II\+::\+Band\+Pass}}$<$ Filter\+Order, State\+Type $>$\+::setup (\begin{DoxyParamCaption}\item[{double}]{sample\+Rate,  }\item[{double}]{center\+Frequency,  }\item[{double}]{width\+Frequency,  }\item[{double}]{stop\+Band\+Db }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients of the filter 
\begin{DoxyParams}{Parameters}
{\em sample\+Rate} & Sampling rate \\
\hline
{\em center\+Frequency} & Center frequency of the bandpass \\
\hline
{\em width\+Frequency} & Width of the bandpass \\
\hline
{\em stop\+Band\+Db} & Permitted ripples in dB in the stopband \\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{structIir_1_1ChebyshevII_1_1BandPass_a1157ea7f70718df1e2983fb0f576931f}\label{structIir_1_1ChebyshevII_1_1BandPass_a1157ea7f70718df1e2983fb0f576931f}} 
\index{Iir::ChebyshevII::BandPass$<$ FilterOrder, StateType $>$@{Iir::ChebyshevII::BandPass$<$ FilterOrder, StateType $>$}!setup@{setup}}
\index{setup@{setup}!Iir::ChebyshevII::BandPass$<$ FilterOrder, StateType $>$@{Iir::ChebyshevII::BandPass$<$ FilterOrder, StateType $>$}}
\doxyparagraph{\texorpdfstring{setup()}{setup()}\hspace{0.1cm}{\footnotesize\ttfamily [2/2]}}
{\footnotesize\ttfamily template$<$int Filter\+Order = 4, class State\+Type  = Direct\+Form\+II$>$ \\
void \mbox{\hyperlink{structIir_1_1ChebyshevII_1_1BandPass}{Iir\+::\+Chebyshev\+II\+::\+Band\+Pass}}$<$ Filter\+Order, State\+Type $>$\+::setup (\begin{DoxyParamCaption}\item[{int}]{req\+Order,  }\item[{double}]{sample\+Rate,  }\item[{double}]{center\+Frequency,  }\item[{double}]{width\+Frequency,  }\item[{double}]{stop\+Band\+Db }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients of the filter 
\begin{DoxyParams}{Parameters}
{\em req\+Order} & Requested order which can be less than the instantiated one \\
\hline
{\em sample\+Rate} & Sampling rate \\
\hline
{\em center\+Frequency} & Center frequency of the bandpass \\
\hline
{\em width\+Frequency} & Width of the bandpass \\
\hline
{\em stop\+Band\+Db} & Permitted ripples in dB in the stopband \\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{structIir_1_1ChebyshevII_1_1BandPass_a6044cfbd6da7f2f6f78e9a2d969038d3}\label{structIir_1_1ChebyshevII_1_1BandPass_a6044cfbd6da7f2f6f78e9a2d969038d3}} 
\index{Iir::ChebyshevII::BandPass$<$ FilterOrder, StateType $>$@{Iir::ChebyshevII::BandPass$<$ FilterOrder, StateType $>$}!setupN@{setupN}}
\index{setupN@{setupN}!Iir::ChebyshevII::BandPass$<$ FilterOrder, StateType $>$@{Iir::ChebyshevII::BandPass$<$ FilterOrder, StateType $>$}}
\doxyparagraph{\texorpdfstring{setupN()}{setupN()}\hspace{0.1cm}{\footnotesize\ttfamily [1/2]}}
{\footnotesize\ttfamily template$<$int Filter\+Order = 4, class State\+Type  = Direct\+Form\+II$>$ \\
void \mbox{\hyperlink{structIir_1_1ChebyshevII_1_1BandPass}{Iir\+::\+Chebyshev\+II\+::\+Band\+Pass}}$<$ Filter\+Order, State\+Type $>$\+::setupN (\begin{DoxyParamCaption}\item[{double}]{center\+Frequency,  }\item[{double}]{width\+Frequency,  }\item[{double}]{stop\+Band\+Db }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients of the filter 
\begin{DoxyParams}{Parameters}
{\em center\+Frequency} & Normalised centre frequency (0..1/2) of the bandpass \\
\hline
{\em width\+Frequency} & Width of the bandpass \\
\hline
{\em stop\+Band\+Db} & Permitted ripples in dB in the stopband \\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{structIir_1_1ChebyshevII_1_1BandPass_ad17b6f448cb0395d0a4fe4843ac0c44b}\label{structIir_1_1ChebyshevII_1_1BandPass_ad17b6f448cb0395d0a4fe4843ac0c44b}} 
\index{Iir::ChebyshevII::BandPass$<$ FilterOrder, StateType $>$@{Iir::ChebyshevII::BandPass$<$ FilterOrder, StateType $>$}!setupN@{setupN}}
\index{setupN@{setupN}!Iir::ChebyshevII::BandPass$<$ FilterOrder, StateType $>$@{Iir::ChebyshevII::BandPass$<$ FilterOrder, StateType $>$}}
\doxyparagraph{\texorpdfstring{setupN()}{setupN()}\hspace{0.1cm}{\footnotesize\ttfamily [2/2]}}
{\footnotesize\ttfamily template$<$int Filter\+Order = 4, class State\+Type  = Direct\+Form\+II$>$ \\
void \mbox{\hyperlink{structIir_1_1ChebyshevII_1_1BandPass}{Iir\+::\+Chebyshev\+II\+::\+Band\+Pass}}$<$ Filter\+Order, State\+Type $>$\+::setupN (\begin{DoxyParamCaption}\item[{int}]{req\+Order,  }\item[{double}]{center\+Frequency,  }\item[{double}]{width\+Frequency,  }\item[{double}]{stop\+Band\+Db }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients of the filter 
\begin{DoxyParams}{Parameters}
{\em req\+Order} & Requested order which can be less than the instantiated one \\
\hline
{\em center\+Frequency} & Normalised centre frequency (0..1/2) of the bandpass \\
\hline
{\em width\+Frequency} & Width of the bandpass \\
\hline
{\em stop\+Band\+Db} & Permitted ripples in dB in the stopband \\
\hline
\end{DoxyParams}


The documentation for this struct was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
iir/Chebyshev\+II.\+h\end{DoxyCompactItemize}
