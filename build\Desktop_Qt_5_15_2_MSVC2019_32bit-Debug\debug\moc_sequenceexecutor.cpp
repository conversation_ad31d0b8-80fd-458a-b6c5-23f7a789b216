/****************************************************************************
** Meta object code from reading C++ file 'sequenceexecutor.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.15.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../sequenceexecutor.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'sequenceexecutor.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.15.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_SequenceExecutor_t {
    QByteArrayData data[18];
    char stringdata0[208];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_SequenceExecutor_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_SequenceExecutor_t qt_meta_stringdata_SequenceExecutor = {
    {
QT_MOC_LITERAL(0, 0, 16), // "SequenceExecutor"
QT_MOC_LITERAL(1, 17, 15), // "sequenceStarted"
QT_MOC_LITERAL(2, 33, 0), // ""
QT_MOC_LITERAL(3, 34, 16), // "sequenceFinished"
QT_MOC_LITERAL(4, 51, 11), // "stepStarted"
QT_MOC_LITERAL(5, 63, 5), // "index"
QT_MOC_LITERAL(6, 69, 4), // "name"
QT_MOC_LITERAL(7, 74, 4), // "type"
QT_MOC_LITERAL(8, 79, 13), // "stepCompleted"
QT_MOC_LITERAL(9, 93, 6), // "result"
QT_MOC_LITERAL(10, 100, 10), // "durationMs"
QT_MOC_LITERAL(11, 111, 12), // "nextFunction"
QT_MOC_LITERAL(12, 124, 10), // "jumpToStep"
QT_MOC_LITERAL(13, 135, 13), // "startSequence"
QT_MOC_LITERAL(14, 149, 11), // "togglePause"
QT_MOC_LITERAL(15, 161, 12), // "stopSequence"
QT_MOC_LITERAL(16, 174, 14), // "jumpToSelected"
QT_MOC_LITERAL(17, 189, 18) // "onSequenceFinished"

    },
    "SequenceExecutor\0sequenceStarted\0\0"
    "sequenceFinished\0stepStarted\0index\0"
    "name\0type\0stepCompleted\0result\0"
    "durationMs\0nextFunction\0jumpToStep\0"
    "startSequence\0togglePause\0stopSequence\0"
    "jumpToSelected\0onSequenceFinished"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_SequenceExecutor[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      12,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       6,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    0,   74,    2, 0x06 /* Public */,
       3,    0,   75,    2, 0x06 /* Public */,
       4,    3,   76,    2, 0x06 /* Public */,
       8,    5,   83,    2, 0x06 /* Public */,
      11,    0,   94,    2, 0x06 /* Public */,
      12,    1,   95,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      13,    0,   98,    2, 0x0a /* Public */,
      14,    0,   99,    2, 0x0a /* Public */,
      15,    0,  100,    2, 0x0a /* Public */,
      16,    0,  101,    2, 0x0a /* Public */,
      16,    1,  102,    2, 0x08 /* Private */,
      17,    0,  105,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int, QMetaType::QString, QMetaType::QString,    5,    6,    7,
    QMetaType::Void, QMetaType::Int, QMetaType::QString, QMetaType::QString, QMetaType::QVariant, QMetaType::LongLong,    5,    6,    7,    9,   10,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int,    5,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int,    5,
    QMetaType::Void,

       0        // eod
};

void SequenceExecutor::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<SequenceExecutor *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->sequenceStarted(); break;
        case 1: _t->sequenceFinished(); break;
        case 2: _t->stepStarted((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2])),(*reinterpret_cast< const QString(*)>(_a[3]))); break;
        case 3: _t->stepCompleted((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2])),(*reinterpret_cast< const QString(*)>(_a[3])),(*reinterpret_cast< const QVariant(*)>(_a[4])),(*reinterpret_cast< qint64(*)>(_a[5]))); break;
        case 4: _t->nextFunction(); break;
        case 5: _t->jumpToStep((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 6: _t->startSequence(); break;
        case 7: _t->togglePause(); break;
        case 8: _t->stopSequence(); break;
        case 9: _t->jumpToSelected(); break;
        case 10: _t->jumpToSelected((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 11: _t->onSequenceFinished(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (SequenceExecutor::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SequenceExecutor::sequenceStarted)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (SequenceExecutor::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SequenceExecutor::sequenceFinished)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (SequenceExecutor::*)(int , const QString & , const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SequenceExecutor::stepStarted)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (SequenceExecutor::*)(int , const QString & , const QString & , const QVariant & , qint64 );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SequenceExecutor::stepCompleted)) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (SequenceExecutor::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SequenceExecutor::nextFunction)) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (SequenceExecutor::*)(int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SequenceExecutor::jumpToStep)) {
                *result = 5;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject SequenceExecutor::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_SequenceExecutor.data,
    qt_meta_data_SequenceExecutor,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *SequenceExecutor::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *SequenceExecutor::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_SequenceExecutor.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int SequenceExecutor::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 12)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 12;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 12)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 12;
    }
    return _id;
}

// SIGNAL 0
void SequenceExecutor::sequenceStarted()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void SequenceExecutor::sequenceFinished()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void SequenceExecutor::stepStarted(int _t1, const QString & _t2, const QString & _t3)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t3))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void SequenceExecutor::stepCompleted(int _t1, const QString & _t2, const QString & _t3, const QVariant & _t4, qint64 _t5)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t3))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t4))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t5))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void SequenceExecutor::nextFunction()
{
    QMetaObject::activate(this, &staticMetaObject, 4, nullptr);
}

// SIGNAL 5
void SequenceExecutor::jumpToStep(int _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 5, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
