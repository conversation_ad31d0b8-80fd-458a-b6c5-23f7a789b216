/****************************************************************************
** Meta object code from reading C++ file 'chanaleditform.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.15.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../MainSet/channel/chanaleditform.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'chanaleditform.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.15.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_ChanalEditForm_t {
    QByteArrayData data[19];
    char stringdata0[265];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_ChanalEditForm_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_ChanalEditForm_t qt_meta_stringdata_ChanalEditForm = {
    {
QT_MOC_LITERAL(0, 0, 14), // "ChanalEditForm"
QT_MOC_LITERAL(1, 15, 20), // "channelInfoConfirmed"
QT_MOC_LITERAL(2, 36, 0), // ""
QT_MOC_LITERAL(3, 37, 20), // "SetChannelInfoStruct"
QT_MOC_LITERAL(4, 58, 4), // "info"
QT_MOC_LITERAL(5, 63, 9), // "operation"
QT_MOC_LITERAL(6, 73, 20), // "ChanalEditForm::Mode"
QT_MOC_LITERAL(7, 94, 4), // "mode"
QT_MOC_LITERAL(8, 99, 14), // "onNameConflict"
QT_MOC_LITERAL(9, 114, 31), // "on_DriverTypecomboBox_activated"
QT_MOC_LITERAL(10, 146, 4), // "arg1"
QT_MOC_LITERAL(11, 151, 27), // "on_DriverComboBox_activated"
QT_MOC_LITERAL(12, 179, 5), // "index"
QT_MOC_LITERAL(13, 185, 20), // "on_UpdateBtn_clicked"
QT_MOC_LITERAL(14, 206, 17), // "on_YesBtn_clicked"
QT_MOC_LITERAL(15, 224, 18), // "on_ExitBtn_clicked"
QT_MOC_LITERAL(16, 243, 4), // "Mode"
QT_MOC_LITERAL(17, 248, 8), // "isOutput"
QT_MOC_LITERAL(18, 257, 7) // "isInput"

    },
    "ChanalEditForm\0channelInfoConfirmed\0"
    "\0SetChannelInfoStruct\0info\0operation\0"
    "ChanalEditForm::Mode\0mode\0onNameConflict\0"
    "on_DriverTypecomboBox_activated\0arg1\0"
    "on_DriverComboBox_activated\0index\0"
    "on_UpdateBtn_clicked\0on_YesBtn_clicked\0"
    "on_ExitBtn_clicked\0Mode\0isOutput\0"
    "isInput"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_ChanalEditForm[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       7,   14, // methods
       0,    0, // properties
       1,   66, // enums/sets
       0,    0, // constructors
       0,       // flags
       1,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    3,   49,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       8,    0,   56,    2, 0x0a /* Public */,
       9,    1,   57,    2, 0x08 /* Private */,
      11,    1,   60,    2, 0x08 /* Private */,
      13,    0,   63,    2, 0x08 /* Private */,
      14,    0,   64,    2, 0x08 /* Private */,
      15,    0,   65,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, 0x80000000 | 3, QMetaType::QString, 0x80000000 | 6,    4,    5,    7,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,   10,
    QMetaType::Void, QMetaType::Int,   12,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

 // enums: name, alias, flags, count, data
      16,   16, 0x2,    2,   71,

 // enum data: key, value
      17, uint(ChanalEditForm::Mode::isOutput),
      18, uint(ChanalEditForm::Mode::isInput),

       0        // eod
};

void ChanalEditForm::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<ChanalEditForm *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->channelInfoConfirmed((*reinterpret_cast< const SetChannelInfoStruct(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2])),(*reinterpret_cast< ChanalEditForm::Mode(*)>(_a[3]))); break;
        case 1: _t->onNameConflict(); break;
        case 2: _t->on_DriverTypecomboBox_activated((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 3: _t->on_DriverComboBox_activated((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 4: _t->on_UpdateBtn_clicked(); break;
        case 5: _t->on_YesBtn_clicked(); break;
        case 6: _t->on_ExitBtn_clicked(); break;
        default: ;
        }
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 0:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< SetChannelInfoStruct >(); break;
            }
            break;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (ChanalEditForm::*)(const SetChannelInfoStruct & , const QString & , ChanalEditForm::Mode );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ChanalEditForm::channelInfoConfirmed)) {
                *result = 0;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject ChanalEditForm::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_meta_stringdata_ChanalEditForm.data,
    qt_meta_data_ChanalEditForm,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *ChanalEditForm::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ChanalEditForm::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_ChanalEditForm.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int ChanalEditForm::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 7)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 7;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 7)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 7;
    }
    return _id;
}

// SIGNAL 0
void ChanalEditForm::channelInfoConfirmed(const SetChannelInfoStruct & _t1, const QString & _t2, ChanalEditForm::Mode _t3)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t3))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
