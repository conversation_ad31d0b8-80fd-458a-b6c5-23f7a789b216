\hypertarget{namespaceIir_1_1Custom}{}\doxysubsection{Iir\+::Custom Namespace Reference}
\label{namespaceIir_1_1Custom}\index{Iir::Custom@{Iir::Custom}}
\doxysubsubsection*{Classes}
\begin{DoxyCompactItemize}
\item 
struct \mbox{\hyperlink{structIir_1_1Custom_1_1OnePole}{One\+Pole}}
\item 
struct \mbox{\hyperlink{structIir_1_1Custom_1_1TwoPole}{Two\+Pole}}
\item 
struct \mbox{\hyperlink{structIir_1_1Custom_1_1SOSCascade}{SOSCascade}}
\end{DoxyCompactItemize}


\doxysubsubsection{Detailed Description}
Single pole, \mbox{\hyperlink{classIir_1_1Biquad}{Biquad}} and cascade of Biquads with parameters allowing for directly setting the parameters. 