\hypertarget{structIir_1_1RBJ_1_1HighShelf}{}\doxysubsection{Iir\+::RBJ\+::High\+Shelf Struct Reference}
\label{structIir_1_1RBJ_1_1HighShelf}\index{Iir::RBJ::HighShelf@{Iir::RBJ::HighShelf}}


{\ttfamily \#include $<$RBJ.\+h$>$}

Inheritance diagram for Iir\+::RBJ\+::High\+Shelf\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=3.000000cm]{structIir_1_1RBJ_1_1HighShelf}
\end{center}
\end{figure}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
void \mbox{\hyperlink{structIir_1_1RBJ_1_1HighShelf_a0d5dbf5b05c08663bedf00e4a7f983db}{setupN}} (double cutoff\+Frequency, double gain\+Db, double shelf\+Slope=1)
\item 
void \mbox{\hyperlink{structIir_1_1RBJ_1_1HighShelf_a4887616ad520c27fce55d76d915d5bf6}{setup}} (double sample\+Rate, double cutoff\+Frequency, double gain\+Db, double shelf\+Slope=1)
\end{DoxyCompactItemize}


\doxysubsubsection{Detailed Description}
High shelf\+: 0db in the stopband and gain\+Db in the passband. 

\doxysubsubsection{Member Function Documentation}
\mbox{\Hypertarget{structIir_1_1RBJ_1_1HighShelf_a4887616ad520c27fce55d76d915d5bf6}\label{structIir_1_1RBJ_1_1HighShelf_a4887616ad520c27fce55d76d915d5bf6}} 
\index{Iir::RBJ::HighShelf@{Iir::RBJ::HighShelf}!setup@{setup}}
\index{setup@{setup}!Iir::RBJ::HighShelf@{Iir::RBJ::HighShelf}}
\doxyparagraph{\texorpdfstring{setup()}{setup()}}
{\footnotesize\ttfamily void Iir\+::\+RBJ\+::\+High\+Shelf\+::setup (\begin{DoxyParamCaption}\item[{double}]{sample\+Rate,  }\item[{double}]{cutoff\+Frequency,  }\item[{double}]{gain\+Db,  }\item[{double}]{shelf\+Slope = {\ttfamily 1} }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients 
\begin{DoxyParams}{Parameters}
{\em sample\+Rate} & Sampling rate \\
\hline
{\em cutoff\+Frequency} & Cutoff frequency \\
\hline
{\em gain\+Db} & Gain in the passband \\
\hline
{\em shelf\+Slope} & Slope between stop/passband. 1 = as steep as it can. \\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{structIir_1_1RBJ_1_1HighShelf_a0d5dbf5b05c08663bedf00e4a7f983db}\label{structIir_1_1RBJ_1_1HighShelf_a0d5dbf5b05c08663bedf00e4a7f983db}} 
\index{Iir::RBJ::HighShelf@{Iir::RBJ::HighShelf}!setupN@{setupN}}
\index{setupN@{setupN}!Iir::RBJ::HighShelf@{Iir::RBJ::HighShelf}}
\doxyparagraph{\texorpdfstring{setupN()}{setupN()}}
{\footnotesize\ttfamily void Iir\+::\+RBJ\+::\+High\+Shelf\+::setupN (\begin{DoxyParamCaption}\item[{double}]{cutoff\+Frequency,  }\item[{double}]{gain\+Db,  }\item[{double}]{shelf\+Slope = {\ttfamily 1} }\end{DoxyParamCaption})}

Calculates the coefficients 
\begin{DoxyParams}{Parameters}
{\em cutoff\+Frequency} & Normalised cutoff frequency \\
\hline
{\em gain\+Db} & Gain in the passband \\
\hline
{\em shelf\+Slope} & Slope between stop/passband. 1 = as steep as it can. \\
\hline
\end{DoxyParams}


The documentation for this struct was generated from the following files\+:\begin{DoxyCompactItemize}
\item 
iir/RBJ.\+h\item 
iir/RBJ.\+cpp\end{DoxyCompactItemize}
