\hypertarget{structIir_1_1ChebyshevII_1_1BandShelf}{}\doxysubsection{Iir\+::Chebyshev\+II\+::Band\+Shelf$<$ Filter\+Order, State\+Type $>$ Struct Template Reference}
\label{structIir_1_1ChebyshevII_1_1BandShelf}\index{Iir::ChebyshevII::BandShelf$<$ FilterOrder, StateType $>$@{Iir::ChebyshevII::BandShelf$<$ FilterOrder, StateType $>$}}


{\ttfamily \#include $<$Chebyshev\+II.\+h$>$}

Inheritance diagram for Iir\+::Chebyshev\+II\+::Band\+Shelf$<$ Filter\+Order, State\+Type $>$\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=2.434783cm]{structIir_1_1ChebyshevII_1_1BandShelf}
\end{center}
\end{figure}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
void \mbox{\hyperlink{structIir_1_1ChebyshevII_1_1BandShelf_adca87357a1e04fe298144cae7b200670}{setup}} (double sample\+Rate, double center\+Frequency, double width\+Frequency, double gain\+Db, double stop\+Band\+Db)
\item 
void \mbox{\hyperlink{structIir_1_1ChebyshevII_1_1BandShelf_a335408ec59e8673fd18dfd603096c7f5}{setup}} (int req\+Order, double sample\+Rate, double center\+Frequency, double width\+Frequency, double gain\+Db, double stop\+Band\+Db)
\item 
void \mbox{\hyperlink{structIir_1_1ChebyshevII_1_1BandShelf_a78ef454e71eb75c85c74e2cdb66e08fd}{setupN}} (double center\+Frequency, double width\+Frequency, double gain\+Db, double stop\+Band\+Db)
\item 
void \mbox{\hyperlink{structIir_1_1ChebyshevII_1_1BandShelf_a468f2edb4849f8f60b65bdfbb3e309e3}{setupN}} (int req\+Order, double center\+Frequency, double width\+Frequency, double gain\+Db, double stop\+Band\+Db)
\end{DoxyCompactItemize}


\doxysubsubsection{Detailed Description}
\subsubsection*{template$<$int Filter\+Order = 4, class State\+Type = Direct\+Form\+II$>$\newline
struct Iir\+::\+Chebyshev\+II\+::\+Band\+Shelf$<$ Filter\+Order, State\+Type $>$}

\mbox{\hyperlink{namespaceIir_1_1ChebyshevII}{Chebyshev\+II}} bandshelf filter. Bandpass with specified gain and 0 dB gain in the stopband. 
\begin{DoxyParams}{Parameters}
{\em Filter\+Order} & Reserves memory for a filter of the order Filter\+Order \\
\hline
{\em State\+Type} & The filter topology\+: \mbox{\hyperlink{classIir_1_1DirectFormI}{Direct\+FormI}}, \mbox{\hyperlink{classIir_1_1DirectFormII}{Direct\+Form\+II}}, ... \\
\hline
\end{DoxyParams}


\doxysubsubsection{Member Function Documentation}
\mbox{\Hypertarget{structIir_1_1ChebyshevII_1_1BandShelf_adca87357a1e04fe298144cae7b200670}\label{structIir_1_1ChebyshevII_1_1BandShelf_adca87357a1e04fe298144cae7b200670}} 
\index{Iir::ChebyshevII::BandShelf$<$ FilterOrder, StateType $>$@{Iir::ChebyshevII::BandShelf$<$ FilterOrder, StateType $>$}!setup@{setup}}
\index{setup@{setup}!Iir::ChebyshevII::BandShelf$<$ FilterOrder, StateType $>$@{Iir::ChebyshevII::BandShelf$<$ FilterOrder, StateType $>$}}
\doxyparagraph{\texorpdfstring{setup()}{setup()}\hspace{0.1cm}{\footnotesize\ttfamily [1/2]}}
{\footnotesize\ttfamily template$<$int Filter\+Order = 4, class State\+Type  = Direct\+Form\+II$>$ \\
void \mbox{\hyperlink{structIir_1_1ChebyshevII_1_1BandShelf}{Iir\+::\+Chebyshev\+II\+::\+Band\+Shelf}}$<$ Filter\+Order, State\+Type $>$\+::setup (\begin{DoxyParamCaption}\item[{double}]{sample\+Rate,  }\item[{double}]{center\+Frequency,  }\item[{double}]{width\+Frequency,  }\item[{double}]{gain\+Db,  }\item[{double}]{stop\+Band\+Db }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients of the filter 
\begin{DoxyParams}{Parameters}
{\em sample\+Rate} & Sampling rate \\
\hline
{\em center\+Frequency} & Center frequency of the bandpass \\
\hline
{\em width\+Frequency} & Width of the bandpass \\
\hline
{\em gain\+Db} & Gain in the passband. The stopband has always 0dB. \\
\hline
{\em stop\+Band\+Db} & Permitted ripples in dB in the stopband \\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{structIir_1_1ChebyshevII_1_1BandShelf_a335408ec59e8673fd18dfd603096c7f5}\label{structIir_1_1ChebyshevII_1_1BandShelf_a335408ec59e8673fd18dfd603096c7f5}} 
\index{Iir::ChebyshevII::BandShelf$<$ FilterOrder, StateType $>$@{Iir::ChebyshevII::BandShelf$<$ FilterOrder, StateType $>$}!setup@{setup}}
\index{setup@{setup}!Iir::ChebyshevII::BandShelf$<$ FilterOrder, StateType $>$@{Iir::ChebyshevII::BandShelf$<$ FilterOrder, StateType $>$}}
\doxyparagraph{\texorpdfstring{setup()}{setup()}\hspace{0.1cm}{\footnotesize\ttfamily [2/2]}}
{\footnotesize\ttfamily template$<$int Filter\+Order = 4, class State\+Type  = Direct\+Form\+II$>$ \\
void \mbox{\hyperlink{structIir_1_1ChebyshevII_1_1BandShelf}{Iir\+::\+Chebyshev\+II\+::\+Band\+Shelf}}$<$ Filter\+Order, State\+Type $>$\+::setup (\begin{DoxyParamCaption}\item[{int}]{req\+Order,  }\item[{double}]{sample\+Rate,  }\item[{double}]{center\+Frequency,  }\item[{double}]{width\+Frequency,  }\item[{double}]{gain\+Db,  }\item[{double}]{stop\+Band\+Db }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients of the filter 
\begin{DoxyParams}{Parameters}
{\em req\+Order} & Requested order which can be less than the instantiated one \\
\hline
{\em sample\+Rate} & Sampling rate \\
\hline
{\em center\+Frequency} & Center frequency of the bandpass \\
\hline
{\em width\+Frequency} & Width of the bandpass \\
\hline
{\em gain\+Db} & Gain in the passband. The stopband has always 0dB. \\
\hline
{\em stop\+Band\+Db} & Permitted ripples in dB in the stopband \\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{structIir_1_1ChebyshevII_1_1BandShelf_a78ef454e71eb75c85c74e2cdb66e08fd}\label{structIir_1_1ChebyshevII_1_1BandShelf_a78ef454e71eb75c85c74e2cdb66e08fd}} 
\index{Iir::ChebyshevII::BandShelf$<$ FilterOrder, StateType $>$@{Iir::ChebyshevII::BandShelf$<$ FilterOrder, StateType $>$}!setupN@{setupN}}
\index{setupN@{setupN}!Iir::ChebyshevII::BandShelf$<$ FilterOrder, StateType $>$@{Iir::ChebyshevII::BandShelf$<$ FilterOrder, StateType $>$}}
\doxyparagraph{\texorpdfstring{setupN()}{setupN()}\hspace{0.1cm}{\footnotesize\ttfamily [1/2]}}
{\footnotesize\ttfamily template$<$int Filter\+Order = 4, class State\+Type  = Direct\+Form\+II$>$ \\
void \mbox{\hyperlink{structIir_1_1ChebyshevII_1_1BandShelf}{Iir\+::\+Chebyshev\+II\+::\+Band\+Shelf}}$<$ Filter\+Order, State\+Type $>$\+::setupN (\begin{DoxyParamCaption}\item[{double}]{center\+Frequency,  }\item[{double}]{width\+Frequency,  }\item[{double}]{gain\+Db,  }\item[{double}]{stop\+Band\+Db }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients of the filter 
\begin{DoxyParams}{Parameters}
{\em center\+Frequency} & Normalised centre frequency (0..1/2) of the bandpass \\
\hline
{\em width\+Frequency} & Width of the bandpass \\
\hline
{\em gain\+Db} & Gain in the passband. The stopband has always 0dB. \\
\hline
{\em stop\+Band\+Db} & Permitted ripples in dB in the stopband \\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{structIir_1_1ChebyshevII_1_1BandShelf_a468f2edb4849f8f60b65bdfbb3e309e3}\label{structIir_1_1ChebyshevII_1_1BandShelf_a468f2edb4849f8f60b65bdfbb3e309e3}} 
\index{Iir::ChebyshevII::BandShelf$<$ FilterOrder, StateType $>$@{Iir::ChebyshevII::BandShelf$<$ FilterOrder, StateType $>$}!setupN@{setupN}}
\index{setupN@{setupN}!Iir::ChebyshevII::BandShelf$<$ FilterOrder, StateType $>$@{Iir::ChebyshevII::BandShelf$<$ FilterOrder, StateType $>$}}
\doxyparagraph{\texorpdfstring{setupN()}{setupN()}\hspace{0.1cm}{\footnotesize\ttfamily [2/2]}}
{\footnotesize\ttfamily template$<$int Filter\+Order = 4, class State\+Type  = Direct\+Form\+II$>$ \\
void \mbox{\hyperlink{structIir_1_1ChebyshevII_1_1BandShelf}{Iir\+::\+Chebyshev\+II\+::\+Band\+Shelf}}$<$ Filter\+Order, State\+Type $>$\+::setupN (\begin{DoxyParamCaption}\item[{int}]{req\+Order,  }\item[{double}]{center\+Frequency,  }\item[{double}]{width\+Frequency,  }\item[{double}]{gain\+Db,  }\item[{double}]{stop\+Band\+Db }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients of the filter 
\begin{DoxyParams}{Parameters}
{\em req\+Order} & Requested order which can be less than the instantiated one \\
\hline
{\em center\+Frequency} & Normalised centre frequency (0..1/2) of the bandpass \\
\hline
{\em width\+Frequency} & Width of the bandpass \\
\hline
{\em gain\+Db} & Gain in the passband. The stopband has always 0dB. \\
\hline
{\em stop\+Band\+Db} & Permitted ripples in dB in the stopband \\
\hline
\end{DoxyParams}


The documentation for this struct was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
iir/Chebyshev\+II.\+h\end{DoxyCompactItemize}
