<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>iir1: iir/PoleFilter.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">iir1
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_5231e4e0b49b19c482755f817f3ac47e.html">iir</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">PoleFilter.h</div>  </div>
</div><!--header-->
<div class="contents">
<div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160; </div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;<span class="preprocessor">#ifndef IIR1_POLEFILTER_H</span></div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;<span class="preprocessor">#define IIR1_POLEFILTER_H</span></div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160; </div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;<span class="preprocessor">#include &quot;Common.h&quot;</span></div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;<span class="preprocessor">#include &quot;MathSupplement.h&quot;</span></div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;<span class="preprocessor">#include &quot;Cascade.h&quot;</span></div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;<span class="preprocessor">#include &quot;State.h&quot;</span></div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160; </div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;<span class="comment">// Purely for debugging...</span></div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;<span class="preprocessor">#include &lt;iostream&gt;</span></div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160; </div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespaceIir.html">Iir</a> {</div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160; </div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;<span class="comment">/***</span></div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;<span class="comment"> * Base for filters designed via algorithmic placement of poles and zeros.</span></div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;<span class="comment"> * Typically, the filter is first designed as a half-band low pass or</span></div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;<span class="comment"> * low shelf analog filter (s-plane). Then, using a transformation such</span></div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;<span class="comment"> * as the ones from Constantinides, the poles and zeros of the analog filter</span></div>
<div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;<span class="comment"> * are calculated in the z-plane.</span></div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;<span class="comment"> ***/</span></div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160; </div>
<div class="line"><a name="l00062"></a><span class="lineno"><a class="line" href="classIir_1_1PoleFilterBase2.html">   62</a></span>&#160;        <span class="keyword">class </span>IIR_EXPORT <a class="code" href="classIir_1_1PoleFilterBase2.html">PoleFilterBase2</a> : <span class="keyword">public</span> <a class="code" href="classIir_1_1Cascade.html">Cascade</a></div>
<div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;        {</div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;        <span class="keyword">public</span>:</div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;                <span class="comment">// This gets the poles/zeros directly from the digital</span></div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;                <span class="comment">// prototype. It is used to double check the correctness</span></div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;                <span class="comment">// of the recovery of pole/zeros from biquad coefficients.</span></div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;                <span class="comment">//</span></div>
<div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;                <span class="comment">// It can also be used to accelerate the interpolation</span></div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;                <span class="comment">// of pole/zeros for parameter modulation, since a pole</span></div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;                <span class="comment">// filter already has them calculated</span></div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160; </div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;                <a class="code" href="classIir_1_1PoleFilterBase2.html">PoleFilterBase2</a>() = <span class="keywordflow">default</span>;</div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160; </div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;                std::vector&lt;PoleZeroPair&gt; getPoleZeros ()<span class="keyword"> const</span></div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;<span class="keyword">                </span>{</div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;                        std::vector&lt;PoleZeroPair&gt; vpz;</div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;                        <span class="keyword">const</span> <span class="keywordtype">int</span> pairs = (m_digitalProto.getNumPoles () + 1) / 2;</div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;                        <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; pairs; ++i)</div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;                                vpz.push_back (m_digitalProto[i]);</div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;                        <span class="keywordflow">return</span> vpz;</div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;                }</div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;        </div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;        <span class="keyword">protected</span>:</div>
<div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;                <a class="code" href="classIir_1_1LayoutBase.html">LayoutBase</a> m_digitalProto = {};</div>
<div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;        };</div>
<div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160; </div>
<div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160; </div>
<div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">class</span> AnalogPrototype&gt;</div>
<div class="line"><a name="l00094"></a><span class="lineno"><a class="line" href="classIir_1_1PoleFilterBase.html">   94</a></span>&#160;        <span class="keyword">class </span><a class="code" href="classIir_1_1PoleFilterBase.html">PoleFilterBase</a> : <span class="keyword">public</span> <a class="code" href="classIir_1_1PoleFilterBase2.html">PoleFilterBase2</a></div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;        {</div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;        <span class="keyword">protected</span>:</div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;                <span class="keywordtype">void</span> setPrototypeStorage (<span class="keyword">const</span> <a class="code" href="classIir_1_1LayoutBase.html">LayoutBase</a>&amp; analogStorage,</div>
<div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;                                          <span class="keyword">const</span> <a class="code" href="classIir_1_1LayoutBase.html">LayoutBase</a>&amp; digitalStorage)</div>
<div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;                {</div>
<div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;                        m_analogProto.setStorage (analogStorage);</div>
<div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;                        m_digitalProto = digitalStorage;</div>
<div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;                }</div>
<div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;                </div>
<div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;        <span class="keyword">protected</span>:</div>
<div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;                AnalogPrototype m_analogProto = {};</div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;        };</div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160; </div>
<div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;<span class="comment">//------------------------------------------------------------------------------</span></div>
<div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160; </div>
<div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">class </span>BaseClass,</div>
<div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;                  <span class="keyword">class </span>StateType,</div>
<div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;                  <span class="keywordtype">int</span> MaxAnalogPoles,</div>
<div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;                  <span class="keywordtype">int</span> MaxDigitalPoles = MaxAnalogPoles&gt;</div>
<div class="line"><a name="l00117"></a><span class="lineno"><a class="line" href="structIir_1_1PoleFilter.html">  117</a></span>&#160;        <span class="keyword">struct </span><a class="code" href="structIir_1_1PoleFilter.html">PoleFilter</a> : BaseClass</div>
<div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;                , <a class="code" href="classIir_1_1CascadeStages.html">CascadeStages</a> &lt;(MaxDigitalPoles + 1) / 2 , StateType&gt;</div>
<div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;        {</div>
<div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;        <span class="keyword">public</span>:</div>
<div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;                <a class="code" href="structIir_1_1PoleFilter.html">PoleFilter</a> ()</div>
<div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;                        {</div>
<div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;                                <span class="comment">// This glues together the factored base classes</span></div>
<div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;                                <span class="comment">// with the templatized storage classes.</span></div>
<div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;                                BaseClass::setCascadeStorage (this-&gt;<a class="code" href="classIir_1_1CascadeStages.html#a034a9be8ae590b814c8499898a93987a">getCascadeStorage</a>());</div>
<div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;                                BaseClass::setPrototypeStorage (m_analogStorage, m_digitalStorage);</div>
<div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;                                <a class="code" href="classIir_1_1CascadeStages.html">CascadeStages</a>&lt;(MaxDigitalPoles + 1) / 2 , StateType&gt;::<a class="code" href="classIir_1_1CascadeStages.html#ae5253fde0be7ccfa459a3f70fe8e3e31">reset</a>();</div>
<div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;                        }</div>
<div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;                </div>
<div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;                <a class="code" href="structIir_1_1PoleFilter.html">PoleFilter</a>(<span class="keyword">const</span> <a class="code" href="structIir_1_1PoleFilter.html">PoleFilter</a>&amp;) = <span class="keywordflow">default</span>;</div>
<div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;                </div>
<div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;                <a class="code" href="structIir_1_1PoleFilter.html">PoleFilter</a>&amp; operator=(<span class="keyword">const</span> <a class="code" href="structIir_1_1PoleFilter.html">PoleFilter</a>&amp;)</div>
<div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;                        {</div>
<div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;                                <span class="comment">// Reset the filter state when copied for now</span></div>
<div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;                                <a class="code" href="classIir_1_1CascadeStages.html">CascadeStages</a>&lt;(MaxDigitalPoles + 1) / 2 , StateType&gt;::<a class="code" href="classIir_1_1CascadeStages.html#ae5253fde0be7ccfa459a3f70fe8e3e31">reset</a>();</div>
<div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;                                <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div>
<div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;                        }</div>
<div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;                </div>
<div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;        <span class="keyword">private</span>:</div>
<div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;                <a class="code" href="classIir_1_1Layout.html">Layout &lt;MaxAnalogPoles&gt;</a> m_analogStorage = {};</div>
<div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;                <a class="code" href="classIir_1_1Layout.html">Layout &lt;MaxDigitalPoles&gt;</a> m_digitalStorage = {};</div>
<div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;        };</div>
<div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160; </div>
<div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;<span class="comment">//------------------------------------------------------------------------------</span></div>
<div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160; </div>
<div class="line"><a name="l00160"></a><span class="lineno"><a class="line" href="classIir_1_1LowPassTransform.html">  160</a></span>&#160;        <span class="keyword">class </span>IIR_EXPORT <a class="code" href="classIir_1_1LowPassTransform.html">LowPassTransform</a></div>
<div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;        {</div>
<div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;        <span class="keyword">public</span>:</div>
<div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;        <a class="code" href="classIir_1_1LowPassTransform.html">LowPassTransform</a> (<span class="keywordtype">double</span> fc,</div>
<div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;                          <a class="code" href="classIir_1_1LayoutBase.html">LayoutBase</a>&amp; digital,</div>
<div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;                          <a class="code" href="classIir_1_1LayoutBase.html">LayoutBase</a> <span class="keyword">const</span>&amp; analog);</div>
<div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160; </div>
<div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160;        <span class="keyword">private</span>:</div>
<div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;        complex_t transform (complex_t c);</div>
<div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160; </div>
<div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;        <span class="keywordtype">double</span> f = 0.0;</div>
<div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;        };</div>
<div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160; </div>
<div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;<span class="comment">//------------------------------------------------------------------------------</span></div>
<div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160; </div>
<div class="line"><a name="l00178"></a><span class="lineno"><a class="line" href="classIir_1_1HighPassTransform.html">  178</a></span>&#160;        <span class="keyword">class </span>IIR_EXPORT <a class="code" href="classIir_1_1HighPassTransform.html">HighPassTransform</a></div>
<div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;        {</div>
<div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;        <span class="keyword">public</span>:</div>
<div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;        <a class="code" href="classIir_1_1HighPassTransform.html">HighPassTransform</a> (<span class="keywordtype">double</span> fc,</div>
<div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;                           <a class="code" href="classIir_1_1LayoutBase.html">LayoutBase</a>&amp; digital,</div>
<div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;                           <a class="code" href="classIir_1_1LayoutBase.html">LayoutBase</a> <span class="keyword">const</span>&amp; analog);</div>
<div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160; </div>
<div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;        <span class="keyword">private</span>:</div>
<div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;        complex_t transform (complex_t c);</div>
<div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160; </div>
<div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;        <span class="keywordtype">double</span> f = 0.0;</div>
<div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;        };</div>
<div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160; </div>
<div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160;<span class="comment">//------------------------------------------------------------------------------</span></div>
<div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160; </div>
<div class="line"><a name="l00196"></a><span class="lineno"><a class="line" href="classIir_1_1BandPassTransform.html">  196</a></span>&#160;        <span class="keyword">class </span>IIR_EXPORT <a class="code" href="classIir_1_1BandPassTransform.html">BandPassTransform</a></div>
<div class="line"><a name="l00197"></a><span class="lineno">  197</span>&#160;        {</div>
<div class="line"><a name="l00198"></a><span class="lineno">  198</span>&#160; </div>
<div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160;        <span class="keyword">public</span>:</div>
<div class="line"><a name="l00200"></a><span class="lineno">  200</span>&#160;        <a class="code" href="classIir_1_1BandPassTransform.html">BandPassTransform</a> (<span class="keywordtype">double</span> fc,</div>
<div class="line"><a name="l00201"></a><span class="lineno">  201</span>&#160;                           <span class="keywordtype">double</span> fw,</div>
<div class="line"><a name="l00202"></a><span class="lineno">  202</span>&#160;                           <a class="code" href="classIir_1_1LayoutBase.html">LayoutBase</a>&amp; digital,</div>
<div class="line"><a name="l00203"></a><span class="lineno">  203</span>&#160;                           <a class="code" href="classIir_1_1LayoutBase.html">LayoutBase</a> <span class="keyword">const</span>&amp; analog);</div>
<div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160; </div>
<div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160;        <span class="keyword">private</span>:</div>
<div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160;        <a class="code" href="structIir_1_1ComplexPair.html">ComplexPair</a> transform (complex_t c);</div>
<div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160; </div>
<div class="line"><a name="l00208"></a><span class="lineno">  208</span>&#160;        <span class="keywordtype">double</span> wc = 0.0;</div>
<div class="line"><a name="l00209"></a><span class="lineno">  209</span>&#160;        <span class="keywordtype">double</span> wc2 = 0.0;</div>
<div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160;        <span class="keywordtype">double</span> a = 0.0;</div>
<div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;        <span class="keywordtype">double</span> b = 0.0;</div>
<div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160;        <span class="keywordtype">double</span> a2 = 0.0;</div>
<div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160;        <span class="keywordtype">double</span> b2 = 0.0;</div>
<div class="line"><a name="l00214"></a><span class="lineno">  214</span>&#160;        <span class="keywordtype">double</span> ab = 0.0;</div>
<div class="line"><a name="l00215"></a><span class="lineno">  215</span>&#160;        <span class="keywordtype">double</span> ab_2 = 0.0;</div>
<div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;        };</div>
<div class="line"><a name="l00217"></a><span class="lineno">  217</span>&#160; </div>
<div class="line"><a name="l00218"></a><span class="lineno">  218</span>&#160;<span class="comment">//------------------------------------------------------------------------------</span></div>
<div class="line"><a name="l00219"></a><span class="lineno">  219</span>&#160; </div>
<div class="line"><a name="l00223"></a><span class="lineno"><a class="line" href="classIir_1_1BandStopTransform.html">  223</a></span>&#160;        <span class="keyword">class </span>IIR_EXPORT <a class="code" href="classIir_1_1BandStopTransform.html">BandStopTransform</a></div>
<div class="line"><a name="l00224"></a><span class="lineno">  224</span>&#160;        {</div>
<div class="line"><a name="l00225"></a><span class="lineno">  225</span>&#160;        <span class="keyword">public</span>:</div>
<div class="line"><a name="l00226"></a><span class="lineno">  226</span>&#160;        <a class="code" href="classIir_1_1BandStopTransform.html">BandStopTransform</a> (<span class="keywordtype">double</span> fc,</div>
<div class="line"><a name="l00227"></a><span class="lineno">  227</span>&#160;                           <span class="keywordtype">double</span> fw,</div>
<div class="line"><a name="l00228"></a><span class="lineno">  228</span>&#160;                           <a class="code" href="classIir_1_1LayoutBase.html">LayoutBase</a>&amp; digital,</div>
<div class="line"><a name="l00229"></a><span class="lineno">  229</span>&#160;                           <a class="code" href="classIir_1_1LayoutBase.html">LayoutBase</a> <span class="keyword">const</span>&amp; analog);</div>
<div class="line"><a name="l00230"></a><span class="lineno">  230</span>&#160; </div>
<div class="line"><a name="l00231"></a><span class="lineno">  231</span>&#160;        <span class="keyword">private</span>:</div>
<div class="line"><a name="l00232"></a><span class="lineno">  232</span>&#160;        <a class="code" href="structIir_1_1ComplexPair.html">ComplexPair</a> transform (complex_t c);</div>
<div class="line"><a name="l00233"></a><span class="lineno">  233</span>&#160; </div>
<div class="line"><a name="l00234"></a><span class="lineno">  234</span>&#160;        <span class="keywordtype">double</span> wc = 0.0;</div>
<div class="line"><a name="l00235"></a><span class="lineno">  235</span>&#160;        <span class="keywordtype">double</span> wc2 = 0.0;</div>
<div class="line"><a name="l00236"></a><span class="lineno">  236</span>&#160;        <span class="keywordtype">double</span> a = 0.0;</div>
<div class="line"><a name="l00237"></a><span class="lineno">  237</span>&#160;        <span class="keywordtype">double</span> b = 0.0;</div>
<div class="line"><a name="l00238"></a><span class="lineno">  238</span>&#160;        <span class="keywordtype">double</span> a2 = 0.0;</div>
<div class="line"><a name="l00239"></a><span class="lineno">  239</span>&#160;        <span class="keywordtype">double</span> b2 = 0.0;</div>
<div class="line"><a name="l00240"></a><span class="lineno">  240</span>&#160;        };</div>
<div class="line"><a name="l00241"></a><span class="lineno">  241</span>&#160; </div>
<div class="line"><a name="l00242"></a><span class="lineno">  242</span>&#160;}</div>
<div class="line"><a name="l00243"></a><span class="lineno">  243</span>&#160; </div>
<div class="line"><a name="l00244"></a><span class="lineno">  244</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="ttc" id="aclassIir_1_1BandPassTransform_html"><div class="ttname"><a href="classIir_1_1BandPassTransform.html">Iir::BandPassTransform</a></div><div class="ttdef"><b>Definition:</b> PoleFilter.h:197</div></div>
<div class="ttc" id="aclassIir_1_1BandStopTransform_html"><div class="ttname"><a href="classIir_1_1BandStopTransform.html">Iir::BandStopTransform</a></div><div class="ttdef"><b>Definition:</b> PoleFilter.h:224</div></div>
<div class="ttc" id="aclassIir_1_1CascadeStages_html"><div class="ttname"><a href="classIir_1_1CascadeStages.html">Iir::CascadeStages</a></div><div class="ttdef"><b>Definition:</b> Cascade.h:114</div></div>
<div class="ttc" id="aclassIir_1_1CascadeStages_html_a034a9be8ae590b814c8499898a93987a"><div class="ttname"><a href="classIir_1_1CascadeStages.html#a034a9be8ae590b814c8499898a93987a">Iir::CascadeStages&lt;(MaxAnalogPoles+1)/2, StateType &gt;::getCascadeStorage</a></div><div class="ttdeci">const Cascade::Storage getCascadeStorage()</div><div class="ttdef"><b>Definition:</b> Cascade.h:167</div></div>
<div class="ttc" id="aclassIir_1_1CascadeStages_html_ae5253fde0be7ccfa459a3f70fe8e3e31"><div class="ttname"><a href="classIir_1_1CascadeStages.html#ae5253fde0be7ccfa459a3f70fe8e3e31">Iir::CascadeStages&lt;(MaxAnalogPoles+1)/2, StateType &gt;::reset</a></div><div class="ttdeci">void reset()</div><div class="ttdef"><b>Definition:</b> Cascade.h:124</div></div>
<div class="ttc" id="aclassIir_1_1Cascade_html"><div class="ttname"><a href="classIir_1_1Cascade.html">Iir::Cascade</a></div><div class="ttdef"><b>Definition:</b> Cascade.h:50</div></div>
<div class="ttc" id="aclassIir_1_1HighPassTransform_html"><div class="ttname"><a href="classIir_1_1HighPassTransform.html">Iir::HighPassTransform</a></div><div class="ttdef"><b>Definition:</b> PoleFilter.h:179</div></div>
<div class="ttc" id="aclassIir_1_1LayoutBase_html"><div class="ttname"><a href="classIir_1_1LayoutBase.html">Iir::LayoutBase</a></div><div class="ttdef"><b>Definition:</b> Layout.h:63</div></div>
<div class="ttc" id="aclassIir_1_1Layout_html"><div class="ttname"><a href="classIir_1_1Layout.html">Iir::Layout&lt; MaxAnalogPoles &gt;</a></div></div>
<div class="ttc" id="aclassIir_1_1LowPassTransform_html"><div class="ttname"><a href="classIir_1_1LowPassTransform.html">Iir::LowPassTransform</a></div><div class="ttdef"><b>Definition:</b> PoleFilter.h:161</div></div>
<div class="ttc" id="aclassIir_1_1PoleFilterBase2_html"><div class="ttname"><a href="classIir_1_1PoleFilterBase2.html">Iir::PoleFilterBase2</a></div><div class="ttdef"><b>Definition:</b> PoleFilter.h:63</div></div>
<div class="ttc" id="aclassIir_1_1PoleFilterBase_html"><div class="ttname"><a href="classIir_1_1PoleFilterBase.html">Iir::PoleFilterBase</a></div><div class="ttdef"><b>Definition:</b> PoleFilter.h:95</div></div>
<div class="ttc" id="anamespaceIir_html"><div class="ttname"><a href="namespaceIir.html">Iir</a></div><div class="ttdef"><b>Definition:</b> Biquad.cpp:40</div></div>
<div class="ttc" id="astructIir_1_1ComplexPair_html"><div class="ttname"><a href="structIir_1_1ComplexPair.html">Iir::ComplexPair</a></div><div class="ttdef"><b>Definition:</b> Types.h:48</div></div>
<div class="ttc" id="astructIir_1_1PoleFilter_html"><div class="ttname"><a href="structIir_1_1PoleFilter.html">Iir::PoleFilter</a></div><div class="ttdef"><b>Definition:</b> PoleFilter.h:119</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Fri Jan 6 2023 23:53:32 for iir1 by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
