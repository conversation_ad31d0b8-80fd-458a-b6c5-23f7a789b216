/****************************************************************************
** Meta object code from reading C++ file 'frequencyresponseanalyzer.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.15.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../libs/frequencyresponseanalyzer.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'frequencyresponseanalyzer.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.15.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_FrequencyResponseAnalyzer_t {
    QByteArrayData data[9];
    char stringdata0[137];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_FrequencyResponseAnalyzer_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_FrequencyResponseAnalyzer_t qt_meta_stringdata_FrequencyResponseAnalyzer = {
    {
QT_MOC_LITERAL(0, 0, 25), // "FrequencyResponseAnalyzer"
QT_MOC_LITERAL(1, 26, 27), // "frequencyResponseCalculated"
QT_MOC_LITERAL(2, 54, 0), // ""
QT_MOC_LITERAL(3, 55, 23), // "FrequencyResponseResult"
QT_MOC_LITERAL(4, 79, 6), // "result"
QT_MOC_LITERAL(5, 86, 13), // "splCalculated"
QT_MOC_LITERAL(6, 100, 9), // "SPLResult"
QT_MOC_LITERAL(7, 110, 13), // "errorOccurred"
QT_MOC_LITERAL(8, 124, 12) // "errorMessage"

    },
    "FrequencyResponseAnalyzer\0"
    "frequencyResponseCalculated\0\0"
    "FrequencyResponseResult\0result\0"
    "splCalculated\0SPLResult\0errorOccurred\0"
    "errorMessage"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_FrequencyResponseAnalyzer[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       3,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       3,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   29,    2, 0x06 /* Public */,
       5,    1,   32,    2, 0x06 /* Public */,
       7,    1,   35,    2, 0x06 /* Public */,

 // signals: parameters
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void, 0x80000000 | 6,    4,
    QMetaType::Void, QMetaType::QString,    8,

       0        // eod
};

void FrequencyResponseAnalyzer::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<FrequencyResponseAnalyzer *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->frequencyResponseCalculated((*reinterpret_cast< const FrequencyResponseResult(*)>(_a[1]))); break;
        case 1: _t->splCalculated((*reinterpret_cast< const SPLResult(*)>(_a[1]))); break;
        case 2: _t->errorOccurred((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (FrequencyResponseAnalyzer::*)(const FrequencyResponseResult & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&FrequencyResponseAnalyzer::frequencyResponseCalculated)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (FrequencyResponseAnalyzer::*)(const SPLResult & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&FrequencyResponseAnalyzer::splCalculated)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (FrequencyResponseAnalyzer::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&FrequencyResponseAnalyzer::errorOccurred)) {
                *result = 2;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject FrequencyResponseAnalyzer::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_FrequencyResponseAnalyzer.data,
    qt_meta_data_FrequencyResponseAnalyzer,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *FrequencyResponseAnalyzer::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *FrequencyResponseAnalyzer::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_FrequencyResponseAnalyzer.stringdata0))
        return static_cast<void*>(this);
    if (!strcmp(_clname, "base_lib"))
        return static_cast< base_lib*>(this);
    return QObject::qt_metacast(_clname);
}

int FrequencyResponseAnalyzer::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 3)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 3;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 3)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 3;
    }
    return _id;
}

// SIGNAL 0
void FrequencyResponseAnalyzer::frequencyResponseCalculated(const FrequencyResponseResult & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void FrequencyResponseAnalyzer::splCalculated(const SPLResult & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void FrequencyResponseAnalyzer::errorOccurred(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
