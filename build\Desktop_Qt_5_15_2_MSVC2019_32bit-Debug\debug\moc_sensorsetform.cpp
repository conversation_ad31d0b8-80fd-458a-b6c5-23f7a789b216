/****************************************************************************
** Meta object code from reading C++ file 'sensorsetform.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.15.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../MainSet/sensor/sensorsetform.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'sensorsetform.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.15.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_SensorSetForm_t {
    QByteArrayData data[15];
    char stringdata0[190];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_SensorSetForm_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_SensorSetForm_t qt_meta_stringdata_SensorSetForm = {
    {
QT_MOC_LITERAL(0, 0, 13), // "SensorSetForm"
QT_MOC_LITERAL(1, 14, 21), // "on_saveButton_clicked"
QT_MOC_LITERAL(2, 36, 0), // ""
QT_MOC_LITERAL(3, 37, 21), // "on_exitButton_clicked"
QT_MOC_LITERAL(4, 59, 15), // "showContextMenu"
QT_MOC_LITERAL(5, 75, 3), // "pos"
QT_MOC_LITERAL(6, 79, 19), // "onCellDoubleClicked"
QT_MOC_LITERAL(7, 99, 3), // "row"
QT_MOC_LITERAL(8, 103, 6), // "column"
QT_MOC_LITERAL(9, 110, 13), // "onCellChanged"
QT_MOC_LITERAL(10, 124, 26), // "updateSensitivity_Slot_Mic"
QT_MOC_LITERAL(11, 151, 6), // "sensor"
QT_MOC_LITERAL(12, 158, 13), // "Result_mVPa__"
QT_MOC_LITERAL(13, 172, 11), // "Result_dB__"
QT_MOC_LITERAL(14, 184, 5) // "FRE__"

    },
    "SensorSetForm\0on_saveButton_clicked\0"
    "\0on_exitButton_clicked\0showContextMenu\0"
    "pos\0onCellDoubleClicked\0row\0column\0"
    "onCellChanged\0updateSensitivity_Slot_Mic\0"
    "sensor\0Result_mVPa__\0Result_dB__\0FRE__"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_SensorSetForm[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       6,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    0,   44,    2, 0x08 /* Private */,
       3,    0,   45,    2, 0x08 /* Private */,
       4,    1,   46,    2, 0x08 /* Private */,
       6,    2,   49,    2, 0x08 /* Private */,
       9,    2,   54,    2, 0x08 /* Private */,
      10,    4,   59,    2, 0x08 /* Private */,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QPoint,    5,
    QMetaType::Void, QMetaType::Int, QMetaType::Int,    7,    8,
    QMetaType::Void, QMetaType::Int, QMetaType::Int,    7,    8,
    QMetaType::Void, QMetaType::QString, QMetaType::Double, QMetaType::Double, QMetaType::Double,   11,   12,   13,   14,

       0        // eod
};

void SensorSetForm::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<SensorSetForm *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->on_saveButton_clicked(); break;
        case 1: _t->on_exitButton_clicked(); break;
        case 2: _t->showContextMenu((*reinterpret_cast< const QPoint(*)>(_a[1]))); break;
        case 3: _t->onCellDoubleClicked((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 4: _t->onCellChanged((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 5: _t->updateSensitivity_Slot_Mic((*reinterpret_cast< QString(*)>(_a[1])),(*reinterpret_cast< double(*)>(_a[2])),(*reinterpret_cast< double(*)>(_a[3])),(*reinterpret_cast< double(*)>(_a[4]))); break;
        default: ;
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject SensorSetForm::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_meta_stringdata_SensorSetForm.data,
    qt_meta_data_SensorSetForm,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *SensorSetForm::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *SensorSetForm::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_SensorSetForm.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int SensorSetForm::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 6)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 6;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 6)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 6;
    }
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
