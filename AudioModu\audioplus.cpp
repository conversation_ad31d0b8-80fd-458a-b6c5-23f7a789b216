﻿#include "audioplus.h"  // 包含 AudioPlus 头文件，定义类和接口

#define FORMAT RTAUDIO_FLOAT32  // 定义音频数据格式为 32 位浮点数
#define SCALE 1.0f  // 定义音频数据缩放因子（归一化）
#define PI 3.14159265358979323846  // 定义 π 常量，用于正弦波和扫频计算

AudioPlus::AudioPlus(QObject *parent)  // 构造函数，接收父对象指针
    : QObject(parent),  // 初始化基类 QObject，支持信号槽
      bufferFrames_(512),  // 设置音频缓冲区帧数为 512（约 11.6ms，44100 Hz）
      useBuffer1_(true),  // 初始化显示缓冲区标志为 true（使用 displayBuffer1_）
      displayPoints_(static_cast<unsigned int>(44100 * 0.2)),  // 设置显示点数（8820 样本，0.2 秒）
      configManager_("config/Channel_Info.json"),  // 初始化通道配置管理器，加载 JSON 文件
      displayTimer(new QTimer(this)) {  // 创建定时器，绑定到当前对象
    if (!configManager_.loadFromFile()) {  // 尝试加载通道配置文件
        qDebug() << "Failed to load channel configurations";  // 如果加载失败，输出调试信息
    }
    connect(displayTimer, &QTimer::timeout, this, &AudioPlus::updateAllDisplayBuffers);  // 连接定时器超时信号到更新函数
    displayTimer->start(100);  // 启动定时器，每 100ms 触发一次
}
AudioPlus::~AudioPlus() {  // 析构函数，释放资源
    stopStream({}, {});  // 停止所有音频流，释放设备资源
    if (displayTimer->isActive()) {  // 检查定时器是否正在运行
        QMetaObject::invokeMethod(displayTimer, "stop", Qt::QueuedConnection);  // 在主线程异步停止定时器
    }
    displayTimer->deleteLater();  // 在主线程异步删除定时器
    asioHosts.clear();  // 清空 ASIO 主机映射，释放实例
    wasapiHosts.clear();  // 清空 WASAPI 主机映射，释放实例
}

//bool AudioPlus::initialize(RtAudio::Api api) {  // 初始化音频设备函数，指定音频 API
//    if (api == RtAudio::WINDOWS_ASIO) {  // 如果指定的是 ASIO API
//        if (asioRtAudio.getDeviceCount() == 0) {  // 检查 ASIO 设备数量
//            qDebug() << "No ASIO devices found";  // 如果无设备，输出调试信息
//            return false;  // 返回初始化失败
//        }
//        return true;  // ASIO 设备存在，返回成功
//    } else if (api == RtAudio::WINDOWS_WASAPI) {  // 如果指定的是 WASAPI API
//        if (wasapiRtAudio.getDeviceCount() == 0) {  // 检查 WASAPI 设备数量
//            qDebug() << "No WASAPI devices found";  // 如果无设备，输出调试信息
//            return false;  // 返回初始化失败
//        }
//        return true;  // WASAPI 设备存在，返回成功
//    }
//    qDebug() << "Unsupported audio API";  // 如果 API 不支持，输出调试信息
bool AudioPlus::addChannels(const QStringList &inputChannels, const QStringList &outputChannels,  // 添加输入/输出通道函数
                            unsigned int sampleRate, QString &errorMessage) {  // 参数：输入/输出通道列表、采样率、错误信息
    QMutexLocker locker(&mutex_);  // 加锁，保护 hosts 数据结构，确保线程安全

    QMap<AudioChannelConfigManager::ChannelType, QMap<QString, AudioChannalInfoConfig>> inputs, outputs;  // 定义输入/输出配置映射

    for (const QString &channelName : inputChannels) {  // 遍历输入通道列表
        if (!configManager_.audioInputs[AudioChannelConfigManager::ChannelType::isInput].contains(channelName)) {  // 检查通道是否在配置中
            errorMessage = QString("Input channel %1 not found").arg(channelName);  // 设置错误信息
            return false;  // 返回失败
        }
        inputs[AudioChannelConfigManager::ChannelType::isInput][channelName] = configManager_.audioInputs[AudioChannelConfigManager::ChannelType::isInput][channelName];  // 添加通道配置到 inputs
    }
    for (const QString &channelName : outputChannels) {  // 遍历输出通道列表
        if (!configManager_.audioOutputs[AudioChannelConfigManager::ChannelType::isOutput].contains(channelName)) {  // 检查通道是否在配置中
            errorMessage = QString("Output channel %1 not found").arg(channelName);  // 设置错误信息
            return false;  // 返回失败
        }
        outputs[AudioChannelConfigManager::ChannelType::isOutput][channelName] = configManager_.audioOutputs[AudioChannelConfigManager::ChannelType::isOutput][channelName];  // 添加通道配置到 outputs
    }

    if (!validateChannels(inputs, outputs, sampleRate, errorMessage)) {  // 验证通道配置是否有效
        return false;  // 如果验证失败，返回 false
    }

    for (const auto &channelName : inputChannels) {  // 遍历输入通道列表
        const auto &config = inputs[AudioChannelConfigManager::ChannelType::isInput][channelName];  // 获取通道配置
        QString driverType = config.Driver_Type == "WDM" ? "WASAPI" : config.Driver_Type;  // 将 WDM 转换为 WASAPI
        QString key = QString("%1_%2_%3").arg(config.Driver_Name).arg(driverType).arg(config.DriverID);  // 生成设备键
        if (driverType == "ASIO") {  // 如果是 ASIO 声卡
            if (!asioHosts.contains(key)) {  // 如果主机映射中无该设备
                asioHosts[key] = QSharedPointer<AsioHost>(new AsioHost(key, this));  // 创建新 AsioHost
            }
            if (!asioHosts[key]->addChannel(channelName, isInput, config, sampleRate, errorMessage)) {  // 添加通道
                return false;  // 如果添加失败，返回 false
            }
            displayBuffer1_[isInput][channelName].resize(displayPoints_, 0.0);  // 初始化显示缓冲区 1
            displayBuffer2_[isInput][channelName].resize(displayPoints_, 0.0);  // 初始化显示缓冲区 2
        } else {  // 如果是 WASAPI 声卡
            if (!wasapiHosts.contains(key)) {  // 如果主机映射中无该设备
                wasapiHosts[key] = QSharedPointer<WasapiHost>(new WasapiHost(key, this));  // 创建新 WasapiHost
            }
            if (!wasapiHosts[key]->addChannel(channelName, isInput, config, sampleRate, errorMessage)) {  // 添加通道
                return false;  // 如果添加失败，返回 false
            }
            displayBuffer1_[isInput][channelName].resize(displayPoints_, 0.0);  // 初始化显示缓冲区 1
            displayBuffer2_[isInput][channelName].resize(displayPoints_, 0.0);  // 初始化显示缓冲区 2
        }
    }

    for (const auto &channelName : outputChannels) {  // 遍历输出通道列表
        const auto &config = outputs[AudioChannelConfigManager::ChannelType::isOutput][channelName];  // 获取通道配置
        QString driverType = config.Driver_Type == "WDM" ? "WASAPI" : config.Driver_Type;  // 将 WDM 转换为 WASAPI
        QString key = QString("%1_%2_%3").arg(config.Driver_Name).arg(driverType).arg(config.DriverID);  // 生成设备键
        if (driverType == "ASIO") {  // 如果是 ASIO 声卡
            if (!asioHosts.contains(key)) {  // 如果主机映射中无该设备
                asioHosts[key] = QSharedPointer<AsioHost>(new AsioHost(key, this));  // 创建新 AsioHost
            }
            if (!asioHosts[key]->addChannel(channelName, isOutput, config, sampleRate, errorMessage)) {  // 添加通道
                return false;  // 如果添加失败，返回 false
            }
            displayBuffer1_[isOutput][channelName].resize(displayPoints_, 0.0);  // 初始化显示缓冲区 1
            displayBuffer2_[isOutput][channelName].resize(displayPoints_, 0.0);  // 初始化显示缓冲区 2
        } else {  // 如果是 WASAPI 声卡
            if (!wasapiHosts.contains(key)) {  // 如果主机映射中无该设备
                wasapiHosts[key] = QSharedPointer<WasapiHost>(new WasapiHost(key, this));  // 创建新 WasapiHost
            }
            if (!wasapiHosts[key]->addChannel(channelName, isOutput, config, sampleRate, errorMessage)) {  // 添加通道
                return false;  // 如果添加失败，返回 false
            }
            displayBuffer1_[isOutput][channelName].resize(displayPoints_, 0.0);  // 初始化显示缓冲区 1
            displayBuffer2_[isOutput][channelName].resize(displayPoints_, 0.0);  // 初始化显示缓冲区 2
        }
    }

    return true;  // 返回成功
}
bool AudioPlus::validateChannels(const QMap<AudioChannelConfigManager::ChannelType, QMap<QString, AudioChannalInfoConfig>> &inputs,  // 验证通道函数
                                 const QMap<AudioChannelConfigManager::ChannelType, QMap<QString, AudioChannalInfoConfig>> &outputs,
                                 unsigned int sampleRate, QString &errorMessage) {
    QSet<QString> deviceKeys;  // 定义设备键集合
    for (const auto &channelMap : inputs.value(AudioChannelConfigManager::ChannelType::isInput)) {  // 遍历输入通道
        const auto &config = channelMap;  // 获取通道配置
        QString driverType = config.Driver_Type == "WDM" ? "WASAPI" : config.Driver_Type;  // 将 WDM 转换为 WASAPI
        QString key = QString("%1_%2_%3").arg(config.Driver_Name).arg(driverType).arg(config.DriverID);  // 生成设备键
        deviceKeys.insert(key);  // 添加设备键
    }
    for (const auto &channelMap : outputs.value(AudioChannelConfigManager::ChannelType::isOutput)) {  // 遍历输出通道
        const auto &config = channelMap;  // 获取通道配置
        QString driverType = config.Driver_Type == "WDM" ? "WASAPI" : config.Driver_Type;  // 将 WDM 转换为 WASAPI
        QString key = QString("%1_%2_%3").arg(config.Driver_Name).arg(driverType).arg(config.DriverID);  // 生成设备键
        deviceKeys.insert(key);  // 添加设备键
    }

    for (const QString &key : deviceKeys) {  // 遍历设备键
        bool hasInput = false, hasOutput = false;  // 标记是否有输入/输出通道
        for (const auto &channelMap : inputs.value(AudioChannelConfigManager::ChannelType::isInput)) {  // 检查输入通道
            const auto &config = channelMap;  // 获取通道配置
            QString driverType = config.Driver_Type == "WDM" ? "WASAPI" : config.Driver_Type;  // 将 WDM 转换为 WASAPI
            if (key == QString("%1_%2_%3").arg(config.Driver_Name).arg(driverType).arg(config.DriverID)) {  // 匹配设备键
                hasInput = true;  // 标记有输入通道
            }
        }
        for (const auto &channelMap : outputs.value(AudioChannelConfigManager::ChannelType::isOutput)) {  // 检查输出通道
            const auto &config = channelMap;  // 获取通道配置
            QString driverType = config.Driver_Type == "WDM" ? "WASAPI" : config.Driver_Type;  // 将 столица转换为 WASAPI
            if (key == QString("%1_%2_%3").arg(config.Driver_Name).arg(driverType).arg(config.DriverID)) {  // 匹配设备键
                hasOutput = true;  // 标记有输出通道
            }
        }
        if (!hasInput && !hasOutput) {  // 如果无输入和输出通道
            errorMessage = QString("No valid channels for device %1").arg(key);  // 设置错误信息
            return false;  // 返回失败
        }
    }

    return true;  // 返回成功
}

bool AudioPlus::startStream(const QStringList &inputChannels, const QStringList &outputChannels,  // 启动音频流函数
                            unsigned int sampleRate, QString &errorMessage) {  // 参数：输入/输出通道列表、采样率、错误信息
    QMutexLocker locker(&mutex_);  // 加锁，保护 hosts 数据结构，确保线程安全

    QMap<QString, unsigned int> deviceSampleRates;  // 定义设备采样率映射
    for (const auto &channelName : inputChannels) {  // 遍历输入通道列表
        if (!configManager_.audioInputs[AudioChannelConfigManager::ChannelType::isInput].contains(channelName)) {  // 检查通道是否在配置中
            errorMessage = QString("Input channel %1 not found").arg(channelName);  // 设置错误信息
            return false;  // 返回失败
        }
        auto config = configManager_.audioInputs[AudioChannelConfigManager::ChannelType::isInput][channelName];  // 获取通道配置
        QString driverType = config.Driver_Type == "WDM" ? "WASAPI" : config.Driver_Type;  // 将 WDM 转换为 WASAPI
        QString key = QString("%1_%2_%3").arg(config.Driver_Name).arg(driverType).arg(config.DriverID);  // 生成设备键
        if (!(driverType == "ASIO" ? asioHosts.contains(key) : wasapiHosts.contains(key))) {  // 检查主机是否存在
            errorMessage = QString("Device %1 not found for input channel %2").arg(key).arg(channelName);  // 设置错误信息
            return false;  // 返回失败
        }
        deviceSampleRates[key] = sampleRate;  // 设置设备采样率
    }
    for (const auto &channelName : outputChannels) {  // 遍历输出通道列表
        if (!configManager_.audioOutputs[AudioChannelConfigManager::ChannelType::isOutput].contains(channelName)) {  // 检查通道是否在配置中
            errorMessage = QString("Output channel %1 not found").arg(channelName);  // 设置错误信息
            return false;  // 返回失败
        }
        auto config = configManager_.audioOutputs[AudioChannelConfigManager::ChannelType::isOutput][channelName];  // 获取通道配置
        QString driverType = config.Driver_Type == "WDM" ? "WASAPI" : config.Driver_Type;  // 将 WDM 转换为 WASAPI
        QString key = QString("%1_%2_%3").arg(config.Driver_Name).arg(driverType).arg(config.DriverID);  // 生成设备键
        if (!(driverType == "ASIO" ? asioHosts.contains(key) : wasapiHosts.contains(key))) {  // 检查主机是否存在
            errorMessage = QString("Device %1 not found for output channel %2").arg(key).arg(channelName);  // 设置错误信息
            return false;  // 返回失败
        }
        deviceSampleRates[key] = sampleRate;  // 设置设备采样率
    }

    QSet<QString> deviceKeys = deviceSampleRates.keys().toSet();  // 获取所有设备键集合
    for (const QString &key : deviceKeys) {  // 遍历所有设备键
        if (asioHosts.contains(key)) {  // 如果是 ASIO 声卡
            if (!asioHosts[key]->startStream(sampleRate, errorMessage)) {  // 启动流
                return false;  // 如果启动失败，返回 false
            }
        } else if (wasapiHosts.contains(key)) {  // 如果是 WASAPI 声卡
            if (!wasapiHosts[key]->startStream(sampleRate, errorMessage)) {  // 启动流
                return false;  // 如果启动失败，返回 false
            }
        } else {  // 如果主机不存在
            errorMessage = QString("No host instance for device %1").arg(key);  // 设置错误信息
            return false;  // 返回失败
        }
    }

    return true;  // 返回成功
}
void AudioPlus::stopStream(const QStringList &inputChannels, const QStringList &outputChannels) {  // 停止音频流函数
    QMutexLocker locker(&mutex_);  // 加锁，保护 hosts

    QSet<QString> devicesToStop;  // 定义要停止的设备键集合
    for (const auto &channelName : inputChannels) {  // 遍历输入通道
        for (auto it = asioHosts.constBegin(); it != asioHosts.constEnd(); ++it) {  // 遍历 ASIO 主机
            for (const auto &ch : it.value()->getStream().inputChannels) {  // 遍历输入通道
                if (ch->deviceName == channelName && ch->active) {  // 如果通道名称匹配且活跃
                    ch->active = false;  // 标记通道为非活跃状态
                    it.value()->getStream().activeChannelCount--;  // 减少活跃通道计数
                    devicesToStop.insert(it.key());  // 添加设备键到停止集合
                }
            }
        }
        for (auto it = wasapiHosts.constBegin(); it != wasapiHosts.constEnd(); ++it) {  // 遍历 WASAPI 主机
            for (const auto &ch : it.value()->getStream().inputChannels) {  // 遍历输入通道
                if (ch->deviceName == channelName && ch->active) {  // 如果通道名称匹配且活跃
                    ch->active = false;  // 标记通道为非活跃状态
                    it.value()->getStream().activeChannelCount--;  // 减少活跃通道计数
                    devicesToStop.insert(it.key());  // 添加设备键到停止集合
                }
            }
        }
    }
    for (const auto &channelName : outputChannels) {  // 遍历输出通道
        for (auto it = asioHosts.constBegin(); it != asioHosts.constEnd(); ++it) {  // 遍历 ASIO 主机
            for (const auto &ch : it.value()->getStream().outputChannels) {  // 遍历输出通道
                if (ch->deviceName == channelName && ch->active) {  // 如果通道名称匹配且活跃
                    ch->active = false;  // 标记通道为非活跃状态
                    it.value()->getStream().activeChannelCount--;  // 减少活跃通道计数
                    devicesToStop.insert(it.key());  // 添加设备键到停止集合
                }
            }
        }
        for (auto it = wasapiHosts.constBegin(); it != wasapiHosts.constEnd(); ++it) {  // 遍历 WASAPI 主机
            for (const auto &ch : it.value()->getStream().outputChannels) {  // 遍历输出通道
                if (ch->deviceName == channelName && ch->active) {  // 如果通道名称匹配且活跃
                    ch->active = false;  // 标记通道为非活跃状态
                    it.value()->getStream().activeChannelCount--;  // 减少活跃通道计数
                    devicesToStop.insert(it.key());  // 添加设备键到停止集合
                }
            }
        }
    }

    for (const QString &key : devicesToStop) {  // 遍历要停止的设备键
        if (asioHosts.contains(key)) {  // 如果是 ASIO 声卡
            if (asioHosts[key]->getStream().activeChannelCount == 0) {  // 如果无活跃通道
                asioHosts[key]->stopStream();  // 停止流
                asioHosts.remove(key);  // 移除主机实例
            }
        } else if (wasapiHosts.contains(key)) {  // 如果是 WASAPI 声卡
            if (wasapiHosts[key]->getStream().activeChannelCount == 0) {  // 如果无活跃通道
                wasapiHosts[key]->stopStream();  // 停止流
                wasapiHosts.remove(key);  // 移除主机实例
            }
        }
    }
}
bool AudioPlus::configureSignal(const QString &channelName, SignalType type, const QVariantMap &params) {  // 配置信号函数
    QMutexLocker locker(&mutex_);  // 加锁，保护 hosts 数据结构，确保线程安全

    bool isInput = configManager_.audioInputs[AudioChannelConfigManager::ChannelType::isInput].contains(channelName);  // 检查是否为输入通道
    bool isOutput = configManager_.audioOutputs[AudioChannelConfigManager::ChannelType::isOutput].contains(channelName);  // 检查是否为输出通道
    if (!isInput && !isOutput) {  // 如果通道不存在
        qDebug() << "Channel" << channelName << "not found";  // 输出调试信息
        return false;  // 返回失败
    }
    if (isInput) {  // 如果是输入通道
        qDebug() << "Cannot configure signal for input channel" << channelName;  // 输出调试信息
        return false;  // 输入通道不支持信号配置
    }

    QString driverType, driverName;
    unsigned int driverID;
    if (isOutput) {  // 如果是输出通道
        auto config = configManager_.audioOutputs[AudioChannelConfigManager::ChannelType::isOutput][channelName];  // 获取通道配置
        driverType = config.Driver_Type == "WDM" ? "WASAPI" : config.Driver_Type;  // 将 WDM 转换为 WASAPI
        driverName = config.Driver_Name;  // 获取驱动名称
        driverID = config.DriverID;  // 获取设备 ID
    }

    QString key = QString("%1_%2_%3").arg(driverName).arg(driverType).arg(driverID);  // 生成设备键
    QSharedPointer<AsioHost> asioHost = asioHosts.value(key);  // 获取 ASIO 主机
    QSharedPointer<WasapiHost> wasapiHost = wasapiHosts.value(key);  // 获取 WASAPI 主机
    if (!asioHost && !wasapiHost) {  // 如果主机不存在
        qDebug() << "No host found for device" << key;  // 输出调试信息
        return false;  // 返回失败
    }

    DeviceStream *stream = asioHost ? &asioHost->getStream() : &wasapiHost->getStream();  // 获取设备流
    for (auto &channel : stream->outputChannels) {  // 遍历输出通道
        if (channel->deviceName == channelName) {  // 如果通道名称匹配
            channel->signalType = type;  // 设置信号类型
            channel->validSamples = 0;  // 重置有效样本数
            channel->tempDataIndex = 0;  // 重置临时数据索引
            channel->tempData.clear();  // 清空临时数据

            if (type == SignalType::Sine) {  // 如果是正弦波
                double frequency = params.value("frequency", 440.0).toDouble();  // 获取频率（默认 440 Hz）
                double amplitude = params.value("amplitude", 1.0).toDouble();  // 获取幅度（默认 1.0）
                int samples = channel->sampleRate / frequency;  // 计算一个周期的样本数
                channel->sineWaveData.resize(samples);  // 调整正弦波数据大小
                for (int i = 0; i < samples; ++i) {  // 生成正弦波数据
                    channel->sineWaveData[i] = amplitude * std::sin(2.0 *PI * i / samples);  // 计算正弦值
                }
                channel->sampleOffset = 0;  // 重置样本偏移
            } else if (type == SignalType::Sweep) {  // 如果是扫频
                double startFreq = params.value("startFreq", 20.0).toDouble();  // 获取起始频率
                double endFreq = params.value("endFreq", 20000.0).toDouble();  // 获取结束频率
                double duration = params.value("duration", 10.0).toDouble();  // 获取持续时间
                channel->validSamples = static_cast<unsigned int>(duration * channel->sampleRate);  // 计算总样本数
                channel->totalSamples = channel->validSamples;  // 设置总样本数
                channel->tempData.resize(channel->validSamples);  // 调整临时数据大小
                for (unsigned int i = 0; i < channel->validSamples; ++i) {  // 生成扫频数据
                    double t = static_cast<double>(i) / channel->sampleRate;  // 当前时间
                    double freq = startFreq + (endFreq - startFreq) * t / duration;  // 当前频率
                    channel->tempData[i] = std::sin(2.0 * PI * freq * t);  // 计算扫频值
                }
            } else if (type == SignalType::Noise) {  // 如果是噪声
                double amplitude = params.value("amplitude", 1.0).toDouble();  // 获取幅度
                channel->validSamples = channel->sampleRate * 10;  // 设置 10 秒噪声
                channel->totalSamples = channel->validSamples;  // 设置总样本数
                channel->tempData.resize(channel->validSamples);  // 调整临时数据大小
                for (unsigned int i = 0; i < channel->validSamples; ++i) {  // 生成随机噪声
                    channel->tempData[i] = amplitude * (2.0 * (rand() / static_cast<double>(RAND_MAX)) - 1.0);  // 随机值
                }
            } else if (type == SignalType::Wav) {  // 如果是 WAV 文件
                qDebug() << "WAV signal configuration not implemented";  // 输出调试信息
                return false;  // WAV 未实现
            }
            channel->active = true;  // 标记通道为活跃状态
            qDebug() << "Configured signal for channel" << channelName << "type" << type;  // 输出调试信息
            return true;  // 返回成功
        }
    }

    qDebug() << "Channel" << channelName << "not found in device" << key;  // 输出调试信息
    return false;  // 返回失败
}
/*
bool AudioPlus::prepareOutputData(QSharedPointer<ChannelInfo> channel) {  // 准备输出数据函数
    if (!channel) return false;  // 如果通道指针为空，返回失败

    channel->sineWaveData.clear();  // 清空正弦波数据容器
    channel->tempData.clear();  // 清空临时数据容器
    channel->validSamples = 0;  // 重置有效样本数
    channel->sampleOffset = 0;  // 重置正弦波样本偏移
    channel->tempDataIndex = 0;  // 重置临时数据索引
    channel->totalSamples = 0;  // 重置总样本数

    if (channel->signalType == SignalType::Sine) {  // 如果信号类型是正弦波
        double frequency = channel->signalParams.value("frequency", 440.0).toDouble();  // 获取频率，默认 440 Hz
        double amplitude = channel->signalParams.value("amplitude", 0.5).toDouble();  // 获取振幅，默认 0.5
        unsigned int periodSamples = static_cast<unsigned int>(channel->sampleRate / frequency);  // 计算周期样本数
        if (periodSamples == 0) periodSamples = 1;  // 确保周期样本数至少为 1
        channel->sineWaveData.resize(periodSamples);  // 调整正弦波数据容器大小
        for (unsigned int i = 0; i < periodSamples; ++i) {  // 遍历周期样本
            channel->sineWaveData[i] = amplitude * std::sin(2.0 * PI * i / periodSamples);  // 生成正弦波数据
        }
        return true;  // 返回成功
    } else if (channel->signalType == SignalType::Sweep) {  // 如果信号类型是扫频
        double startFreq = channel->signalParams.value("startFrequency", 20.0).toDouble();  // 获取起始频率，默认 20 Hz
        double endFreq = channel->signalParams.value("endFrequency", 20000.0).toDouble();  // 获取结束频率，默认 20 kHz
        double amplitude = channel->signalParams.value("amplitude", 0.5).toDouble();  // 获取振幅，默认 0.5
        unsigned int step = 10;  // 设置频率步长为 10 Hz
        unsigned int totalSamples = 0;  // 初始化总样本数
        std::vector<float> data;  // 创建数据容器

        for (double freq = startFreq; freq <= endFreq; freq += step) {  // 遍历频率范围
            unsigned int samplesPerFreq = static_cast<unsigned int>(channel->sampleRate / freq * 2);  // 计算每个频率的样本数
            if (samplesPerFreq == 0) samplesPerFreq = 1;  // 确保样本数至少为 1
            totalSamples += samplesPerFreq;  // 累加总样本数
        }
        totalSamples = std::min(totalSamples, channel->sampleRate * 300);  // 限制最大样本数（300 秒）
        data.resize(totalSamples);  // 调整数据容器大小
        channel->totalSamples = totalSamples;  // 设置总样本数
        channel->validSamples = totalSamples;  // 设置有效样本数

        unsigned int sampleIndex = 0;  // 初始化样本索引
        double phase = 0.0;  // 初始化相位
        for (double freq = startFreq; freq <= endFreq && sampleIndex < totalSamples; freq += step) {  // 遍历频率
            unsigned int samplesPerFreq = std::min(  // 计算当前频率的样本数
                static_cast<unsigned int>(channel->sampleRate / freq * 2),  // 2 个周期
                totalSamples - sampleIndex  // 剩余样本
            );
            for (unsigned int i = 0; i < samplesPerFreq; ++i) {  // 遍历样本
                data[sampleIndex] = amplitude * std::sin(phase);  // 生成扫频数据
                phase += 2.0 * PI * freq / channel->sampleRate;  // 更新相位
                sampleIndex++;  // 增加样本索引
            }
        }

        channel->tempData = std::move(data);  // 将数据移动到临时数据容器
        return true;  // 返回成功
    } else if (channel->signalType == SignalType::Wav) {  // 如果信号类型是 WAV
        std::string filePath = channel->signalParams.value("filePath", "").toString().toStdString();  // 获取 WAV 文件路径
        unsigned int channels, wavSampleRate, totalSamples;  // 定义通道数、采样率、总样本数
        std::vector<float> wavData;  // 创建 WAV 数据容器
        drwav wav;  // 定义 WAV 文件对象
        if (!drwav_init_file(&wav, filePath.c_str(), nullptr)) {  // 尝试初始化 WAV 文件
            return false;  // 如果初始化失败，返回失败
        }
        channels = wav.channels;  // 获取通道数
        wavSampleRate = wav.sampleRate;  // 获取采样率
        totalSamples = static_cast<unsigned int>(wav.totalPCMFrameCount);  // 获取总样本数
        totalSamples = std::min(totalSamples, channel->sampleRate * 300);  // 限制最大样本数
        wavData.resize(totalSamples);  // 调整数据容器大小
        drwav_read_pcm_frames_f32(&wav, totalSamples, wavData.data());  // 读取 WAV 数据
        drwav_uninit(&wav);  // 释放 WAV 文件资源

        if (channels == 1 && wavSampleRate == channel->sampleRate) {  // 确保单通道且采样率匹配
            channel->tempData = std::move(wavData);  // 将数据移动到临时数据容器
            channel->totalSamples = totalSamples;  // 设置总样本数
            channel->validSamples = totalSamples;  // 设置有效样本数
            return true;  // 返回成功
        }
        return false;  // 返回失败
    } else if (channel->signalType == SignalType::WhiteNoise || channel->signalType == SignalType::PinkNoise) {  // 如果信号类型是噪声
        double amplitude = channel->signalParams.value("amplitude", 0.5).toDouble();  // 获取振幅，默认 0.5
        double duration = channel->signalParams.value("duration", 10.0).toDouble();  // 获取时长，默认 10 秒
        unsigned int totalSamples = static_cast<unsigned int>(channel->sampleRate * duration);  // 计算总样本数
        totalSamples = std::min(totalSamples, channel->sampleRate * 300);  // 限制最大样本数
        std::vector<float> data(totalSamples);  // 创建数据容器
        std::random_device rd;  // 创建随机设备
        std::mt19937 gen(rd());  // 创建随机数生成器
        std::uniform_real_distribution<float> dis(-1.0f, 1.0f);  // 创建均匀分布（-1, 1）

        if (channel->signalType == SignalType::WhiteNoise) {  // 如果是白噪声
            for (unsigned int i = 0; i < totalSamples; ++i) {  // 遍历样本
                data[i] = amplitude * dis(gen);  // 生成白噪声数据
            }
        } else {  // 如果是粉噪声
            float prev = 0.0f;  // 初始化前值
            for (unsigned int i = 0; i < totalSamples; ++i) {  // 遍历样本
                float white = dis(gen);  // 生成白噪声
                data[i] = amplitude * (0.5f * prev + 0.5f * white);  // 生成粉噪声（简化算法）
                prev = data[i];  // 更新前值
            }
        }

        channel->tempData = std::move(data);  // 将数据移动到临时数据容器
        channel->totalSamples = totalSamples;  // 设置总样本数
        channel->validSamples = totalSamples;  // 设置有效样本数
        return true;  // 返回成功
    }

    return false;  // 返回失败（未知信号类型）
}

*/


bool AudioPlus::getDisplayData(ChannelType type, const QString &channelName, std::vector<double> &data) {  // 获取显示数据函数
    QMutexLocker locker(&displayMutex);  // 加锁，保护显示缓冲区

    if (!displayBuffer1_[type].contains(channelName)) {  // 检查通道是否在缓冲区中
        qDebug() << "Channel" << channelName << "not found in display buffer";  // 输出调试信息
        return false;  // 返回失败
    }

    const auto &buffer = useBuffer1_ ? displayBuffer1_[type][channelName] : displayBuffer2_[type][channelName];  // 选择当前缓冲区
    data.clear();  // 清空输出数据
    data.resize(buffer.size());  // 调整输出数据大小
    std::copy(buffer.begin(), buffer.end(), data.begin());  // 复制并转换 float 到 double

    return true;  // 返回成功
}

bool AudioPlus::isDeviceRunning(const QString &deviceName) const {  // 检查设备是否运行函数
    //QMutexLocker locker(&mutex_);  // 加锁，保护 hosts

    for (auto it = asioHosts.constBegin(); it != asioHosts.constEnd(); ++it) {  // 遍历 ASIO 主机
        if (it.key().contains(deviceName) && it.value()->isRunning()) {  // 检查设备名称和运行状态
            return true;  // 返回 true
        }
    }
    for (auto it = wasapiHosts.constBegin(); it != wasapiHosts.constEnd(); ++it) {  // 遍历 WASAPI 主机
        if (it.key().contains(deviceName) && it.value()->isRunning()) {  // 检查设备名称和运行状态
            return true;  // 返回 true
        }
    }

    return false;  // 返回 false
}

//void AudioPlus::asioErrorCallback(RtAudioErrorType type, const std::string &errorText) {  // ASIO 错误回调函数
//    QString msg = QString("ASIO error: %1").arg(errorText.c_str());  // 创建错误信息
//    emit errorOccurred(msg);  // 发出错误信号
//    qDebug() << msg;  // 输出调试信息
//}

//void AudioPlus::wasapiErrorCallback(RtAudioErrorType type, const std::string &errorText) {  // WASAPI 错误回调函数
//    QString msg = QString("WASAPI error: %1").arg(errorText.c_str());  // 创建错误信息
//    emit errorOccurred(msg);  // 发出错误信号
//    qDebug() << msg;  // 输出调试信息
//}

QString AudioPlus::apiToString(RtAudio::Api api) {  // 将 RtAudio::Api 转换为字符串函数
    switch (api) {  // 根据 API 类型
    case RtAudio::WINDOWS_ASIO:  // 如果是 ASIO
        return "ASIO";  // 返回字符串 "ASIO"
    case RtAudio::WINDOWS_WASAPI:  // 如果是 WASAPI
        return "WASAPI";  // 返回字符串 "WASAPI"
    default:  // 如果是其他类型
        return "Unknown";  // 返回字符串 "Unknown"
    }
}

//int AudioPlus::asioCallback(void *outputBuffer, void *inputBuffer, unsigned int nFrames,  // ASIO 回调函数
//                            double streamTime, RtAudioStreamStatus status, void *userData) {  // 参数：输入/输出缓冲区、帧数、流时间、状态、用户数据
//    AudioPlus *self = static_cast<AudioPlus*>(userData);  // 将用户数据转换为 AudioPlus 指针
//    QMutexLocker locker(&self->mutex_);  // 加锁，保护 devices_

////    self->timer.start();  // 启动计时器，记录回调执行时间

//    DeviceStream *device = nullptr;  // 初始化设备流指针
//    for (auto it = self->devices_.begin(); it != self->devices_.end(); ++it) {  // 遍历设备流
//        if (it.value().isRunning && it.value().dac == &self->asioRtAudio) {  // 查找运行中的 ASIO 设备
//            device = &it.value();  // 设置设备流指针
//            break;  // 找到后退出循环
//        }
//    }
//    if (!device || !device->dac) {  // 如果设备不存在或无效
//        return 0;  // 返回 0，结束回调
//    }

////    double expectedTime = static_cast<double>(nFrames) / device->sampleRate;  // 计算预期回调间隔（秒）
////    double currentTime = QDateTime::currentMSecsSinceEpoch() / 1000.0;  // 获取当前时间（秒）
////    if (device->lastStreamTime >= 0.0) {  // 如果存在上次流时间
////        double deltaTime = streamTime - device->lastStreamTime;  // 计算时间差
////        if (deltaTime > 2.0 * expectedTime) {  // 如果时间差超过 2 倍预期
////            device->errorCount++;  // 增加错误计数
////            if (currentTime - device->lastErrorTime >= 1.0) {  // 如果距离上次错误超过 1 秒
////                QString msg = QString("ASIO callback disconnected: deltaTime=%1s, expected=%2s")  // 创建断开警告
////                              .arg(deltaTime).arg(expectedTime);
////                emit self->errorOccurred(msg);  // 发出错误信号
////                device->lastErrorTime = currentTime;  // 更新上次错误时间
////                qDebug() << msg;  // 输出调试信息
////            }
////        } else {  // 如果时间差正常
////            device->errorCount = 0;  // 重置错误计数
////        }
////    }
////    device->lastStreamTime = streamTime;  // 更新上次流时间

//    float *in = static_cast<float*>(inputBuffer);  // 转换输入缓冲区指针
//    if (in) {  // 如果输入缓冲区有效
//        for (const auto &channel : device->inputChannels) {  // 遍历输入通道
//            if (channel->active && channel->inputQueue) {  // 如果通道活跃且队列有效
//                std::vector<float> temp(nFrames);  // 创建临时数据容器
//                for (unsigned int i = 0; i < nFrames; ++i) {  // 遍历帧
//                    temp[i] = in[i * device->inputChannels.size() + channel->channelIndex];  // 提取通道数据
//                }
//                if (!channel->inputQueue->enqueue_bulk(temp.data(), nFrames)) {  // 尝试批量入队
////                    if (currentTime - device->lastErrorTime >= 1.0) {  // 如果距离上次错误超过 1 秒
////                        QString msg = QString("Input queue overflow risk: %1").arg(channel->deviceName);  // 创建溢出警告
////                        emit self->errorOccurred(msg);  // 发出错误信号
////                        device->lastErrorTime = currentTime;  // 更新上次错误时间
////                        qDebug() << msg;  // 输出调试信息
////                    }
//                }
//            }
//        }
//    }

//    float *out = static_cast<float*>(outputBuffer);  // 转换输出缓冲区指针
//    if (out) {  // 如果输出缓冲区有效
//        std::memset(out, 0, nFrames * device->outputChannels.size() * sizeof(float));  // 清空输出缓冲区
//        for (auto it = device->outputChannels.begin(); it != device->outputChannels.end(); ) {  // 遍历输出通道
//            auto channel = *it;  // 获取当前通道指针
//            if (!channel->active || !channel->outputQueue) {  // 如果通道不活跃或队列无效
//                ++it;  // 移动迭代器
//                continue;  // 跳过当前通道
//            }

//            std::vector<float> temp(nFrames);  // 创建临时数据容器
//            size_t toRead = 0;  // 初始化读取样本数

//            if (channel->signalType == SignalType::Sine && !channel->sineWaveData.empty()) {  // 如果是正弦波
//                toRead = nFrames;  // 设置读取样本数为帧数
//                for (unsigned int i = 0; i < nFrames; ++i) {  // 遍历帧
//                    unsigned int index = (channel->sampleOffset + i) % channel->sineWaveData.size();  // 计算正弦波索引
//                    temp[i] = channel->sineWaveData[index] * channel->gain;  // 生成正弦波数据
//                }
//                channel->sampleOffset += nFrames;  // 更新样本偏移
//            } else if (channel->validSamples > 0) {  // 如果有有效样本（扫频/WAV/噪声）
//                toRead = std::min(nFrames, channel->validSamples);  // 计算读取样本数
//                for (size_t i = 0; i < toRead; ++i) {  // 遍历样本
//                    if (channel->tempDataIndex < channel->totalSamples) {  // 如果索引有效
//                        temp[i] = channel->tempData[channel->tempDataIndex++] * channel->gain;  // 提取临时数据
//                    } else {  // 如果索引超出
//                        temp[i] = 0.0f;  // 填充 0
//                    }
//                }
//                channel->validSamples -= toRead;  // 减少有效样本数
//                if (channel->validSamples == 0) {  // 如果样本耗尽
//                    channel->active = false;  // 标记通道为非活跃
//                    emit self->playbackFinished(ChannelType::isOutput, channel->deviceName);  // 发出播放完成信号
//                    it = device->outputChannels.erase(it);  // 移除通道
//                    continue;  // 跳过当前通道
//                } else {  // 如果仍有样本
//                    ++it;  // 移动迭代器
//                }
//            } else {  // 如果无有效样本
//                ++it;  // 移动迭代器
//                continue;  // 跳过当前通道
//            }

//            for (size_t i = 0; i < toRead; ++i) {  // 遍历样本
//                out[i * device->outputChannels.size() + channel->channelIndex] = temp[i];  // 填充输出缓冲区
//            }
//            if (!channel->outputQueue->enqueue_bulk(temp.data(), toRead)) {  // 尝试批量入队
////                if (currentTime - device->lastErrorTime >= 1.0) {  // 如果距离上次错误超过 1 秒
////                    QString msg = QString("Output queue overflow risk: %1").arg(channel->deviceName);  // 创建溢出警告
////                    emit self->errorOccurred(msg);  // 发出错误信号
////                    device->lastErrorTime = currentTime;  // 更新上次错误时间
////                    qDebug() << msg;  // 输出调试信息
////                }
//            }
//        }
//    }

////    qint64 elapsedTimeMs = self->timer.elapsed();  // 获取回调执行时间（毫秒）
////    double elapsedTime = elapsedTimeMs / 1000.0;  // 转换为秒
////    double latencyTime = static_cast<double>(device->streamLatency) / device->sampleRate;  // 计算延迟时间
////    if (elapsedTime > 0.5 * latencyTime) {  // 如果执行时间超过 50% 延迟
////        device->errorCount++;  // 增加错误计数
////        if (currentTime - device->lastErrorTime >= 1.0) {  // 如果距离上次错误超过 1 秒
////            QString msg = QString("ASIO callback timeout: elapsed=%1s, latency=%2s")  // 创建超时警告
////                          .arg(elapsedTime).arg(latencyTime);
////            emit self->errorOccurred(msg);  // 发出错误信号
////            device->lastErrorTime = currentTime;  // 更新上次错误时间
////            qDebug() << msg;  // 输出调试信息
////        }
////    } else {  // 如果执行时间正常
////        device->errorCount = 0;  // 重置错误计数
////    }

////    if (device->errorCount >= 5) {  // 如果错误计数达到 5 次
////        QStringList inputChannels, outputChannels;  // 定义输入/输出通道列表
////        for (const auto &ch : device->inputChannels) {  // 遍历输入通道
////            if (ch->active) inputChannels.append(ch->deviceName);  // 添加活跃通道
////        }
////        for (const auto &ch : device->outputChannels) {  // 遍历输出通道
////            if (ch->active) outputChannels.append(ch->deviceName);  // 添加活跃通道
////        }
////        QString errorMessage;  // 定义错误信息
////        self->stopStream(inputChannels, outputChannels);  // 停止流
////        if (!self->startStream(inputChannels, outputChannels, device->sampleRate, errorMessage)) {  // 尝试重启流
////            emit self->errorOccurred("Failed to restart stream: " + errorMessage);  // 如果重启失败，发出错误信号
////        }
////        device->errorCount = 0;  // 重置错误计数
////        qDebug() << "Restarted stream for" << apiToString(device->dac->getCurrentApi());  // 输出调试信息
////    }

//    return 0;  // 返回 0，继续流处理
//}

//int AudioPlus::wasapiCallback(void *outputBuffer, void *inputBuffer, unsigned int nFrames,  // WASAPI 回调函数
//                              double streamTime, RtAudioStreamStatus status, void *userData) {  // 参数：输入/输出缓冲区、帧数、流时间、状态、用户数据
//    AudioPlus *self = static_cast<AudioPlus*>(userData);  // 将用户数据转换为 AudioPlus 指针
//    QMutexLocker locker(&self->mutex_);  // 加锁，保护 devices_

//    self->timer.start();  // 启动计时器，记录回调执行时间

//    DeviceStream *device = nullptr;  // 初始化设备流指针
//    for (auto it = self->devices_.begin(); it != self->devices_.end(); ++it) {  // 遍历设备流
//        if (it.value().isRunning && it.value().dac == &self->wasapiRtAudio) {  // 查找运行中的 WASAPI 设备
//            device = &it.value();  // 设置设备流指针
//            break;  // 找到后退出循环
//        }
//    }
//    if (!device || !device->dac) {  // 如果设备不存在或无效
//        return 0;  // 返回 0，结束回调
//    }

//    double expectedTime = static_cast<double>(nFrames) / device->sampleRate;  // 计算预期回调间隔（秒）
//    double currentTime = QDateTime::currentMSecsSinceEpoch() / 1000.0;  // 获取当前时间（秒）
//    if (device->lastStreamTime >= 0.0) {  // 如果存在上次流时间
//        double deltaTime = streamTime - device->lastStreamTime;  // 计算时间差
//        if (deltaTime > 2.0 * expectedTime) {  // 如果时间差超过 2 倍预期
//            device->errorCount++;  // 增加错误计数
//            if (currentTime - device->lastErrorTime >= 1.0) {  // 如果距离上次错误超过 1 秒
//                QString msg = QString("WASAPI callback disconnected: deltaTime=%1s, expected=%2s")  // 创建断开警告
//                              .arg(deltaTime).arg(expectedTime);
//                emit self->errorOccurred(msg);  // 发出错误信号
//                device->lastErrorTime = currentTime;  // 更新上次错误时间
//                qDebug() << msg;  // 输出调试信息
//            }
//        } else {  // 如果时间差正常
//            device->errorCount = 0;  // 重置错误计数
//        }
//    }
//    device->lastStreamTime = streamTime;  // 更新上次流时间

//    float *in = static_cast<float*>(inputBuffer);  // 转换输入缓冲区指针
//    if (in) {  // 如果输入缓冲区有效
//        for (const auto &channel : device->inputChannels) {  // 遍历输入通道
//            if (channel->active && channel->inputQueue) {  // 如果通道活跃且队列有效
//                std::vector<float> temp(nFrames);  // 创建临时数据容器
//                for (unsigned int i = 0; i < nFrames; ++i) {  // 遍历帧
//                    temp[i] = in[i * device->inputChannels.size() + channel->channelIndex];  // 提取通道数据
//                }
//                if (!channel->inputQueue->enqueue_bulk(temp.data(), nFrames)) {  // 尝试批量入队
//                    if (currentTime - device->lastErrorTime >= 1.0) {  // 如果距离上次错误超过 1 秒
//                        QString msg = QString("Input queue overflow risk: %1").arg(channel->deviceName);  // 创建溢出警告
//                        emit self->errorOccurred(msg);  // 发出错误信号
//                        device->lastErrorTime = currentTime;  // 更新上次错误时间
//                        qDebug() << msg;  // 输出调试信息
//                    }
//                }
//            }
//        }
//    }

//    float *out = static_cast<float*>(outputBuffer);  // 转换输出缓冲区指针
//    if (out) {  // 如果输出缓冲区有效
//        std::memset(out, 0, nFrames * device->outputChannels.size() * sizeof(float));  // 清空输出缓冲区
//        for (auto it = device->outputChannels.begin(); it != device->outputChannels.end(); ) {  // 遍历输出通道
//            auto channel = *it;  // 获取当前通道指针
//            if (!channel->active || !channel->outputQueue) {  // 如果通道不活跃或队列无效
//                ++it;  // 移动迭代器
//                continue;  // 跳过当前通道
//            }

//            std::vector<float> temp(nFrames);  // 创建临时数据容器
//            size_t toRead = 0;  // 初始化读取样本数

//            if (channel->signalType == SignalType::Sine && !channel->sineWaveData.empty()) {  // 如果是正弦波
//                toRead = nFrames;  // 设置读取样本数为帧数
//                for (unsigned int i = 0; i < nFrames; ++i) {  // 遍历帧
//                    unsigned int index = (channel->sampleOffset + i) % channel->sineWaveData.size();  // 计算正弦波索引
//                    temp[i] = channel->sineWaveData[index] * channel->gain;  // 生成正弦波数据
//                }
//                channel->sampleOffset += nFrames;  // 更新样本偏移
//            } else if (channel->validSamples > 0) {  // 如果有有效样本（扫频/WAV/噪声）
//                toRead = std::min(nFrames, channel->validSamples);  // 计算读取样本数
//                for (size_t i = 0; i < toRead; ++i) {  // 遍历样本
//                    if (channel->tempDataIndex < channel->totalSamples) {  // 如果索引有效
//                        temp[i] = channel->tempData[channel->tempDataIndex++] * channel->gain;  // 提取临时数据
//                    } else {  // 如果索引超出
//                        temp[i] = 0.0f;  // 填充 0
//                    }
//                }
//                channel->validSamples -= toRead;  // 减少有效样本数
//                if (channel->validSamples == 0) {  // 如果样本耗尽
//                    channel->active = false;  // 标记通道为非活跃
//                    emit self->playbackFinished(ChannelType::isOutput, channel->deviceName);  // 发出播放完成信号
//                    it = device->outputChannels.erase(it);  // 移除通道
//                    continue;  // 跳过当前通道
//                } else {  // 如果仍有样本
//                    ++it;  // 移动迭代器
//                }
//            } else {  // 如果无有效样本
//                ++it;  // 移动迭代器
//                continue;  // 跳过当前通道
//            }

//            for (size_t i = 0; i < toRead; ++i) {  // 遍历样本
//                out[i * device->outputChannels.size() + channel->channelIndex] = temp[i];  // 填充输出缓冲区
//            }
//            if (!channel->outputQueue->enqueue_bulk(temp.data(), toRead)) {  // 尝试批量入队
//                if (currentTime - device->lastErrorTime >= 1.0) {  // 如果距离上次错误超过 1 秒
//                    QString msg = QString("Output queue overflow risk: %1").arg(channel->deviceName);  // 创建溢出警告
//                    emit self->errorOccurred(msg);  // 发出错误信号
//                    device->lastErrorTime = currentTime;  // 更新上次错误时间
//                    qDebug() << msg;  // 输出调试信息
//                }
//            }
//        }
//    }

//    qint64 elapsedTimeMs = self->timer.elapsed();  // 获取回调执行时间（毫秒）
//    double elapsedTime = elapsedTimeMs / 1000.0;  // 转换为秒
//    double latencyTime = static_cast<double>(device->streamLatency) / device->sampleRate;  // 计算延迟时间
//    if (elapsedTime > 0.5 * latencyTime) {  // 如果执行时间超过 50% 延迟
//        device->errorCount++;  // 增加错误计数
//        if (currentTime - device->lastErrorTime >= 1.0) {  // 如果距离上次错误超过 1 秒
//            QString msg = QString("WASAPI callback timeout: elapsed=%1s, latency=%2s")  // 创建超时警告
//                          .arg(elapsedTime).arg(latencyTime);
//            emit self->errorOccurred(msg);  // 发出错误信号
//            device->lastErrorTime = currentTime;  // 更新上次错误时间
//            qDebug() << msg;  // 输出调试信息
//        }
//    } else {  // 如果执行时间正常
//        device->errorCount = 0;  // 重置错误计数
//    }

//    if (device->errorCount >= 5) {  // 如果错误计数达到 5 次
//        QStringList inputChannels, outputChannels;  // 定义输入/输出通道列表
//        for (const auto &ch : device->inputChannels) {  // 遍历输入通道
//            if (ch->active) inputChannels.append(ch->deviceName);  // 添加活跃通道
//        }
//        for (const auto &ch : device->outputChannels) {  // 遍历输出通道
//            if (ch->active) outputChannels.append(ch->deviceName);  // 添加活跃通道
//        }
//        QString errorMessage;  // 定义错误信息
//        self->stopStream(inputChannels, outputChannels);  // 停止流
//        if (!self->startStream(inputChannels, outputChannels, device->sampleRate, errorMessage)) {  // 尝试重启流
//            emit self->errorOccurred("Failed to restart stream: " + errorMessage);  // 如果重启失败，发出错误信号
//        }
//        device->errorCount = 0;  // 重置错误计数
//        qDebug() << "Restarted stream for" << apiToString(device->dac->getCurrentApi());  // 输出调试信息
//    }

//    return 0;  // 返回 0，继续流处理
//}

void AudioPlus::updateAllDisplayBuffers() {  // 更新所有显示缓冲区函数
    QMutexLocker locker(&displayMutex);  // 加锁，保护显示缓冲区

    for (auto it = asioHosts.constBegin(); it != asioHosts.constEnd(); ++it) {  // 遍历 ASIO 主机
        const DeviceStream &stream = it.value()->getStream();  // 获取设备流
        for (const auto &channel : stream.inputChannels) {  // 遍历输入通道
            if (channel->active && channel->inputQueue) {  // 如果通道活跃且队列有效
                std::vector<float> samples(displayPoints_);  // 创建样本容器
                size_t samplesRead = channel->inputQueue->try_dequeue_bulk(samples.data(), displayPoints_);  // 尝试出队样本
                auto &buffer = useBuffer1_ ? displayBuffer1_[isInput][channel->deviceName] : displayBuffer2_[isInput][channel->deviceName];  // 选择缓冲区
                for (size_t i = 0; i < samplesRead; ++i) {  // 遍历样本
                    buffer[i] = samples[i];  // 填充缓冲区
                }
                for (size_t i = samplesRead; i < displayPoints_; ++i) {  // 填充剩余部分
                    buffer[i] = 0.0f;  // 使用 0 填充
                }
                emit dataUpdated(isInput, channel->deviceName);  // 发出数据更新信号
            }
        }
        for (const auto &channel : stream.outputChannels) {  // 遍历输出通道
            if (channel->active && channel->outputQueue) {  // 如果通道活跃且队列有效
                std::vector<float> samples(displayPoints_);  // 创建样本容器
                size_t samplesRead = channel->outputQueue->try_dequeue_bulk(samples.data(), displayPoints_);  // 尝试出队样本
                auto &buffer = useBuffer1_ ? displayBuffer1_[isOutput][channel->deviceName] : displayBuffer2_[isOutput][channel->deviceName];  // 选择缓冲区
                for (size_t i = 0; i < samplesRead; ++i) {  // 遍历样本
                    buffer[i] = samples[i];  // 填充缓冲区
                }
                for (size_t i = samplesRead; i < displayPoints_; ++i) {  // 填充剩余部分
                    buffer[i] = 0.0f;  // 使用 0 填充
                }
                emit dataUpdated(isOutput, channel->deviceName);  // 发出数据更新信号
            }
        }
    }

    for (auto it = wasapiHosts.constBegin(); it != wasapiHosts.constEnd(); ++it) {  // 遍历 WASAPI 主机
        const DeviceStream &stream = it.value()->getStream();  // 获取设备流
        for (const auto &channel : stream.inputChannels) {  // 遍历输入通道
            if (channel->active && channel->inputQueue) {  // 如果通道活跃且队列有效
                std::vector<float> samples(displayPoints_);  // 创建样本容器
                size_t samplesRead = channel->inputQueue->try_dequeue_bulk(samples.data(), displayPoints_);  // 尝试出队样本
                auto &buffer = useBuffer1_ ? displayBuffer1_[isInput][channel->deviceName] : displayBuffer2_[isInput][channel->deviceName];  // 选择缓冲区
                for (size_t i = 0; i < samplesRead; ++i) {  // 遍历样本
                    buffer[i] = samples[i];  // 填充缓冲区
                }
                for (size_t i = samplesRead; i < displayPoints_; ++i) {  // 填充剩余部分
                    buffer[i] = 0.0f;  // 使用 0 填充
                }
                emit dataUpdated(isInput, channel->deviceName);  // 发出数据更新信号
            }
        }
        for (const auto &channel : stream.outputChannels) {  // 遍历输出通道
            if (channel->active && channel->outputQueue) {  // 如果通道活跃且队列有效
                std::vector<float> samples(displayPoints_);  // 创建样本容器
                size_t samplesRead = channel->outputQueue->try_dequeue_bulk(samples.data(), displayPoints_);  // 尝试出队样本
                auto &buffer = useBuffer1_ ? displayBuffer1_[isOutput][channel->deviceName] : displayBuffer2_[isOutput][channel->deviceName];  // 选择缓冲区
                for (size_t i = 0; i < samplesRead; ++i) {  // 遍历样本
                    buffer[i] = samples[i];  // 填充缓冲区
                }
                for (size_t i = samplesRead; i < displayPoints_; ++i) {  // 填充剩余部分
                    buffer[i] = 0.0f;  // 使用 0 填充
                }
                emit dataUpdated(isOutput, channel->deviceName);  // 发出数据更新信号
            }
        }
    }

    useBuffer1_ = !useBuffer1_;  // 切换缓冲区
}

//void AudioPlus::updateDisplayBuffers(const QStringList &inputChannels, const QStringList &outputChannels) {  // 更新显示缓冲区函数
//    QMutexLocker locker(&displayMutex);  // 加锁，保护 displayBuffer1_ 和 displayBuffer2_
//    auto &writeBuffer = useBuffer1_ ? displayBuffer1_ : displayBuffer2_;  // 选择当前写入缓冲区
//    unsigned int maxSamples = std::min(displayPoints_, static_cast<unsigned int>(44100 * 0.05));  // 设置最大样本数（4410 样本，50ms）
//    unsigned int index = 0;  // 初始化写入索引

//    for (const QString &channelName : outputChannels) {  // 遍历输出通道列表
//        for (auto &device : devices_) {  // 遍历设备流
//            for (const auto &channel : device.outputChannels) {  // 遍历设备输出通道
//                if (channel->deviceName == channelName && channel->active && channel->outputQueue) {  // 如果通道匹配、活跃且队列有效
//                    std::vector<float> temp(maxSamples);  // 创建临时数据容器
//                    size_t read = channel->outputQueue->try_dequeue_bulk(temp.data(), maxSamples);  // 从输出队列批量出队
//                    for (size_t i = 0; i < read && index < maxSamples; ++i) {  // 遍历出队数据
//                        writeBuffer[ChannelType::isOutput][channelName][index++] = temp[i] / SCALE;  // 写入显示缓冲区，应用缩放
//                    }
//                    if (index >= maxSamples) {  // 如果写入样本达到最大值
//                        emit dataUpdated(ChannelType::isOutput, channelName);  // 发出数据更新信号，通知界面刷新
//                    }
//                }
//            }
//        }
//    }

//    for (const QString &channelName : inputChannels) {  // 遍历输入通道列表
//        for (auto &device : devices_) {  // 遍历设备流
//            for (const auto &channel : device.inputChannels) {  // 遍历设备输入通道
//                if (channel->deviceName == channelName && channel->active && channel->inputQueue) {  // 如果通道匹配、活跃且队列有效
//                    std::vector<float> temp(maxSamples);  // 创建临时数据容器
//                    size_t read = channel->inputQueue->try_dequeue_bulk(temp.data(), maxSamples);  // 从输入队列批量出队
//                    for (size_t i = 0; i < read && index < maxSamples; ++i) {  // 遍历出队数据
//                        writeBuffer[ChannelType::isInput][channelName][index++] = temp[i] / SCALE;  // 写入显示缓冲区，应用缩放
//                    }
//                    if (index >= maxSamples) {  // 如果写入样本达到最大值
//                        emit dataUpdated(ChannelType::isInput, channelName);  // 发出数据更新信号，通知界面刷新
//                    }
//                }
//            }
//        }
//    }

//    if (index >= maxSamples) {  // 如果写入样本达到最大值
//        useBuffer1_ = !useBuffer1_;  // 切换显示缓冲区（buffer1 ↔ buffer2）
//    }
//}


AudioPlus::AsioHost::AsioHost(const QString &key, AudioPlus *parent)  // 构造函数
    : key(key), parent(parent),  // 初始化设备键和 AudioPlus 指针
      rtAudio(new RtAudio(RtAudio::WINDOWS_ASIO,
                          std::function<void(RtAudioErrorType, const std::string&)>(
                              std::bind(&AsioHost::errorCallback, this, std::placeholders::_1, std::placeholders::_2)))) {}  // 创建 ASIO RtAudio 实例
void AudioPlus::AsioHost::errorCallback(RtAudioErrorType type, const std::string &errorText) {  // 错误回调函数
    QString errorTypeStr;  // 定义错误类型字符串
    switch (type) {  // 根据错误类型设置字符串
        case RtAudioErrorType::RTAUDIO_NO_ERROR: errorTypeStr = "RTAUDIO_NO_ERROR"; break;
        case RtAudioErrorType::RTAUDIO_WARNING: errorTypeStr = "RTAUDIO_WARNING"; break;
        case RtAudioErrorType::RTAUDIO_UNKNOWN_ERROR: errorTypeStr = "RTAUDIO_UNKNOWN_ERROR"; break;
        case RtAudioErrorType::RTAUDIO_NO_DEVICES_FOUND: errorTypeStr = "RTAUDIO_NO_DEVICES_FOUND"; break;
        case RtAudioErrorType::RTAUDIO_INVALID_DEVICE: errorTypeStr = "RTAUDIO_INVALID_DEVICE"; break;
        case RtAudioErrorType::RTAUDIO_DEVICE_DISCONNECT: errorTypeStr = "RTAUDIO_DEVICE_DISCONNECT"; break;
        case RtAudioErrorType::RTAUDIO_MEMORY_ERROR: errorTypeStr = "RTAUDIO_MEMORY_ERROR"; break;
        case RtAudioErrorType::RTAUDIO_INVALID_PARAMETER: errorTypeStr = "RTAUDIO_INVALID_PARAMETER"; break;
        case RtAudioErrorType::RTAUDIO_INVALID_USE: errorTypeStr = "RTAUDIO_INVALID_USE"; break;
        case RtAudioErrorType::RTAUDIO_DRIVER_ERROR: errorTypeStr = "RTAUDIO_DRIVER_ERROR"; break;
        case RtAudioErrorType::RTAUDIO_SYSTEM_ERROR: errorTypeStr = "RTAUDIO_SYSTEM_ERROR"; break;
        case RtAudioErrorType::RTAUDIO_THREAD_ERROR: errorTypeStr = "RTAUDIO_THREAD_ERROR"; break;
        default: errorTypeStr = "UNKNOWN"; break;
    }

    QString msg = QString("%1 error: %2 (%3)").arg(key).arg(errorTypeStr).arg(QString::fromStdString(errorText));  // 构造错误消息
    qDebug() << msg;  // 输出调试信息

    if (type != RtAudioErrorType::RTAUDIO_WARNING && stream.isRunning && rtAudio->isStreamOpen()) {  // 如果是严重错误且流已打开
        rtAudio->stopStream();  // 停止音频流
        if (rtAudio->isStreamOpen()) {  // 检查流是否仍打开
            rtAudio->closeStream();  // 关闭音频流
        }
        stream.isRunning = false;  // 标记设备流为非运行状态
        qDebug() << "Closed stream for" << key << "due to error";  // 输出调试信息
    }

    QMetaObject::invokeMethod(parent, "errorOccurred", Qt::QueuedConnection, Q_ARG(QString, msg));  // 在主线程发出错误信号
}
bool AudioPlus::AsioHost::addChannel(const QString &channelName, ChannelType type, const AudioChannalInfoConfig &config,  // 添加通道
                                     unsigned int sampleRate, QString &errorMessage) {
    for (const auto &ch : type == isInput ? stream.inputChannels : stream.outputChannels) {  // 检查重复通道
        if (ch->deviceName == channelName) {  // 如果通道已存在
            errorMessage = QString("%1 channel %2 already added").arg(type == isInput ? "Input" : "Output").arg(channelName);  // 设置错误信息
            return false;  // 返回失败
        }
    }
    auto info = QSharedPointer<ChannelInfo>::create();  // 创建通道信息智能指针
    info->deviceName = channelName;  // 设置通道名称
    info->deviceId = config.DriverID;  // 设置设备 ID
    info->api = RtAudio::WINDOWS_ASIO;  // 设置音频 API
    info->channelIndex = config.Sound_Card_Channel_Name.split("_").last().toUInt() - 1;  // 设置通道索引（0-based）
    info->gain = config.Channel_Gain;  // 设置通道增益
    info->active = true;  // 标记通道为活跃状态
    info->sampleRate = sampleRate;  // 设置采样率
    if (type == isInput) {  // 如果是输入通道
        info->inputQueue = QSharedPointer<moodycamel::ConcurrentQueue<float>>(  // 创建输入无锁队列
            new moodycamel::ConcurrentQueue<float>(sampleRate * 600));  // 设置容量为 10 分钟
        stream.inputChannels.append(info);  // 添加到输入通道列表
    } else {  // 如果是输出通道
        info->outputQueue = QSharedPointer<moodycamel::ConcurrentQueue<float>>(  // 创建输出无锁队列
            new moodycamel::ConcurrentQueue<float>(sampleRate * 600));  // 设置容量为 10 分钟
        stream.outputChannels.append(info);  // 添加到输出通道列表
    }
    stream.activeChannelCount++;  // 增加活跃通道计数
    qDebug() << "Added" << (type == isInput ? "input" : "output") << "channel:" << channelName  // 输出调试信息
             << "to device:" << key << "DriverID:" << info->deviceId;  // 输出设备信息
    return true;  // 返回成功
}

bool AudioPlus::AsioHost::startStream(unsigned int sampleRate, QString &errorMessage) {  // 启动流函数
    if (stream.isRunning) {  // 如果流已运行
        errorMessage = QString("Device %1 is already running").arg(key);  // 设置错误信息
        return false;  // 返回失败
    }
    RtAudio::DeviceInfo deviceInfo = rtAudio->getDeviceInfo(stream.inputChannels.isEmpty() ?
                                                           stream.outputChannels.first()->deviceId :
                                                           stream.inputChannels.first()->deviceId);  // 获取设备信息
    RtAudio::StreamParameters iParams, oParams;  // 定义输入/输出流参数
    if (deviceInfo.inputChannels > 0) {  // 如果设备支持输入
        iParams.deviceId = deviceInfo.ID;  // 设置设备 ID
        iParams.nChannels = deviceInfo.inputChannels;  // 设置最大输入通道数
        iParams.firstChannel = 0;  // 设置起始通道为 0
    }
    if (deviceInfo.outputChannels > 0) {  // 如果设备支持输出
        oParams.deviceId = deviceInfo.ID;  // 设置设备 ID
        oParams.nChannels = deviceInfo.outputChannels;  // 设置最大输出通道数
        oParams.firstChannel = 0;  // 设置起始通道为 0
    }
    if (!iParams.nChannels && !oParams.nChannels) {  // 检查输入和输出参数
        errorMessage = QString("No valid input or output channels for %1").arg(key);  // 设置错误信息
        return false;  // 返回失败
    }
    unsigned int bufferFrames = 512;  // 设置缓冲区帧数（默认 512）
    RtAudio::StreamOptions options;  // 创建流选项对象
    options.flags = RTAUDIO_MINIMIZE_LATENCY | RTAUDIO_SCHEDULE_REALTIME;  // 设置流选项：最小化延迟、实时调度
    //options.numberOfBuffers = 4;  // 设置缓冲区数量为 4
    //options.streamName = key.toStdString();  // 设置流名称为设备键
    if (rtAudio->openStream(oParams.nChannels ? &oParams : nullptr, iParams.nChannels ? &iParams : nullptr,  // 尝试开启音频流
                            FORMAT, sampleRate, &bufferFrames, &callback, this, &options) != 0) {
        errorMessage = QString("Failed to open stream for %1: %2").arg(key).arg(rtAudio->getErrorText().c_str());  // 设置错误信息
        return false;  // 返回失败
    }
    if (rtAudio->startStream() != 0) {  // 尝试启动音频流
        errorMessage = QString("Failed to start stream for %1: %2").arg(key).arg(rtAudio->getErrorText().c_str());  // 设置错误信息
        rtAudio->closeStream();  // 关闭已打开的流
        return false;  // 返回失败
    }
    stream.dac = rtAudio.data();  // 设置设备流中的 RtAudio 指针
    stream.isRunning = true;  // 标记设备流为运行状态
    stream.sampleRate = sampleRate;  // 设置采样率
    stream.streamLatency = bufferFrames;  // 设置流延迟
    qDebug() << "Started stream for" << key << "with latency" << bufferFrames << "samples";  // 输出调试信息
    return true;  // 返回成功
}

void AudioPlus::AsioHost::stopStream() {  // 停止流函数
    if (stream.isRunning && stream.dac && stream.dac->isStreamRunning()) {  // 检查流是否运行
        stream.dac->stopStream();  // 停止音频流
        if (stream.dac->isStreamOpen()) {  // 检查流是否仍打开
            stream.dac->closeStream();  // 关闭音频流
        }
        stream.isRunning = false;  // 标记设备流为非运行状态
        stream.dac = nullptr;  // 清空设备指针
        qDebug() << "Stopped stream for" << key;  // 输出调试信息
    }
}

int AudioPlus::AsioHost::callback(void *outputBuffer, void *inputBuffer, unsigned int nFrames,  // 静态回调函数
                                  double streamTime, RtAudioStreamStatus status, void *userData) {

    AsioHost *host = static_cast<AsioHost*>(userData);  // 将用户数据转换为 AsioHost 指针
    DeviceStream &device = host->stream;  // 获取设备流引用
    AudioPlus *self = host->parent;  // 获取 AudioPlus 指针

    self->timer.start();  // 启动计时器，记录回调执行时间

    double expectedTime = static_cast<double>(nFrames) / device.sampleRate;  // 计算预期回调间隔（秒）
    double currentTime = QDateTime::currentMSecsSinceEpoch() / 1000.0;  // 获取当前时间（秒）
//    if (device.lastStreamTime >= 0.0) {  // 如果存在上次流时间
//        double deltaTime = streamTime - device.lastStreamTime;  // 计算时间差
//        if (deltaTime > 2.0 * expectedTime) {  // 如果时间差超过 2 倍预期
//            device.errorCount++;  // 增加错误计数
//            if (currentTime - device.lastErrorTime >= 1.0) {  // 如果距离上次错误超过 1 秒
//                QString msg = QString("%1 callback disconnected: deltaTime=%2s, expected=%3s")  // 创建断开警告
//                              .arg(host->key).arg(deltaTime).arg(expectedTime);
//               // emit self->errorOccurred(msg);  // 发出错误信号
//                device.lastErrorTime = currentTime;  // 更新上次错误时间
//                //qDebug() << msg;  // 输出调试信息
//            }
//        } else {  // 如果时间差正常
//            device.errorCount = 0;  // 重置错误计数
//        }
//    }
    device.lastStreamTime = streamTime;  // 更新上次流时间

    float *in = static_cast<float*>(inputBuffer);  // 转换输入缓冲区指针
    if (in) {  // 如果输入缓冲区有效
        for (const auto &channel : device.inputChannels) {  // 遍历输入通道
            if (channel->active && channel->inputQueue) {  // 如果通道活跃且队列有效
                std::vector<float> temp(nFrames);  // 创建临时数据容器
                for (unsigned int i = 0; i < nFrames; ++i) {  // 遍历帧
                    temp[i] = in[i * device.inputChannels.size() + channel->channelIndex];  // 提取通道数据
                }
                channel->inputQueue->enqueue_bulk(temp.data(), nFrames);  // 批量入队，无溢出检查
            }
        }
    }

    float *out = static_cast<float*>(outputBuffer);  // 转换输出缓冲区指针
    if (out) {  // 如果输出缓冲区有效
        std::memset(out, 0, nFrames * device.outputChannels.size() * sizeof(float));  // 清空输出缓冲区
        for (auto it = device.outputChannels.begin(); it != device.outputChannels.end(); ) {  // 遍历输出通道
            auto channel = *it;  // 获取当前通道指针
            if (!channel->active || !channel->outputQueue) {  // 如果通道不活跃或队列无效
                ++it;  // 移动迭代器
                continue;  // 跳过当前通道
            }

            std::vector<float> temp(nFrames);  // 创建临时数据容器
            size_t toRead = 0;  // 初始化读取样本数

            if (channel->signalType == SignalType::Sine && !channel->sineWaveData.empty()) {  // 如果是正弦波
                toRead = nFrames;  // 设置读取样本数为帧数
                for (unsigned int i = 0; i < nFrames; ++i) {  // 遍历帧
                    unsigned int index = (channel->sampleOffset + i) % channel->sineWaveData.size();  // 计算正弦波索引
                    temp[i] = channel->sineWaveData[index] * channel->gain;  // 生成正弦波数据
                }
                channel->sampleOffset += nFrames;  // 更新样本偏移
            } else if (channel->validSamples > 0) {  // 如果有有效样本（扫频/WAV/噪声）
                toRead = std::min(nFrames, channel->validSamples);  // 计算读取样本数
                for (size_t i = 0; i < toRead; ++i) {  // 遍历样本
                    if (channel->tempDataIndex < channel->totalSamples) {  // 如果索引有效
                        temp[i] = channel->tempData[channel->tempDataIndex++] * channel->gain;  // 提取临时数据
                    } else {  // 如果索引超出
                        temp[i] = 0.0f;  // 填充 0
                    }
                }
                channel->validSamples -= toRead;  // 减少有效样本数
                if (channel->validSamples == 0) {  // 如果样本耗尽
                    channel->active = false;  // 标记通道为非活跃
                    device.activeChannelCount--;  // 减少活跃通道计数
                    emit self->playbackFinished(ChannelType::isOutput, channel->deviceName);  // 发出播放完成信号
                    it = device.outputChannels.erase(it);  // 移除通道
                    continue;  // 跳过当前通道
                } else {  // 如果仍有样本
                    ++it;  // 移动迭代器
                }
            } else {  // 如果无有效样本
                ++it;  // 移动迭代器
                continue;  // 跳过当前通道
            }

            for (size_t i = 0; i < toRead; ++i) {  // 遍历样本
                out[i * device.outputChannels.size() + channel->channelIndex] = temp[i];  // 填充输出缓冲区
            }
            channel->outputQueue->enqueue_bulk(temp.data(), toRead);  // 批量入队，无溢出检查
        }
    }

//    qint64 elapsedTimeMs = self->timer.elapsed();  // 获取回调执行时间（毫秒）
//    double elapsedTime = elapsedTimeMs / 1000.0;  // 转换为秒
//    double latencyTime = static_cast<double>(device.streamLatency) / device.sampleRate;  // 计算延迟时间
//    if (elapsedTime > 0.5 * latencyTime) {  // 如果执行时间超过 50% 延迟
//        device.errorCount++;  // 增加错误计数
//        if (currentTime - device.lastErrorTime >= 1.0) {  // 如果距离上次错误超过 1 秒
//            QString msg = QString("%1 callback timeout: elapsed=%2s, latency=%3s")  // 创建超时警告
//                          .arg(host->key).arg(elapsedTime).arg(latencyTime);
//            emit self->errorOccurred(msg);  // 发出错误信号
//            device.lastErrorTime = currentTime;  // 更新上次错误时间
//            qDebug() << msg;  // 输出调试信息
//        }
//    } else {  // 如果执行时间正常
//        device.errorCount = 0;  // 重置错误计数
//    }

//    if (device.errorCount >= 5) {  // 如果错误计数达到 5 次
//        QStringList inputChannels, outputChannels;  // 定义输入/输出通道列表
//        for (const auto &ch : device.inputChannels) {  // 遍历输入通道
//            if (ch->active) inputChannels.append(ch->deviceName);  // 添加活跃通道
//        }
//        for (const auto &ch : device.outputChannels) {  // 遍历输出通道
//            if (ch->active) outputChannels.append(ch->deviceName);  // 添加活跃通道
//        }
//        QString errorMessage;  // 定义错误信息
//        host->stopStream();  // 停止流
//        if (!host->startStream(device.sampleRate, errorMessage)) {  // 尝试重启流
//            emit self->errorOccurred("Failed to restart stream for " + host->key + ": " + errorMessage);  // 发出错误信号
//        }
//        device.errorCount = 0;  // 重置错误计数
//        qDebug() << "Restarted stream for" << host->key;  // 输出调试信息
//    }

    return 0;  // 返回 0，继续流处理
}







AudioPlus::WasapiHost::WasapiHost(const QString &key, AudioPlus *parent)  // 构造函数
    : key(key), parent(parent),  // 初始化设备键和 AudioPlus 指针
      rtAudio(new RtAudio(RtAudio::WINDOWS_WASAPI,
                          std::bind(&WasapiHost::errorCallback, this, std::placeholders::_1, std::placeholders::_2))) {}  // 创建 WASAPI RtAudio 实例

void AudioPlus::WasapiHost::errorCallback(RtAudioErrorType type, const std::string &errorText) {  // 错误回调函数
    QString errorTypeStr;  // 定义错误类型字符串
    switch (type) {  // 根据错误类型设置字符串
        case RtAudioErrorType::RTAUDIO_NO_ERROR: errorTypeStr = "RTAUDIO_NO_ERROR"; break;
        case RtAudioErrorType::RTAUDIO_WARNING: errorTypeStr = "RTAUDIO_WARNING"; break;
        case RtAudioErrorType::RTAUDIO_UNKNOWN_ERROR: errorTypeStr = "RTAUDIO_UNKNOWN_ERROR"; break;
        case RtAudioErrorType::RTAUDIO_NO_DEVICES_FOUND: errorTypeStr = "RTAUDIO_NO_DEVICES_FOUND"; break;
        case RtAudioErrorType::RTAUDIO_INVALID_DEVICE: errorTypeStr = "RTAUDIO_INVALID_DEVICE"; break;
        case RtAudioErrorType::RTAUDIO_DEVICE_DISCONNECT: errorTypeStr = "RTAUDIO_DEVICE_DISCONNECT"; break;
        case RtAudioErrorType::RTAUDIO_MEMORY_ERROR: errorTypeStr = "RTAUDIO_MEMORY_ERROR"; break;
        case RtAudioErrorType::RTAUDIO_INVALID_PARAMETER: errorTypeStr = "RTAUDIO_INVALID_PARAMETER"; break;
        case RtAudioErrorType::RTAUDIO_INVALID_USE: errorTypeStr = "RTAUDIO_INVALID_USE"; break;
        case RtAudioErrorType::RTAUDIO_DRIVER_ERROR: errorTypeStr = "RTAUDIO_DRIVER_ERROR"; break;
        case RtAudioErrorType::RTAUDIO_SYSTEM_ERROR: errorTypeStr = "RTAUDIO_SYSTEM_ERROR"; break;
        case RtAudioErrorType::RTAUDIO_THREAD_ERROR: errorTypeStr = "RTAUDIO_THREAD_ERROR"; break;
        default: errorTypeStr = "UNKNOWN"; break;
    }

    QString msg = QString("%1 error: %2 (%3)").arg(key).arg(errorTypeStr).arg(QString::fromStdString(errorText));  // 构造错误消息
    qDebug() << msg;  // 输出调试信息

    if (type != RtAudioErrorType::RTAUDIO_WARNING && stream.isRunning && rtAudio->isStreamOpen()) {  // 如果是严重错误且流已打开
        rtAudio->stopStream();  // 停止音频流
        if (rtAudio->isStreamOpen()) {  // 检查流是否仍打开
            rtAudio->closeStream();  // 关闭音频流
        }
        stream.isRunning = false;  // 标记设备流为非运行状态
        qDebug() << "Closed stream for" << key << "due to error";  // 输出调试信息
    }

    QMetaObject::invokeMethod(parent, "errorOccurred", Qt::QueuedConnection, Q_ARG(QString, msg));  // 在主线程发出错误信号
}
bool AudioPlus::WasapiHost::addChannel(const QString &channelName, ChannelType type, const AudioChannalInfoConfig &config,  // 添加通道
                                     unsigned int sampleRate, QString &errorMessage) {
    for (const auto &ch : type == isInput ? stream.inputChannels : stream.outputChannels) {  // 检查重复通道
        if (ch->deviceName == channelName) {  // 如果通道已存在
            errorMessage = QString("%1 channel %2 already added").arg(type == isInput ? "Input" : "Output").arg(channelName);  // 设置错误信息
            return false;  // 返回失败
        }
    }
    auto info = QSharedPointer<ChannelInfo>::create();  // 创建通道信息智能指针
    info->deviceName = channelName;  // 设置通道名称
    info->deviceId = config.DriverID;  // 设置设备 ID
    info->api = RtAudio::WINDOWS_ASIO;  // 设置音频 API
    info->channelIndex = config.Sound_Card_Channel_Name.split("_").last().toUInt() - 1;  // 设置通道索引（0-based）
    info->gain = config.Channel_Gain;  // 设置通道增益
    info->active = true;  // 标记通道为活跃状态
    info->sampleRate = sampleRate;  // 设置采样率
    if (type == isInput) {  // 如果是输入通道
        info->inputQueue = QSharedPointer<moodycamel::ConcurrentQueue<float>>(  // 创建输入无锁队列
            new moodycamel::ConcurrentQueue<float>(sampleRate * 600));  // 设置容量为 10 分钟
        stream.inputChannels.append(info);  // 添加到输入通道列表
    } else {  // 如果是输出通道
        info->outputQueue = QSharedPointer<moodycamel::ConcurrentQueue<float>>(  // 创建输出无锁队列
            new moodycamel::ConcurrentQueue<float>(sampleRate * 600));  // 设置容量为 10 分钟
        stream.outputChannels.append(info);  // 添加到输出通道列表
    }
    stream.activeChannelCount++;  // 增加活跃通道计数
    qDebug() << "Added" << (type == isInput ? "input" : "output") << "channel:" << channelName  // 输出调试信息
             << "to device:" << key << "DriverID:" << info->deviceId;  // 输出设备信息
    return true;  // 返回成功
}

bool AudioPlus::WasapiHost::startStream(unsigned int sampleRate, QString &errorMessage) {  // 启动流函数
    if (stream.isRunning) {  // 如果流已运行
        errorMessage = QString("Device %1 is already running").arg(key);  // 设置错误信息
        return false;  // 返回失败
    }
    RtAudio::DeviceInfo deviceInfo = rtAudio->getDeviceInfo(stream.inputChannels.isEmpty() ?
                                                           stream.outputChannels.first()->deviceId :
                                                           stream.inputChannels.first()->deviceId);  // 获取设备信息
    RtAudio::StreamParameters iParams, oParams;  // 定义输入/输出流参数
    if (deviceInfo.inputChannels > 0) {  // 如果设备支持输入
        iParams.deviceId = deviceInfo.ID;  // 设置设备 ID
        iParams.nChannels = deviceInfo.inputChannels;  // 设置最大输入通道数
        iParams.firstChannel = 0;  // 设置起始通道为 0
    }
    if (deviceInfo.outputChannels > 0) {  // 如果设备支持输出
        oParams.deviceId = deviceInfo.ID;  // 设置设备 ID
        oParams.nChannels = deviceInfo.outputChannels;  // 设置最大输出通道数
        oParams.firstChannel = 0;  // 设置起始通道为 0
    }
    if (!iParams.nChannels && !oParams.nChannels) {  // 检查输入和输出参数
        errorMessage = QString("No valid input or output channels for %1").arg(key);  // 设置错误信息
        return false;  // 返回失败
    }
    unsigned int bufferFrames = 512;  // 设置缓冲区帧数（默认 512）
    RtAudio::StreamOptions options;  // 创建流选项对象
    options.flags = RTAUDIO_MINIMIZE_LATENCY | RTAUDIO_SCHEDULE_REALTIME;  // 设置流选项：最小化延迟、实时调度
    options.numberOfBuffers = 4;  // 设置缓冲区数量为 4
    options.streamName = key.toStdString();  // 设置流名称为设备键
    if (rtAudio->openStream(oParams.nChannels ? &oParams : nullptr, iParams.nChannels ? &iParams : nullptr,  // 尝试开启音频流
                            FORMAT, sampleRate, &bufferFrames, &callback, this, &options) != 0) {
        errorMessage = QString("Failed to open stream for %1: %2").arg(key).arg(rtAudio->getErrorText().c_str());  // 设置错误信息
        return false;  // 返回失败
    }
    if (rtAudio->startStream() != 0) {  // 尝试启动音频流
        errorMessage = QString("Failed to start stream for %1: %2").arg(key).arg(rtAudio->getErrorText().c_str());  // 设置错误信息
        rtAudio->closeStream();  // 关闭已打开的流
        return false;  // 返回失败
    }
    stream.dac = rtAudio.data();  // 设置设备流中的 RtAudio 指针
    stream.isRunning = true;  // 标记设备流为运行状态
    stream.sampleRate = sampleRate;  // 设置采样率
    stream.streamLatency = bufferFrames;  // 设置流延迟
    qDebug() << "Started stream for" << key << "with latency" << bufferFrames << "samples";  // 输出调试信息
    return true;  // 返回成功
}

void AudioPlus::WasapiHost::stopStream() {  // 停止流函数
    if (stream.isRunning && stream.dac && stream.dac->isStreamRunning()) {  // 检查流是否运行
        stream.dac->stopStream();  // 停止音频流
        if (stream.dac->isStreamOpen()) {  // 检查流是否仍打开
            stream.dac->closeStream();  // 关闭音频流
        }
        stream.isRunning = false;  // 标记设备流为非运行状态
        stream.dac = nullptr;  // 清空设备指针
        qDebug() << "Stopped stream for" << key;  // 输出调试信息
    }
}

int AudioPlus::WasapiHost::callback(void *outputBuffer, void *inputBuffer, unsigned int nFrames,  // 静态回调函数
                                    double streamTime, RtAudioStreamStatus status, void *userData) {
    WasapiHost *host = static_cast<WasapiHost*>(userData);  // 将用户数据转换为 WasapiHost 指针
    DeviceStream &device = host->stream;  // 获取设备流引用
    AudioPlus *self = host->parent;  // 获取 AudioPlus 指针

    self->timer.start();  // 启动计时器，记录回调执行时间

    double expectedTime = static_cast<double>(nFrames) / device.sampleRate;  // 计算预期回调间隔（秒）
    double currentTime = QDateTime::currentMSecsSinceEpoch() / 1000.0;  // 获取当前时间（秒）
    if (device.lastStreamTime >= 0.0) {  // 如果存在上次流时间
        double deltaTime = streamTime - device.lastStreamTime;  // 计算时间差
        if (deltaTime > 2.0 * expectedTime) {  // 如果时间差超过 2 倍预期
            device.errorCount++;  // 增加错误计数
            if (currentTime - device.lastErrorTime >= 1.0) {  // 如果距离上次错误超过 1 秒
                QString msg = QString("%1 callback disconnected: deltaTime=%2s, expected=%3s")  // 创建断开警告
                              .arg(host->key).arg(deltaTime).arg(expectedTime);
                emit self->errorOccurred(msg);  // 发出错误信号
                device.lastErrorTime = currentTime;  // 更新上次错误时间
                qDebug() << msg;  // 输出调试信息
            }
        } else {  // 如果时间差正常
            device.errorCount = 0;  // 重置错误计数
        }
    }
    device.lastStreamTime = streamTime;  // 更新上次流时间

    float *in = static_cast<float*>(inputBuffer);  // 转换输入缓冲区指针
    if (in) {  // 如果输入缓冲区有效
        for (const auto &channel : device.inputChannels) {  // 遍历输入通道
            if (channel->active && channel->inputQueue) {  // 如果通道活跃且队列有效
                std::vector<float> temp(nFrames);  // 创建临时数据容器
                for (unsigned int i = 0; i < nFrames; ++i) {  // 遍历帧
                    temp[i] = in[i * device.inputChannels.size() + channel->channelIndex];  // 提取通道数据
                }
                channel->inputQueue->enqueue_bulk(temp.data(), nFrames);  // 批量入队，无溢出检查
            }
        }
    }

    float *out = static_cast<float*>(outputBuffer);  // 转换输出缓冲区指针
    if (out) {  // 如果输出缓冲区有效
        std::memset(out, 0, nFrames * device.outputChannels.size() * sizeof(float));  // 清空输出缓冲区
        for (auto it = device.outputChannels.begin(); it != device.outputChannels.end(); ) {  // 遍历输出通道
            auto channel = *it;  // 获取当前通道指针
            if (!channel->active || !channel->outputQueue) {  // 如果通道不活跃或队列无效
                ++it;  // 移动迭代器
                continue;  // 跳过当前通道
            }

            std::vector<float> temp(nFrames);  // 创建临时数据容器
            size_t toRead = 0;  // 初始化读取样本数

            if (channel->signalType == SignalType::Sine && !channel->sineWaveData.empty()) {  // 如果是正弦波
                toRead = nFrames;  // 设置读取样本数为帧数
                for (unsigned int i = 0; i < nFrames; ++i) {  // 遍历帧
                    unsigned int index = (channel->sampleOffset + i) % channel->sineWaveData.size();  // 计算正弦波索引
                    temp[i] = channel->sineWaveData[index] * channel->gain;  // 生成正弦波数据
                }
                channel->sampleOffset += nFrames;  // 更新样本偏移
            } else if (channel->validSamples > 0) {  // 如果有有效样本（扫频/WAV/噪声）
                toRead = std::min(nFrames, channel->validSamples);  // 计算读取样本数
                for (size_t i = 0; i < toRead; ++i) {  // 遍历样本
                    if (channel->tempDataIndex < channel->totalSamples) {  // 如果索引有效
                        temp[i] = channel->tempData[channel->tempDataIndex++] * channel->gain;  // 提取临时数据
                    } else {  // 如果索引超出
                        temp[i] = 0.0f;  // 填充 0
                    }
                }
                channel->validSamples -= toRead;  // 减少有效样本数
                if (channel->validSamples == 0) {  // 如果样本耗尽
                    channel->active = false;  // 标记通道为非活跃
                    device.activeChannelCount--;  // 减少活跃通道计数
                    emit self->playbackFinished(ChannelType::isOutput, channel->deviceName);  // 发出播放完成信号
                    it = device.outputChannels.erase(it);  // 移除通道
                    continue;  // 跳过当前通道
                } else {  // 如果仍有样本
                    ++it;  // 移动迭代器
                }
            } else {  // 如果无有效样本
                ++it;  // 移动迭代器
                continue;  // 跳过当前通道
            }

            for (size_t i = 0; i < toRead; ++i) {  // 遍历样本
                out[i * device.outputChannels.size() + channel->channelIndex] = temp[i];  // 填充输出缓冲区
            }
            channel->outputQueue->enqueue_bulk(temp.data(), toRead);  // 批量入队，无溢出检查
        }
    }

    qint64 elapsedTimeMs = self->timer.elapsed();  // 获取回调执行时间（毫秒）
    double elapsedTime = elapsedTimeMs / 1000.0;  // 转换为秒
    double latencyTime = static_cast<double>(device.streamLatency) / device.sampleRate;  // 计算延迟时间
    if (elapsedTime > 0.5 * latencyTime) {  // 如果执行时间超过 50% 延迟
        device.errorCount++;  // 增加错误计数
        if (currentTime - device.lastErrorTime >= 1.0) {  // 如果距离上次错误超过 1 秒
            QString msg = QString("%1 callback timeout: elapsed=%2s, latency=%3s")  // 创建超时警告
                          .arg(host->key).arg(elapsedTime).arg(latencyTime);
            emit self->errorOccurred(msg);  // 发出错误信号
            device.lastErrorTime = currentTime;  // 更新上次错误时间
            qDebug() << msg;  // 输出调试信息
        }
    } else {  // 如果执行时间正常
        device.errorCount = 0;  // 重置错误计数
    }

    if (device.errorCount >= 5) {  // 如果错误计数达到 5 次
        QStringList inputChannels, outputChannels;  // 定义输入/输出通道列表
        for (const auto &ch : device.inputChannels) {  // 遍历输入通道
            if (ch->active) inputChannels.append(ch->deviceName);  // 添加活跃通道
        }
        for (const auto &ch : device.outputChannels) {  // 遍历输出通道
            if (ch->active) outputChannels.append(ch->deviceName);  // 添加活跃通道
        }
        QString errorMessage;  // 定义错误信息
        host->stopStream();  // 停止流
        if (!host->startStream(device.sampleRate, errorMessage)) {  // 尝试重启流
            emit self->errorOccurred("Failed to restart stream for " + host->key + ": " + errorMessage);  // 发出错误信号
        }
        device.errorCount = 0;  // 重置错误计数
        qDebug() << "Restarted stream for" << host->key;  // 输出调试信息
    }

    return 0;  // 返回 0，继续流处理
}
