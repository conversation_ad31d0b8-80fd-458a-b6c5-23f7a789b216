<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>iir1: Iir::ChebyshevI::HighShelf&lt; FilterOrder, StateType &gt; Struct Template Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">iir1
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespaceIir.html">Iir</a></li><li class="navelem"><a class="el" href="namespaceIir_1_1ChebyshevI.html">ChebyshevI</a></li><li class="navelem"><a class="el" href="structIir_1_1ChebyshevI_1_1HighShelf.html">HighShelf</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="structIir_1_1ChebyshevI_1_1HighShelf-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">Iir::ChebyshevI::HighShelf&lt; FilterOrder, StateType &gt; Struct Template Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;<a class="el" href="ChebyshevI_8h_source.html">ChebyshevI.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for Iir::ChebyshevI::HighShelf&lt; FilterOrder, StateType &gt;:</div>
<div class="dyncontent">
 <div class="center">
  <img src="structIir_1_1ChebyshevI_1_1HighShelf.png" usemap="#Iir::ChebyshevI::HighShelf_3C_20FilterOrder_2C_20StateType_20_3E_map" alt=""/>
  <map id="Iir::ChebyshevI::HighShelf_3C_20FilterOrder_2C_20StateType_20_3E_map" name="Iir::ChebyshevI::HighShelf_3C_20FilterOrder_2C_20StateType_20_3E_map">
<area href="structIir_1_1PoleFilter.html" alt="Iir::PoleFilter&lt; HighShelfBase, DirectFormII, 4 &gt;" shape="rect" coords="176,56,518,80"/>
<area href="classIir_1_1CascadeStages.html" alt="Iir::CascadeStages&lt; MaxStages, StateType &gt;" shape="rect" coords="352,0,694,24"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a9fe4f6cee3b4b1aa4688ea21d3f5202f"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structIir_1_1ChebyshevI_1_1HighShelf.html#a9fe4f6cee3b4b1aa4688ea21d3f5202f">setup</a> (double sampleRate, double cutoffFrequency, double gainDb, double rippleDb)</td></tr>
<tr class="separator:a9fe4f6cee3b4b1aa4688ea21d3f5202f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6e8901d8a4c2b804f4a4df780f1937cc"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structIir_1_1ChebyshevI_1_1HighShelf.html#a6e8901d8a4c2b804f4a4df780f1937cc">setup</a> (int reqOrder, double sampleRate, double cutoffFrequency, double gainDb, double rippleDb)</td></tr>
<tr class="separator:a6e8901d8a4c2b804f4a4df780f1937cc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa078bdf604ac9feeedd282174bd0cea9"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structIir_1_1ChebyshevI_1_1HighShelf.html#aa078bdf604ac9feeedd282174bd0cea9">setupN</a> (double cutoffFrequency, double gainDb, double rippleDb)</td></tr>
<tr class="separator:aa078bdf604ac9feeedd282174bd0cea9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adbfa0e3487393075e3f178d50293f0bb"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structIir_1_1ChebyshevI_1_1HighShelf.html#adbfa0e3487393075e3f178d50293f0bb">setupN</a> (int reqOrder, double cutoffFrequency, double gainDb, double rippleDb)</td></tr>
<tr class="separator:adbfa0e3487393075e3f178d50293f0bb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_methods_classIir_1_1CascadeStages"><td colspan="2" onclick="javascript:toggleInherit('pub_methods_classIir_1_1CascadeStages')"><img src="closed.png" alt="-"/>&#160;Public Member Functions inherited from <a class="el" href="classIir_1_1CascadeStages.html">Iir::CascadeStages&lt; MaxStages, StateType &gt;</a></td></tr>
<tr class="memitem:ae5253fde0be7ccfa459a3f70fe8e3e31 inherit pub_methods_classIir_1_1CascadeStages"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classIir_1_1CascadeStages.html#ae5253fde0be7ccfa459a3f70fe8e3e31">reset</a> ()</td></tr>
<tr class="separator:ae5253fde0be7ccfa459a3f70fe8e3e31 inherit pub_methods_classIir_1_1CascadeStages"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a56d24da4cf09f898b534b1b05578244d inherit pub_methods_classIir_1_1CascadeStages"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classIir_1_1CascadeStages.html#a56d24da4cf09f898b534b1b05578244d">setup</a> (const double(&amp;sosCoefficients)[MaxStages][6])</td></tr>
<tr class="separator:a56d24da4cf09f898b534b1b05578244d inherit pub_methods_classIir_1_1CascadeStages"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa18e9abcaac65fd21be31ee859add3bb inherit pub_methods_classIir_1_1CascadeStages"><td class="memTemplParams" colspan="2">template&lt;typename Sample &gt; </td></tr>
<tr class="memitem:aa18e9abcaac65fd21be31ee859add3bb inherit pub_methods_classIir_1_1CascadeStages"><td class="memTemplItemLeft" align="right" valign="top">Sample&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="classIir_1_1CascadeStages.html#aa18e9abcaac65fd21be31ee859add3bb">filter</a> (const Sample in)</td></tr>
<tr class="separator:aa18e9abcaac65fd21be31ee859add3bb inherit pub_methods_classIir_1_1CascadeStages"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a034a9be8ae590b814c8499898a93987a inherit pub_methods_classIir_1_1CascadeStages"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="structIir_1_1Cascade_1_1Storage.html">Cascade::Storage</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classIir_1_1CascadeStages.html#a034a9be8ae590b814c8499898a93987a">getCascadeStorage</a> ()</td></tr>
<tr class="separator:a034a9be8ae590b814c8499898a93987a inherit pub_methods_classIir_1_1CascadeStages"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><h3>template&lt;int FilterOrder = 4, class StateType = DirectFormII&gt;<br />
struct Iir::ChebyshevI::HighShelf&lt; FilterOrder, StateType &gt;</h3>

<p><a class="el" href="namespaceIir_1_1ChebyshevI.html">ChebyshevI</a> high shelf filter. Specified gain in the passband. Otherwise 0 dB. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">FilterOrder</td><td>Reserves memory for a filter of the order FilterOrder </td></tr>
    <tr><td class="paramname">StateType</td><td>The filter topology: <a class="el" href="classIir_1_1DirectFormI.html">DirectFormI</a>, <a class="el" href="classIir_1_1DirectFormII.html">DirectFormII</a>, ... </td></tr>
  </table>
  </dd>
</dl>
</div><h2 class="groupheader">Member Function Documentation</h2>
<a id="a9fe4f6cee3b4b1aa4688ea21d3f5202f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9fe4f6cee3b4b1aa4688ea21d3f5202f">&#9670;&nbsp;</a></span>setup() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int FilterOrder = 4, class StateType  = DirectFormII&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void <a class="el" href="structIir_1_1ChebyshevI_1_1HighShelf.html">Iir::ChebyshevI::HighShelf</a>&lt; FilterOrder, StateType &gt;::setup </td>
          <td>(</td>
          <td class="paramtype">double&#160;</td>
          <td class="paramname"><em>sampleRate</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">double&#160;</td>
          <td class="paramname"><em>cutoffFrequency</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">double&#160;</td>
          <td class="paramname"><em>gainDb</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">double&#160;</td>
          <td class="paramname"><em>rippleDb</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Calculates the coefficients of the filter at the order FilterOrder </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">sampleRate</td><td>Sampling rate </td></tr>
    <tr><td class="paramname">cutoffFrequency</td><td>Cutoff frequency. </td></tr>
    <tr><td class="paramname">gainDb</td><td>Gain in the passband </td></tr>
    <tr><td class="paramname">rippleDb</td><td>Permitted ripples in dB in the passband </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a6e8901d8a4c2b804f4a4df780f1937cc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6e8901d8a4c2b804f4a4df780f1937cc">&#9670;&nbsp;</a></span>setup() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int FilterOrder = 4, class StateType  = DirectFormII&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void <a class="el" href="structIir_1_1ChebyshevI_1_1HighShelf.html">Iir::ChebyshevI::HighShelf</a>&lt; FilterOrder, StateType &gt;::setup </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>reqOrder</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">double&#160;</td>
          <td class="paramname"><em>sampleRate</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">double&#160;</td>
          <td class="paramname"><em>cutoffFrequency</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">double&#160;</td>
          <td class="paramname"><em>gainDb</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">double&#160;</td>
          <td class="paramname"><em>rippleDb</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Calculates the coefficients of the filter at specified order </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">reqOrder</td><td>Actual order for the filter calculations </td></tr>
    <tr><td class="paramname">sampleRate</td><td>Sampling rate </td></tr>
    <tr><td class="paramname">cutoffFrequency</td><td>Cutoff frequency. </td></tr>
    <tr><td class="paramname">gainDb</td><td>Gain in the passband </td></tr>
    <tr><td class="paramname">rippleDb</td><td>Permitted ripples in dB in the passband </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="aa078bdf604ac9feeedd282174bd0cea9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa078bdf604ac9feeedd282174bd0cea9">&#9670;&nbsp;</a></span>setupN() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int FilterOrder = 4, class StateType  = DirectFormII&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void <a class="el" href="structIir_1_1ChebyshevI_1_1HighShelf.html">Iir::ChebyshevI::HighShelf</a>&lt; FilterOrder, StateType &gt;::setupN </td>
          <td>(</td>
          <td class="paramtype">double&#160;</td>
          <td class="paramname"><em>cutoffFrequency</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">double&#160;</td>
          <td class="paramname"><em>gainDb</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">double&#160;</td>
          <td class="paramname"><em>rippleDb</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Calculates the coefficients of the filter at the order FilterOrder </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">cutoffFrequency</td><td>Normalised cutoff frequency (0..1/2) </td></tr>
    <tr><td class="paramname">gainDb</td><td>Gain in the passband </td></tr>
    <tr><td class="paramname">rippleDb</td><td>Permitted ripples in dB in the passband </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="adbfa0e3487393075e3f178d50293f0bb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#adbfa0e3487393075e3f178d50293f0bb">&#9670;&nbsp;</a></span>setupN() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int FilterOrder = 4, class StateType  = DirectFormII&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void <a class="el" href="structIir_1_1ChebyshevI_1_1HighShelf.html">Iir::ChebyshevI::HighShelf</a>&lt; FilterOrder, StateType &gt;::setupN </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>reqOrder</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">double&#160;</td>
          <td class="paramname"><em>cutoffFrequency</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">double&#160;</td>
          <td class="paramname"><em>gainDb</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">double&#160;</td>
          <td class="paramname"><em>rippleDb</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Calculates the coefficients of the filter at specified order </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">reqOrder</td><td>Actual order for the filter calculations </td></tr>
    <tr><td class="paramname">cutoffFrequency</td><td>Normalised cutoff frequency (0..1/2) </td></tr>
    <tr><td class="paramname">gainDb</td><td>Gain in the passband </td></tr>
    <tr><td class="paramname">rippleDb</td><td>Permitted ripples in dB in the passband </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li>iir/<a class="el" href="ChebyshevI_8h_source.html">ChebyshevI.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Fri Jan 6 2023 23:53:41 for iir1 by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
