/********************************************************************************
** Form generated from reading UI file 'onepass.ui'
**
** Created by: Qt User Interface Compiler version 5.15.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_ONEPASS_H
#define UI_ONEPASS_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QDoubleSpinBox>
#include <QtWidgets/QFrame>
#include <QtWidgets/QLabel>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QRadioButton>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_onePass
{
public:
    QVBoxLayout *verticalLayout;
    QFrame *frame_2;
    QRadioButton *rB_noise;
    QLabel *label_4;
    QDoubleSpinBox *dSB_value;
    QLabel *label_5;
    QDoubleSpinBox *dSB_time;
    QFrame *frame;
    QComboBox *cB_equipment;
    QLabel *label_7;
    QRadioButton *rB_end;
    QComboBox *cB_out;
    QLabel *label_3;
    QComboBox *cB_signal;
    QLabel *label;
    QLabel *label_6;
    QComboBox *cB_trigger;
    QWidget *widget;
    QPushButton *pB_start;

    void setupUi(QWidget *onePass)
    {
        if (onePass->objectName().isEmpty())
            onePass->setObjectName(QString::fromUtf8("onePass"));
        onePass->resize(1007, 675);
        verticalLayout = new QVBoxLayout(onePass);
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        frame_2 = new QFrame(onePass);
        frame_2->setObjectName(QString::fromUtf8("frame_2"));
        frame_2->setFrameShape(QFrame::StyledPanel);
        frame_2->setFrameShadow(QFrame::Raised);
        rB_noise = new QRadioButton(frame_2);
        rB_noise->setObjectName(QString::fromUtf8("rB_noise"));
        rB_noise->setGeometry(QRect(30, 30, 101, 21));
        label_4 = new QLabel(frame_2);
        label_4->setObjectName(QString::fromUtf8("label_4"));
        label_4->setGeometry(QRect(30, 110, 91, 16));
        dSB_value = new QDoubleSpinBox(frame_2);
        dSB_value->setObjectName(QString::fromUtf8("dSB_value"));
        dSB_value->setGeometry(QRect(40, 140, 62, 22));
        label_5 = new QLabel(frame_2);
        label_5->setObjectName(QString::fromUtf8("label_5"));
        label_5->setGeometry(QRect(130, 110, 91, 16));
        dSB_time = new QDoubleSpinBox(frame_2);
        dSB_time->setObjectName(QString::fromUtf8("dSB_time"));
        dSB_time->setGeometry(QRect(140, 140, 62, 22));

        verticalLayout->addWidget(frame_2);

        frame = new QFrame(onePass);
        frame->setObjectName(QString::fromUtf8("frame"));
        frame->setFrameShape(QFrame::StyledPanel);
        frame->setFrameShadow(QFrame::Raised);
        cB_equipment = new QComboBox(frame);
        cB_equipment->addItem(QString());
        cB_equipment->addItem(QString());
        cB_equipment->setObjectName(QString::fromUtf8("cB_equipment"));
        cB_equipment->setGeometry(QRect(160, 130, 69, 22));
        label_7 = new QLabel(frame);
        label_7->setObjectName(QString::fromUtf8("label_7"));
        label_7->setGeometry(QRect(170, 90, 71, 31));
        rB_end = new QRadioButton(frame);
        rB_end->setObjectName(QString::fromUtf8("rB_end"));
        rB_end->setGeometry(QRect(150, 50, 89, 16));
        cB_out = new QComboBox(frame);
        cB_out->addItem(QString());
        cB_out->addItem(QString());
        cB_out->addItem(QString());
        cB_out->addItem(QString());
        cB_out->addItem(QString());
        cB_out->addItem(QString());
        cB_out->addItem(QString());
        cB_out->setObjectName(QString::fromUtf8("cB_out"));
        cB_out->setGeometry(QRect(8, 50, 101, 22));
        label_3 = new QLabel(frame);
        label_3->setObjectName(QString::fromUtf8("label_3"));
        label_3->setGeometry(QRect(20, 90, 71, 31));
        cB_signal = new QComboBox(frame);
        cB_signal->addItem(QString());
        cB_signal->addItem(QString());
        cB_signal->addItem(QString());
        cB_signal->addItem(QString());
        cB_signal->addItem(QString());
        cB_signal->addItem(QString());
        cB_signal->addItem(QString());
        cB_signal->addItem(QString());
        cB_signal->addItem(QString());
        cB_signal->setObjectName(QString::fromUtf8("cB_signal"));
        cB_signal->setGeometry(QRect(20, 130, 69, 22));
        label = new QLabel(frame);
        label->setObjectName(QString::fromUtf8("label"));
        label->setGeometry(QRect(20, 10, 71, 31));

        verticalLayout->addWidget(frame);

        label_6 = new QLabel(onePass);
        label_6->setObjectName(QString::fromUtf8("label_6"));

        verticalLayout->addWidget(label_6);

        cB_trigger = new QComboBox(onePass);
        cB_trigger->addItem(QString());
        cB_trigger->addItem(QString());
        cB_trigger->addItem(QString());
        cB_trigger->setObjectName(QString::fromUtf8("cB_trigger"));

        verticalLayout->addWidget(cB_trigger);

        widget = new QWidget(onePass);
        widget->setObjectName(QString::fromUtf8("widget"));

        verticalLayout->addWidget(widget);

        pB_start = new QPushButton(onePass);
        pB_start->setObjectName(QString::fromUtf8("pB_start"));

        verticalLayout->addWidget(pB_start);

        frame_2->raise();
        frame->raise();
        pB_start->raise();
        label_6->raise();
        cB_trigger->raise();
        widget->raise();

        retranslateUi(onePass);

        QMetaObject::connectSlotsByName(onePass);
    } // setupUi

    void retranslateUi(QWidget *onePass)
    {
        onePass->setWindowTitle(QCoreApplication::translate("onePass", "Form", nullptr));
        rB_noise->setText(QCoreApplication::translate("onePass", "\351\242\204\346\222\255\345\231\252\345\243\260\357\274\237", nullptr));
        label_4->setText(QCoreApplication::translate("onePass", "\345\231\252\345\243\260\345\271\205\345\200\274\357\274\210V\357\274\211", nullptr));
        label_5->setText(QCoreApplication::translate("onePass", "\345\231\252\345\243\260\346\227\266\351\225\277\357\274\210S\357\274\211", nullptr));
        cB_equipment->setItemText(0, QString());
        cB_equipment->setItemText(1, QCoreApplication::translate("onePass", "Mouth_0", nullptr));

        label_7->setText(QCoreApplication::translate("onePass", "\346\277\200\345\212\261\350\256\276\345\244\207", nullptr));
        rB_end->setText(QCoreApplication::translate("onePass", "\347\255\211\345\276\205\350\277\220\350\241\214\347\273\223\346\235\237", nullptr));
        cB_out->setItemText(0, QCoreApplication::translate("onePass", "<65535>", nullptr));
        cB_out->setItemText(1, QCoreApplication::translate("onePass", "PM 6143-AO1", nullptr));
        cB_out->setItemText(2, QCoreApplication::translate("onePass", "PM 6143-AO2", nullptr));
        cB_out->setItemText(3, QCoreApplication::translate("onePass", "PM 6143-AO3", nullptr));
        cB_out->setItemText(4, QCoreApplication::translate("onePass", "PM 6143-AO4", nullptr));
        cB_out->setItemText(5, QCoreApplication::translate("onePass", "U 982-AO1", nullptr));
        cB_out->setItemText(6, QCoreApplication::translate("onePass", "U 982-AO2", nullptr));

        label_3->setText(QCoreApplication::translate("onePass", "\346\277\200\345\212\261\344\277\241\345\217\267", nullptr));
        cB_signal->setItemText(0, QCoreApplication::translate("onePass", "<65535>", nullptr));
        cB_signal->setItemText(1, QCoreApplication::translate("onePass", "1K", nullptr));
        cB_signal->setItemText(2, QCoreApplication::translate("onePass", "48K", nullptr));
        cB_signal->setItemText(3, QCoreApplication::translate("onePass", "ANC Tool sig", nullptr));
        cB_signal->setItemText(4, QCoreApplication::translate("onePass", "BNC Sig", nullptr));
        cB_signal->setItemText(5, QCoreApplication::translate("onePass", "MIC", nullptr));
        cB_signal->setItemText(6, QCoreApplication::translate("onePass", "SPK", nullptr));
        cB_signal->setItemText(7, QCoreApplication::translate("onePass", "\345\226\207\345\217\255", nullptr));
        cB_signal->setItemText(8, QCoreApplication::translate("onePass", "\351\272\246\345\205\213\351\243\216", nullptr));

        label->setText(QCoreApplication::translate("onePass", "\350\276\223\345\207\272\351\200\232\351\201\223\357\274\232", nullptr));
        label_6->setText(QCoreApplication::translate("onePass", "\350\247\246\345\217\221\347\261\273\345\236\213", nullptr));
        cB_trigger->setItemText(0, QCoreApplication::translate("onePass", "\344\270\215\350\247\246\345\217\221", nullptr));
        cB_trigger->setItemText(1, QCoreApplication::translate("onePass", "\345\206\205\351\203\250\350\247\246\345\217\221", nullptr));
        cB_trigger->setItemText(2, QCoreApplication::translate("onePass", "\345\244\226\351\203\250\350\247\246\345\217\221", nullptr));

        pB_start->setText(QCoreApplication::translate("onePass", "\345\274\200\345\247\213", nullptr));
    } // retranslateUi

};

namespace Ui {
    class onePass: public Ui_onePass {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_ONEPASS_H
