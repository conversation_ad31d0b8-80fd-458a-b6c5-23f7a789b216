\hypertarget{classIir_1_1PoleFilterBase}{}\doxysubsection{Iir\+::Pole\+Filter\+Base$<$ Analog\+Prototype $>$ Class Template Reference}
\label{classIir_1_1PoleFilterBase}\index{Iir::PoleFilterBase$<$ AnalogPrototype $>$@{Iir::PoleFilterBase$<$ AnalogPrototype $>$}}


{\ttfamily \#include $<$Pole\+Filter.\+h$>$}

Inheritance diagram for Iir\+::Pole\+Filter\+Base$<$ Analog\+Prototype $>$\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=3.000000cm]{classIir_1_1PoleFilterBase}
\end{center}
\end{figure}
\doxysubsubsection*{Additional Inherited Members}


\doxysubsubsection{Detailed Description}
\subsubsection*{template$<$class Analog\+Prototype$>$\newline
class Iir\+::\+Pole\+Filter\+Base$<$ Analog\+Prototype $>$}

Serves a container to hold the analog prototype and the digital pole/zero layout. 

The documentation for this class was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
iir/Pole\+Filter.\+h\end{DoxyCompactItemize}
