\hypertarget{structIir_1_1RBJ_1_1BandPass1}{}\doxysubsection{Iir\+::RBJ\+::Band\+Pass1 Struct Reference}
\label{structIir_1_1RBJ_1_1BandPass1}\index{Iir::RBJ::BandPass1@{Iir::RBJ::BandPass1}}


{\ttfamily \#include $<$RBJ.\+h$>$}

Inheritance diagram for Iir\+::RBJ\+::Band\+Pass1\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=3.000000cm]{structIir_1_1RBJ_1_1BandPass1}
\end{center}
\end{figure}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
void \mbox{\hyperlink{structIir_1_1RBJ_1_1BandPass1_a3af22e73e4809afa1349aeeee2d6452b}{setupN}} (double center\+Frequency, double band\+Width)
\item 
void \mbox{\hyperlink{structIir_1_1RBJ_1_1BandPass1_a82ba843c68435a5a09cc16fdbf358afc}{setup}} (double sample\+Rate, double center\+Frequency, double band\+Width)
\end{DoxyCompactItemize}


\doxysubsubsection{Detailed Description}
Bandpass with constant skirt gain 

\doxysubsubsection{Member Function Documentation}
\mbox{\Hypertarget{structIir_1_1RBJ_1_1BandPass1_a82ba843c68435a5a09cc16fdbf358afc}\label{structIir_1_1RBJ_1_1BandPass1_a82ba843c68435a5a09cc16fdbf358afc}} 
\index{Iir::RBJ::BandPass1@{Iir::RBJ::BandPass1}!setup@{setup}}
\index{setup@{setup}!Iir::RBJ::BandPass1@{Iir::RBJ::BandPass1}}
\doxyparagraph{\texorpdfstring{setup()}{setup()}}
{\footnotesize\ttfamily void Iir\+::\+RBJ\+::\+Band\+Pass1\+::setup (\begin{DoxyParamCaption}\item[{double}]{sample\+Rate,  }\item[{double}]{center\+Frequency,  }\item[{double}]{band\+Width }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients 
\begin{DoxyParams}{Parameters}
{\em sample\+Rate} & Sampling rate \\
\hline
{\em center\+Frequency} & Center frequency of the bandpass \\
\hline
{\em band\+Width} & Bandwidth in octaves \\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{structIir_1_1RBJ_1_1BandPass1_a3af22e73e4809afa1349aeeee2d6452b}\label{structIir_1_1RBJ_1_1BandPass1_a3af22e73e4809afa1349aeeee2d6452b}} 
\index{Iir::RBJ::BandPass1@{Iir::RBJ::BandPass1}!setupN@{setupN}}
\index{setupN@{setupN}!Iir::RBJ::BandPass1@{Iir::RBJ::BandPass1}}
\doxyparagraph{\texorpdfstring{setupN()}{setupN()}}
{\footnotesize\ttfamily void Iir\+::\+RBJ\+::\+Band\+Pass1\+::setupN (\begin{DoxyParamCaption}\item[{double}]{center\+Frequency,  }\item[{double}]{band\+Width }\end{DoxyParamCaption})}

Calculates the coefficients 
\begin{DoxyParams}{Parameters}
{\em center\+Frequency} & Center frequency of the bandpass \\
\hline
{\em band\+Width} & Bandwidth in octaves \\
\hline
\end{DoxyParams}


The documentation for this struct was generated from the following files\+:\begin{DoxyCompactItemize}
\item 
iir/RBJ.\+h\item 
iir/RBJ.\+cpp\end{DoxyCompactItemize}
