// mdiwindowmanager.h
#ifndef MDIWINDOWMANAGER_H
#define MDIWINDOWMANAGER_H

#include <QMdiArea>          // 继承 QMdiArea，用于多文档界面管理
#include <QMdiSubWindow>     // 子窗口类
#include <QMap>              // 用于存储映射（名字 -> 子窗口，子窗口 -> 位置等）
#include <QRect>             // 用于存储位置和大小
#include <QEvent>            // 事件处理
#include <QCloseEvent>       // 关闭事件
#include <QMoveEvent>        // 移动事件
#include <QDebug>            // 调试输出
#include<QMessageBox>
#include "Chart/chart.h"

// 自定义 QMdiSubWindow 子类，用于处理移动事件和锁定
class CustomMdiSubWindow : public QMdiSubWindow
{
    Q_OBJECT  // 添加 Q_OBJECT 宏以支持 qobject_cast 和元对象系统
public:
    CustomMdiSubWindow(QWidget *parent = nullptr) : QMdiSubWindow(parent) {}
    bool locked = false; // 是否锁定
    bool inMoveEvent = false;// 防止递归

signals:
    void subClose();

protected:
    void moveEvent(QMoveEvent *event) override;
    void closeEvent(QCloseEvent *event) override;

};


class MdiWindowManager : public QMdiArea
{
    Q_OBJECT
public:
    explicit MdiWindowManager(QWidget *parent = nullptr);

    //添加一个管理的子窗口
    CustomMdiSubWindow* addWindow(const QString &subWindowName, const QRect &rect={0,0,600,400});

    //根据名字获取子窗口指针
    CustomMdiSubWindow* getWindowByName(const QString &subWindowName);

    //根据名字删除子窗口及其对应的事件
    void clearName(const QString &subWindowName);

    //根据名字获取子窗口是否显示
    bool getShowByName(const QString &subWindowName);

    //根据名字设置子窗口是否显示
    void setShowByName(const QString &subWindowName,bool show);

    //更改名字
    bool updateTile(QString newTile,QString oldTile);

    //更新指定子窗口内部 Chart 的曲线数据
    void updateWaveform(const QVector<double>& x, const QVector<double>& y, const QString &widgetName,const QString &curveName);

    //一键显示所有为true的子窗口
    void showAll();

    //一键隐藏所有子窗口
    void hideAll();

    //一键锁定/解锁所有子窗口（锁定时不可移动）
    void lockAll(bool lock);

    //一键清除所有子窗口内部 Chart 的曲线
    void clearAllCurves();

    //显示特定子窗口（根据名字）
    void showWindow(const QString &name);

protected:
    //重写关闭事件，移除事件过滤器。
    void closeEvent(QCloseEvent *event) override;

private slots:
    //槽函数：处理子窗口激活信号，突出显示激活窗口（橙色标题），其他恢复紫色。
    void onSubWindowActivated(QMdiSubWindow *window);

public:
    // 清除所有子窗口
    void clearAllSubWindows();

private:
    // 私有成员变量
    QMap<QString, QPair<CustomMdiSubWindow*,bool>> subWindowsName;         // 名字 -> 子窗口
    QMap<CustomMdiSubWindow*,Chart*> subWindowsChart;  //子窗口->Chart
    bool globalLock = false;                             // 全局锁定标志
};

#endif // MDIWINDOWMANAGER_H
