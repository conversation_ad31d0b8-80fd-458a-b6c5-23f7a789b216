/****************************************************************************
** Meta object code from reading C++ file 'miccalibrationform.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.15.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../MainSet/sensor/miccalibrationform.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'miccalibrationform.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.15.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_MicCalibrationForm_t {
    QByteArrayData data[16];
    char stringdata0[282];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_MicCalibrationForm_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_MicCalibrationForm_t qt_meta_stringdata_MicCalibrationForm = {
    {
QT_MOC_LITERAL(0, 0, 18), // "MicCalibrationForm"
QT_MOC_LITERAL(1, 19, 10), // "SendResult"
QT_MOC_LITERAL(2, 30, 0), // ""
QT_MOC_LITERAL(3, 31, 6), // "sensor"
QT_MOC_LITERAL(4, 38, 13), // "Result_mVPa__"
QT_MOC_LITERAL(5, 52, 11), // "Result_dB__"
QT_MOC_LITERAL(6, 64, 5), // "FRE__"
QT_MOC_LITERAL(7, 70, 40), // "on_CalibratorSPLlineEdit_edit..."
QT_MOC_LITERAL(8, 111, 40), // "on_CalibratorFrelineEdit_edit..."
QT_MOC_LITERAL(9, 152, 25), // "on_StarStopButton_clicked"
QT_MOC_LITERAL(10, 178, 10), // "updatePlot"
QT_MOC_LITERAL(11, 189, 27), // "AudioProcessor::ChannelType"
QT_MOC_LITERAL(12, 217, 4), // "type"
QT_MOC_LITERAL(13, 222, 11), // "channelName"
QT_MOC_LITERAL(14, 234, 25), // "on_SavepushButton_clicked"
QT_MOC_LITERAL(15, 260, 21) // "on_ExitButton_clicked"

    },
    "MicCalibrationForm\0SendResult\0\0sensor\0"
    "Result_mVPa__\0Result_dB__\0FRE__\0"
    "on_CalibratorSPLlineEdit_editingFinished\0"
    "on_CalibratorFrelineEdit_editingFinished\0"
    "on_StarStopButton_clicked\0updatePlot\0"
    "AudioProcessor::ChannelType\0type\0"
    "channelName\0on_SavepushButton_clicked\0"
    "on_ExitButton_clicked"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_MicCalibrationForm[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       7,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       1,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    4,   49,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       7,    0,   58,    2, 0x08 /* Private */,
       8,    0,   59,    2, 0x08 /* Private */,
       9,    0,   60,    2, 0x08 /* Private */,
      10,    2,   61,    2, 0x08 /* Private */,
      14,    0,   66,    2, 0x08 /* Private */,
      15,    0,   67,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::QString, QMetaType::Double, QMetaType::Double, QMetaType::Double,    3,    4,    5,    6,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 11, QMetaType::QString,   12,   13,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void MicCalibrationForm::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<MicCalibrationForm *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->SendResult((*reinterpret_cast< QString(*)>(_a[1])),(*reinterpret_cast< double(*)>(_a[2])),(*reinterpret_cast< double(*)>(_a[3])),(*reinterpret_cast< double(*)>(_a[4]))); break;
        case 1: _t->on_CalibratorSPLlineEdit_editingFinished(); break;
        case 2: _t->on_CalibratorFrelineEdit_editingFinished(); break;
        case 3: _t->on_StarStopButton_clicked(); break;
        case 4: _t->updatePlot((*reinterpret_cast< AudioProcessor::ChannelType(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 5: _t->on_SavepushButton_clicked(); break;
        case 6: _t->on_ExitButton_clicked(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (MicCalibrationForm::*)(QString , double , double , double );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&MicCalibrationForm::SendResult)) {
                *result = 0;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject MicCalibrationForm::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_meta_stringdata_MicCalibrationForm.data,
    qt_meta_data_MicCalibrationForm,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *MicCalibrationForm::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *MicCalibrationForm::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_MicCalibrationForm.stringdata0))
        return static_cast<void*>(this);
    if (!strcmp(_clname, "base_lib"))
        return static_cast< base_lib*>(this);
    return QWidget::qt_metacast(_clname);
}

int MicCalibrationForm::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 7)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 7;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 7)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 7;
    }
    return _id;
}

// SIGNAL 0
void MicCalibrationForm::SendResult(QString _t1, double _t2, double _t3, double _t4)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t3))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t4))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
