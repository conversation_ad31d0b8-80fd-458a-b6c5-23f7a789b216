<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>iir1: Namespace List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">iir1
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">Namespace List</div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock">Here is a list of all documented namespaces with brief descriptions:</div><div class="directory">
<div class="levels">[detail level <span onclick="javascript:toggleLevel(1);">1</span><span onclick="javascript:toggleLevel(2);">2</span><span onclick="javascript:toggleLevel(3);">3</span>]</div><table class="directory">
<tr id="row_0_" class="even"><td class="entry"><span style="width:0px;display:inline-block;">&#160;</span><span id="arr_0_" class="arrow" onclick="toggleFolder('0_')">&#9660;</span><span class="icona"><span class="icon">N</span></span><a class="el" href="namespaceIir.html" target="_self">Iir</a></td><td class="desc"></td></tr>
<tr id="row_0_0_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span id="arr_0_0_" class="arrow" onclick="toggleFolder('0_0_')">&#9660;</span><span class="icona"><span class="icon">N</span></span><a class="el" href="namespaceIir_1_1Butterworth.html" target="_self">Butterworth</a></td><td class="desc"></td></tr>
<tr id="row_0_0_0_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classIir_1_1Butterworth_1_1AnalogLowPass.html" target="_self">AnalogLowPass</a></td><td class="desc"></td></tr>
<tr id="row_0_0_1_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classIir_1_1Butterworth_1_1AnalogLowShelf.html" target="_self">AnalogLowShelf</a></td><td class="desc"></td></tr>
<tr id="row_0_0_2_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1Butterworth_1_1LowPassBase.html" target="_self">LowPassBase</a></td><td class="desc"></td></tr>
<tr id="row_0_0_3_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1Butterworth_1_1HighPassBase.html" target="_self">HighPassBase</a></td><td class="desc"></td></tr>
<tr id="row_0_0_4_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1Butterworth_1_1BandPassBase.html" target="_self">BandPassBase</a></td><td class="desc"></td></tr>
<tr id="row_0_0_5_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1Butterworth_1_1BandStopBase.html" target="_self">BandStopBase</a></td><td class="desc"></td></tr>
<tr id="row_0_0_6_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1Butterworth_1_1LowShelfBase.html" target="_self">LowShelfBase</a></td><td class="desc"></td></tr>
<tr id="row_0_0_7_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1Butterworth_1_1HighShelfBase.html" target="_self">HighShelfBase</a></td><td class="desc"></td></tr>
<tr id="row_0_0_8_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1Butterworth_1_1BandShelfBase.html" target="_self">BandShelfBase</a></td><td class="desc"></td></tr>
<tr id="row_0_0_9_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1Butterworth_1_1LowPass.html" target="_self">LowPass</a></td><td class="desc"></td></tr>
<tr id="row_0_0_10_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1Butterworth_1_1HighPass.html" target="_self">HighPass</a></td><td class="desc"></td></tr>
<tr id="row_0_0_11_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1Butterworth_1_1BandPass.html" target="_self">BandPass</a></td><td class="desc"></td></tr>
<tr id="row_0_0_12_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1Butterworth_1_1BandStop.html" target="_self">BandStop</a></td><td class="desc"></td></tr>
<tr id="row_0_0_13_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1Butterworth_1_1LowShelf.html" target="_self">LowShelf</a></td><td class="desc"></td></tr>
<tr id="row_0_0_14_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1Butterworth_1_1HighShelf.html" target="_self">HighShelf</a></td><td class="desc"></td></tr>
<tr id="row_0_0_15_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1Butterworth_1_1BandShelf.html" target="_self">BandShelf</a></td><td class="desc"></td></tr>
<tr id="row_0_1_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span id="arr_0_1_" class="arrow" onclick="toggleFolder('0_1_')">&#9660;</span><span class="icona"><span class="icon">N</span></span><a class="el" href="namespaceIir_1_1ChebyshevI.html" target="_self">ChebyshevI</a></td><td class="desc"></td></tr>
<tr id="row_0_1_0_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classIir_1_1ChebyshevI_1_1AnalogLowPass.html" target="_self">AnalogLowPass</a></td><td class="desc"></td></tr>
<tr id="row_0_1_1_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classIir_1_1ChebyshevI_1_1AnalogLowShelf.html" target="_self">AnalogLowShelf</a></td><td class="desc"></td></tr>
<tr id="row_0_1_2_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevI_1_1LowPassBase.html" target="_self">LowPassBase</a></td><td class="desc"></td></tr>
<tr id="row_0_1_3_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevI_1_1HighPassBase.html" target="_self">HighPassBase</a></td><td class="desc"></td></tr>
<tr id="row_0_1_4_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevI_1_1BandPassBase.html" target="_self">BandPassBase</a></td><td class="desc"></td></tr>
<tr id="row_0_1_5_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevI_1_1BandStopBase.html" target="_self">BandStopBase</a></td><td class="desc"></td></tr>
<tr id="row_0_1_6_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevI_1_1LowShelfBase.html" target="_self">LowShelfBase</a></td><td class="desc"></td></tr>
<tr id="row_0_1_7_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevI_1_1HighShelfBase.html" target="_self">HighShelfBase</a></td><td class="desc"></td></tr>
<tr id="row_0_1_8_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevI_1_1BandShelfBase.html" target="_self">BandShelfBase</a></td><td class="desc"></td></tr>
<tr id="row_0_1_9_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevI_1_1LowPass.html" target="_self">LowPass</a></td><td class="desc"></td></tr>
<tr id="row_0_1_10_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevI_1_1HighPass.html" target="_self">HighPass</a></td><td class="desc"></td></tr>
<tr id="row_0_1_11_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevI_1_1BandPass.html" target="_self">BandPass</a></td><td class="desc"></td></tr>
<tr id="row_0_1_12_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevI_1_1BandStop.html" target="_self">BandStop</a></td><td class="desc"></td></tr>
<tr id="row_0_1_13_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevI_1_1LowShelf.html" target="_self">LowShelf</a></td><td class="desc"></td></tr>
<tr id="row_0_1_14_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevI_1_1HighShelf.html" target="_self">HighShelf</a></td><td class="desc"></td></tr>
<tr id="row_0_1_15_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevI_1_1BandShelf.html" target="_self">BandShelf</a></td><td class="desc"></td></tr>
<tr id="row_0_2_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span id="arr_0_2_" class="arrow" onclick="toggleFolder('0_2_')">&#9660;</span><span class="icona"><span class="icon">N</span></span><a class="el" href="namespaceIir_1_1ChebyshevII.html" target="_self">ChebyshevII</a></td><td class="desc"></td></tr>
<tr id="row_0_2_0_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classIir_1_1ChebyshevII_1_1AnalogLowPass.html" target="_self">AnalogLowPass</a></td><td class="desc"></td></tr>
<tr id="row_0_2_1_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classIir_1_1ChebyshevII_1_1AnalogLowShelf.html" target="_self">AnalogLowShelf</a></td><td class="desc"></td></tr>
<tr id="row_0_2_2_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevII_1_1LowPassBase.html" target="_self">LowPassBase</a></td><td class="desc"></td></tr>
<tr id="row_0_2_3_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevII_1_1HighPassBase.html" target="_self">HighPassBase</a></td><td class="desc"></td></tr>
<tr id="row_0_2_4_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevII_1_1BandPassBase.html" target="_self">BandPassBase</a></td><td class="desc"></td></tr>
<tr id="row_0_2_5_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevII_1_1BandStopBase.html" target="_self">BandStopBase</a></td><td class="desc"></td></tr>
<tr id="row_0_2_6_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevII_1_1LowShelfBase.html" target="_self">LowShelfBase</a></td><td class="desc"></td></tr>
<tr id="row_0_2_7_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevII_1_1HighShelfBase.html" target="_self">HighShelfBase</a></td><td class="desc"></td></tr>
<tr id="row_0_2_8_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevII_1_1BandShelfBase.html" target="_self">BandShelfBase</a></td><td class="desc"></td></tr>
<tr id="row_0_2_9_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevII_1_1LowPass.html" target="_self">LowPass</a></td><td class="desc"></td></tr>
<tr id="row_0_2_10_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevII_1_1HighPass.html" target="_self">HighPass</a></td><td class="desc"></td></tr>
<tr id="row_0_2_11_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevII_1_1BandPass.html" target="_self">BandPass</a></td><td class="desc"></td></tr>
<tr id="row_0_2_12_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevII_1_1BandStop.html" target="_self">BandStop</a></td><td class="desc"></td></tr>
<tr id="row_0_2_13_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevII_1_1LowShelf.html" target="_self">LowShelf</a></td><td class="desc"></td></tr>
<tr id="row_0_2_14_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevII_1_1HighShelf.html" target="_self">HighShelf</a></td><td class="desc"></td></tr>
<tr id="row_0_2_15_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ChebyshevII_1_1BandShelf.html" target="_self">BandShelf</a></td><td class="desc"></td></tr>
<tr id="row_0_3_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span id="arr_0_3_" class="arrow" onclick="toggleFolder('0_3_')">&#9660;</span><span class="icona"><span class="icon">N</span></span><a class="el" href="namespaceIir_1_1Custom.html" target="_self">Custom</a></td><td class="desc"></td></tr>
<tr id="row_0_3_0_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1Custom_1_1OnePole.html" target="_self">OnePole</a></td><td class="desc"></td></tr>
<tr id="row_0_3_1_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1Custom_1_1TwoPole.html" target="_self">TwoPole</a></td><td class="desc"></td></tr>
<tr id="row_0_3_2_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1Custom_1_1SOSCascade.html" target="_self">SOSCascade</a></td><td class="desc"></td></tr>
<tr id="row_0_4_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span id="arr_0_4_" class="arrow" onclick="toggleFolder('0_4_')">&#9660;</span><span class="icona"><span class="icon">N</span></span><b>RBJ</b></td><td class="desc"></td></tr>
<tr id="row_0_4_0_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1RBJ_1_1RBJbase.html" target="_self">RBJbase</a></td><td class="desc"></td></tr>
<tr id="row_0_4_1_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1RBJ_1_1LowPass.html" target="_self">LowPass</a></td><td class="desc"></td></tr>
<tr id="row_0_4_2_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1RBJ_1_1HighPass.html" target="_self">HighPass</a></td><td class="desc"></td></tr>
<tr id="row_0_4_3_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1RBJ_1_1BandPass1.html" target="_self">BandPass1</a></td><td class="desc"></td></tr>
<tr id="row_0_4_4_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1RBJ_1_1BandPass2.html" target="_self">BandPass2</a></td><td class="desc"></td></tr>
<tr id="row_0_4_5_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1RBJ_1_1BandStop.html" target="_self">BandStop</a></td><td class="desc"></td></tr>
<tr id="row_0_4_6_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1RBJ_1_1IIRNotch.html" target="_self">IIRNotch</a></td><td class="desc"></td></tr>
<tr id="row_0_4_7_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1RBJ_1_1LowShelf.html" target="_self">LowShelf</a></td><td class="desc"></td></tr>
<tr id="row_0_4_8_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1RBJ_1_1HighShelf.html" target="_self">HighShelf</a></td><td class="desc"></td></tr>
<tr id="row_0_4_9_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1RBJ_1_1BandShelf.html" target="_self">BandShelf</a></td><td class="desc"></td></tr>
<tr id="row_0_4_10_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1RBJ_1_1AllPass.html" target="_self">AllPass</a></td><td class="desc"></td></tr>
<tr id="row_0_5_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classIir_1_1Biquad.html" target="_self">Biquad</a></td><td class="desc"></td></tr>
<tr id="row_0_6_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1BiquadPoleState.html" target="_self">BiquadPoleState</a></td><td class="desc"></td></tr>
<tr id="row_0_7_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span id="arr_0_7_" class="arrow" onclick="toggleFolder('0_7_')">&#9660;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classIir_1_1Cascade.html" target="_self">Cascade</a></td><td class="desc"></td></tr>
<tr id="row_0_7_0_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1Cascade_1_1Storage.html" target="_self">Storage</a></td><td class="desc"></td></tr>
<tr id="row_0_8_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classIir_1_1CascadeStages.html" target="_self">CascadeStages</a></td><td class="desc"></td></tr>
<tr id="row_0_9_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classIir_1_1LayoutBase.html" target="_self">LayoutBase</a></td><td class="desc"></td></tr>
<tr id="row_0_10_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classIir_1_1Layout.html" target="_self">Layout</a></td><td class="desc"></td></tr>
<tr id="row_0_11_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classIir_1_1PoleFilterBase2.html" target="_self">PoleFilterBase2</a></td><td class="desc"></td></tr>
<tr id="row_0_12_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classIir_1_1PoleFilterBase.html" target="_self">PoleFilterBase</a></td><td class="desc"></td></tr>
<tr id="row_0_13_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1PoleFilter.html" target="_self">PoleFilter</a></td><td class="desc"></td></tr>
<tr id="row_0_14_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classIir_1_1LowPassTransform.html" target="_self">LowPassTransform</a></td><td class="desc"></td></tr>
<tr id="row_0_15_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classIir_1_1HighPassTransform.html" target="_self">HighPassTransform</a></td><td class="desc"></td></tr>
<tr id="row_0_16_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classIir_1_1BandPassTransform.html" target="_self">BandPassTransform</a></td><td class="desc"></td></tr>
<tr id="row_0_17_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classIir_1_1BandStopTransform.html" target="_self">BandStopTransform</a></td><td class="desc"></td></tr>
<tr id="row_0_18_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classIir_1_1DirectFormI.html" target="_self">DirectFormI</a></td><td class="desc"></td></tr>
<tr id="row_0_19_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classIir_1_1DirectFormII.html" target="_self">DirectFormII</a></td><td class="desc"></td></tr>
<tr id="row_0_20_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classIir_1_1TransposedDirectFormII.html" target="_self">TransposedDirectFormII</a></td><td class="desc"></td></tr>
<tr id="row_0_21_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1ComplexPair.html" target="_self">ComplexPair</a></td><td class="desc"></td></tr>
<tr id="row_0_22_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="structIir_1_1PoleZeroPair.html" target="_self">PoleZeroPair</a></td><td class="desc"></td></tr>
</table>
</div><!-- directory -->
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Fri Jan 6 2023 23:53:47 for iir1 by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
