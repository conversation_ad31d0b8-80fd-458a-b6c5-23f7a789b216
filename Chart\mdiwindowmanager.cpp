// mdiwindowmanager.cpp
#include "mdiwindowmanager.h"
#include <QSize>  // 用于大小处理
#include <QPoint> // 用于位置处理

void CustomMdiSubWindow::moveEvent(QMoveEvent *event)
{

    if (locked) {
        if (inMoveEvent) {return;}// 如果已经在处理事件中，直接返回，避免递归
        inMoveEvent = true;  // 设置标志

        setGeometry(QRect(event->oldPos(), size()));  // 恢复原位置，使用 setGeometry 避免立即重入

        inMoveEvent = false;  // 重置标志
        event->ignore();  // 忽略当前事件，防止进一步传播
        return;
    }

    QWidget *mdiParent = parentWidget();
    if (!mdiParent) {
        QMdiSubWindow::moveEvent(event);
        return;
    }

    // 获取有效的边界区域（考虑滚动条等）
    QRect bounds = mdiParent->rect();
    if (MdiWindowManager *mdiArea = qobject_cast<MdiWindowManager*>(mdiParent)) {
        bounds = mdiArea->viewport()->rect();
    }

    // 计算允许的最大位置
    QSize windowSize = size();
    QPoint newPos = event->pos();

    // 确保计算不会出现负值（当窗口比父窗口大时）
    int maxX = qMax(0, bounds.width() - windowSize.width());
    int maxY = qMax(0, bounds.height() - windowSize.height());

    // 严格限制在边界内
    newPos.setX(qBound(0, newPos.x(), maxX));
    newPos.setY(qBound(0, newPos.y(), maxY));

    // 如果位置需要调整，直接设置新位置
    if (newPos != event->pos()) {
        move(newPos);
        return; // 跳过基类事件处理，避免二次移动
    }

    //QMdiSubWindow::moveEvent(event);
}

void CustomMdiSubWindow::closeEvent(QCloseEvent *event)
{
    // 如果窗口被锁定，阻止关闭
    if (locked) {
        event->ignore();
        qDebug() << "窗口被锁定，禁止关闭";
        return;
    }

    emit subClose();
    // 直接隐藏窗口，不执行任何清理操作
    hide();  // 关键修改：隐藏而非关闭

    // 忽略关闭事件，阻止基类进一步处理
    event->ignore();

    qDebug() << "窗口已隐藏（模拟关闭）";
}

MdiWindowManager::MdiWindowManager(QWidget *parent) : QMdiArea(parent)
{
    // 连接 subWindowActivated 信号到槽
    connect(this, &QMdiArea::subWindowActivated, this, &MdiWindowManager::onSubWindowActivated);
}

 //添加一个管理的子窗口
CustomMdiSubWindow* MdiWindowManager::addWindow(const QString &subWindowName, const QRect &rect)
{
    if (subWindowsName.contains(subWindowName)) {
        QMessageBox::information(this,"警告",subWindowName+"窗口已存在:");
        return subWindowsName[subWindowName].first;
    }

    // 创建自定义子窗口
    CustomMdiSubWindow *subWindow = new CustomMdiSubWindow(this);
    subWindow->setWindowTitle(" ");//让窗口标题由默认的from变为空白
    Chart *chartNew = new Chart(nullptr, subWindowName);
    subWindow->setWidget(chartNew);

    //subWindow->setAttribute(Qt::WA_DeleteOnClose, false);  // 防止关闭时自动删除
    subWindow->setGeometry(rect);  // 设置初始位置和大小
    subWindow->setMinimumSize(300, 200);// 设置最小大小,防止其随意调整

    // 存储到映射
    subWindowsName[subWindowName].first = subWindow;
    subWindowsName[subWindowName].second = false;
    subWindowsChart[subWindow]=chartNew;

    // 添加到 mdiArea
    addSubWindow(subWindow);
    subWindow->hide();

    qDebug() << "添加窗口:" << subWindowName << "位置:" << rect;
    return subWindowsName[subWindowName].first;
}

//根据名字获取子窗口是否显示
bool MdiWindowManager::getShowByName(const QString &subWindowName)
{
    return subWindowsName[subWindowName].first;
}

void MdiWindowManager::setShowByName(const QString &subWindowName, bool show)
{
    subWindowsName[subWindowName].second=show;
}

//根据名字获取子窗口指针
CustomMdiSubWindow* MdiWindowManager::getWindowByName(const QString &subWindowName)
{
    return subWindowsName[subWindowName].first;
}

void MdiWindowManager::clearName(const QString &subWindowName)
{
    if (subWindowsName.contains(subWindowName)) {
        CustomMdiSubWindow* subWindow = subWindowsName[subWindowName].first;
        if (subWindow) {
            subWindow->disconnect(); // 断开信号槽
            if (subWindowsChart.contains(subWindow)) {
                Chart* chart = subWindowsChart.take(subWindow); // 移除并获取 Chart
                delete chart; // 删除 Chart 对象
            }
            removeSubWindow(subWindow); // 从 QMdiArea 移除子窗口
            delete subWindow; // 删除子窗口
        }
        subWindowsName.remove(subWindowName); // 移除映射条目
        qDebug() << "Cleared window:" << subWindowName;
    } else {
        qDebug() << "Window not found:" << subWindowName;
    }
    Q_ASSERT(subWindowsName.count() == subWindowsChart.count()); // 确保映射一致
}

//更改名字
bool MdiWindowManager::updateTile(QString newTile, QString oldTile)
{
    Chart* chart=subWindowsChart[subWindowsName[oldTile].first];
    if(chart){
        chart->setTitle(newTile);
        return true;
    }
    return false;
}

//更新指定子窗口内部 Chart 的曲线数据
void MdiWindowManager::updateWaveform(const QVector<double>& x, const QVector<double>& y, const QString &subWindowName,const QString &curveName)
{
    CustomMdiSubWindow* subWindow = getWindowByName(subWindowName);
    Chart *chart=subWindowsChart[subWindow];
    if (subWindow&&chart) {
        chart->updateCurve(x, y, curveName);
        qDebug() << "更新曲线:" << curveName << "数据点数:" << x.size();
    } else {
        qDebug() << "未找到子窗口:" << subWindowName;
    }
}

 //一键显示所有为true的子窗口
void MdiWindowManager::showAll()
{
    int i=0;
    if(subWindowsName.count()==0||subWindowsName.count()!=subWindowsChart.count()||globalLock==true)return;
    for (QPair<CustomMdiSubWindow*,bool> subWindow : subWindowsName.values()) {
        qDebug()<<"第个"<<++i<<"窗口"<<"值为"<<subWindow.second;
        if (subWindow.first&&subWindow.second) {

            subWindow.first->show();
        }
    }
    qDebug() << "显示所有窗口";
    qDebug() <<"窗口数量为"<<subWindowList().count();
}

//一键隐藏所有子窗口
void MdiWindowManager::hideAll()
{
    if (subWindowsName.count()!=subWindowsChart.count()||globalLock==true) return;
    for (QPair<CustomMdiSubWindow*,bool> subWindow : subWindowsName.values()) {
        if (subWindow.first&&subWindow.second) {
            subWindow.first->hide();
        }
    }
    qDebug() << "隐藏所有窗口";
}

//一键锁定/解锁所有子窗口（锁定时不可移动）
void MdiWindowManager::lockAll(bool lock)
{
    if (subWindowsName.count()!=subWindowsChart.count()) return;
    globalLock = lock;
    for (QPair<CustomMdiSubWindow*,bool> subWindow : subWindowsName.values()) {
        if (subWindow.first) {
            subWindow.first->locked = lock;
        }
    }
    qDebug() << (lock ? "锁定所有窗口" : "解锁所有窗口");
}

//一键清除所有子窗口内部 Chart 的曲线
void MdiWindowManager::clearAllCurves()
{
    if (subWindowsName.count()!=subWindowsChart.count()||globalLock==true) return;
    for (QPair<CustomMdiSubWindow*,bool> subWindow : subWindowsName.values()) {
        if (subWindow.first&&subWindowsChart[subWindow.first]) {
            subWindowsChart[subWindow.first]->clearGraphs();  // 清除所有曲线
        }
    }
    qDebug() << "清除所有曲线";
}

//显示特定子窗口（根据名字）
void MdiWindowManager::showWindow(const QString &name)
{
    if (globalLock==true) return;
    CustomMdiSubWindow *subWindow = getWindowByName(name);
    if (subWindow) {
        subWindow->show();
        subWindow->activateWindow();  // 激活它，使其成为焦点
        onSubWindowActivated(subWindow);
        qDebug() << "显示特定窗口:" << name;
    } else {
        qDebug() << "未找到窗口:" << name;
    }
}

//重写关闭事件，移除事件过滤器。
void MdiWindowManager::closeEvent(QCloseEvent *event)
{
    // 清理所有子窗口和chart
    for (QPair<CustomMdiSubWindow*,bool> subWindow : subWindowsName.values()) {
        if (subWindow.first&&subWindowsChart[subWindow.first]) {
            delete subWindow.first;
        }
    }
    subWindowsName.clear();
    subWindowsChart.clear();
    QMdiArea::closeEvent(event);
}

//槽函数：处理子窗口激活信号，突出显示激活窗口（橙色标题），其他恢复紫色。
void MdiWindowManager::onSubWindowActivated(QMdiSubWindow *window)
{
    if (!window||subWindowsName.count()!=subWindowsChart.count()) return;
    for (QPair<CustomMdiSubWindow*,bool> subWindow : subWindowsName.values()) {
        if (subWindow.first&&subWindowsChart[subWindow.first]){
            if(subWindow.first == window){
                subWindowsChart[subWindow.first]->setTitleStyleSheet(true);  // 假设 false 设置紫色
            }else{
                subWindowsChart[subWindow.first]->setTitleStyleSheet(false);
            }
        }
    }
}

// 清除所有子窗口
void MdiWindowManager::clearAllSubWindows() {
    QList<QMdiSubWindow*> subWindows = subWindowList();
    for (QMdiSubWindow* subWindow : subWindows) {
        CustomMdiSubWindow* customSubWindow = qobject_cast<CustomMdiSubWindow*>(subWindow);
        if (customSubWindow) {
            customSubWindow->disconnect(); // 断开所有信号
            removeSubWindow(customSubWindow);
            delete customSubWindow;
        }
    }
    subWindowsName.clear();
    subWindowsChart.clear();
}
