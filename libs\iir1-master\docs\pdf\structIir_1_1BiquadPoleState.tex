\hypertarget{structIir_1_1BiquadPoleState}{}\doxysubsection{Iir\+::Biquad\+Pole\+State Struct Reference}
\label{structIir_1_1BiquadPoleState}\index{Iir::BiquadPoleState@{Iir::BiquadPoleState}}


{\ttfamily \#include $<$Biquad.\+h$>$}

Inheritance diagram for Iir\+::Biquad\+Pole\+State\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=2.000000cm]{structIir_1_1BiquadPoleState}
\end{center}
\end{figure}


\doxysubsubsection{Detailed Description}
Expresses a biquad as a pair of pole/zeros, with gain values so that the coefficients can be reconstructed precisely. 

The documentation for this struct was generated from the following files\+:\begin{DoxyCompactItemize}
\item 
iir/Biquad.\+h\item 
iir/Biquad.\+cpp\end{DoxyCompactItemize}
