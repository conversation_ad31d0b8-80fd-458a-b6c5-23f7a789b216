\hypertarget{structIir_1_1ChebyshevI_1_1HighPass}{}\doxysubsection{Iir\+::ChebyshevI\+::High\+Pass$<$ Filter\+Order, State\+Type $>$ Struct Template Reference}
\label{structIir_1_1ChebyshevI_1_1HighPass}\index{Iir::ChebyshevI::HighPass$<$ FilterOrder, StateType $>$@{Iir::ChebyshevI::HighPass$<$ FilterOrder, StateType $>$}}


{\ttfamily \#include $<$Chebyshev\+I.\+h$>$}

Inheritance diagram for Iir\+::ChebyshevI\+::High\+Pass$<$ Filter\+Order, State\+Type $>$\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=2.400000cm]{structIir_1_1ChebyshevI_1_1HighPass}
\end{center}
\end{figure}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
void \mbox{\hyperlink{structIir_1_1ChebyshevI_1_1HighPass_a031b43bd2545c4b0aba616c2464e1a65}{setup}} (double sample\+Rate, double cutoff\+Frequency, double ripple\+Db)
\item 
void \mbox{\hyperlink{structIir_1_1ChebyshevI_1_1HighPass_a0ecdb8e5692663a043f49db30277ba37}{setup}} (int req\+Order, double sample\+Rate, double cutoff\+Frequency, double ripple\+Db)
\item 
void \mbox{\hyperlink{structIir_1_1ChebyshevI_1_1HighPass_aea3ad8ee966575c3a6680758f3b22123}{setupN}} (double cutoff\+Frequency, double ripple\+Db)
\item 
void \mbox{\hyperlink{structIir_1_1ChebyshevI_1_1HighPass_a634d827d4c59b83e4eb29ad5a2c97eb5}{setupN}} (int req\+Order, double cutoff\+Frequency, double ripple\+Db)
\end{DoxyCompactItemize}


\doxysubsubsection{Detailed Description}
\subsubsection*{template$<$int Filter\+Order = 4, class State\+Type = Direct\+Form\+II$>$\newline
struct Iir\+::\+Chebyshev\+I\+::\+High\+Pass$<$ Filter\+Order, State\+Type $>$}

\mbox{\hyperlink{namespaceIir_1_1ChebyshevI}{ChebyshevI}} highpass filter 
\begin{DoxyParams}{Parameters}
{\em Filter\+Order} & Reserves memory for a filter of the order Filter\+Order \\
\hline
{\em State\+Type} & The filter topology\+: \mbox{\hyperlink{classIir_1_1DirectFormI}{Direct\+FormI}}, \mbox{\hyperlink{classIir_1_1DirectFormII}{Direct\+Form\+II}}, ... \\
\hline
\end{DoxyParams}


\doxysubsubsection{Member Function Documentation}
\mbox{\Hypertarget{structIir_1_1ChebyshevI_1_1HighPass_a031b43bd2545c4b0aba616c2464e1a65}\label{structIir_1_1ChebyshevI_1_1HighPass_a031b43bd2545c4b0aba616c2464e1a65}} 
\index{Iir::ChebyshevI::HighPass$<$ FilterOrder, StateType $>$@{Iir::ChebyshevI::HighPass$<$ FilterOrder, StateType $>$}!setup@{setup}}
\index{setup@{setup}!Iir::ChebyshevI::HighPass$<$ FilterOrder, StateType $>$@{Iir::ChebyshevI::HighPass$<$ FilterOrder, StateType $>$}}
\doxyparagraph{\texorpdfstring{setup()}{setup()}\hspace{0.1cm}{\footnotesize\ttfamily [1/2]}}
{\footnotesize\ttfamily template$<$int Filter\+Order = 4, class State\+Type  = Direct\+Form\+II$>$ \\
void \mbox{\hyperlink{structIir_1_1ChebyshevI_1_1HighPass}{Iir\+::\+Chebyshev\+I\+::\+High\+Pass}}$<$ Filter\+Order, State\+Type $>$\+::setup (\begin{DoxyParamCaption}\item[{double}]{sample\+Rate,  }\item[{double}]{cutoff\+Frequency,  }\item[{double}]{ripple\+Db }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients of the filter at the order Filter\+Order 
\begin{DoxyParams}{Parameters}
{\em sample\+Rate} & Sampling rate \\
\hline
{\em cutoff\+Frequency} & Cutoff frequency. \\
\hline
{\em ripple\+Db} & Permitted ripples in dB in the passband \\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{structIir_1_1ChebyshevI_1_1HighPass_a0ecdb8e5692663a043f49db30277ba37}\label{structIir_1_1ChebyshevI_1_1HighPass_a0ecdb8e5692663a043f49db30277ba37}} 
\index{Iir::ChebyshevI::HighPass$<$ FilterOrder, StateType $>$@{Iir::ChebyshevI::HighPass$<$ FilterOrder, StateType $>$}!setup@{setup}}
\index{setup@{setup}!Iir::ChebyshevI::HighPass$<$ FilterOrder, StateType $>$@{Iir::ChebyshevI::HighPass$<$ FilterOrder, StateType $>$}}
\doxyparagraph{\texorpdfstring{setup()}{setup()}\hspace{0.1cm}{\footnotesize\ttfamily [2/2]}}
{\footnotesize\ttfamily template$<$int Filter\+Order = 4, class State\+Type  = Direct\+Form\+II$>$ \\
void \mbox{\hyperlink{structIir_1_1ChebyshevI_1_1HighPass}{Iir\+::\+Chebyshev\+I\+::\+High\+Pass}}$<$ Filter\+Order, State\+Type $>$\+::setup (\begin{DoxyParamCaption}\item[{int}]{req\+Order,  }\item[{double}]{sample\+Rate,  }\item[{double}]{cutoff\+Frequency,  }\item[{double}]{ripple\+Db }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients of the filter at specified order 
\begin{DoxyParams}{Parameters}
{\em req\+Order} & Actual order for the filter calculations \\
\hline
{\em sample\+Rate} & Sampling rate \\
\hline
{\em cutoff\+Frequency} & Cutoff frequency. \\
\hline
{\em ripple\+Db} & Permitted ripples in dB in the passband \\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{structIir_1_1ChebyshevI_1_1HighPass_aea3ad8ee966575c3a6680758f3b22123}\label{structIir_1_1ChebyshevI_1_1HighPass_aea3ad8ee966575c3a6680758f3b22123}} 
\index{Iir::ChebyshevI::HighPass$<$ FilterOrder, StateType $>$@{Iir::ChebyshevI::HighPass$<$ FilterOrder, StateType $>$}!setupN@{setupN}}
\index{setupN@{setupN}!Iir::ChebyshevI::HighPass$<$ FilterOrder, StateType $>$@{Iir::ChebyshevI::HighPass$<$ FilterOrder, StateType $>$}}
\doxyparagraph{\texorpdfstring{setupN()}{setupN()}\hspace{0.1cm}{\footnotesize\ttfamily [1/2]}}
{\footnotesize\ttfamily template$<$int Filter\+Order = 4, class State\+Type  = Direct\+Form\+II$>$ \\
void \mbox{\hyperlink{structIir_1_1ChebyshevI_1_1HighPass}{Iir\+::\+Chebyshev\+I\+::\+High\+Pass}}$<$ Filter\+Order, State\+Type $>$\+::setupN (\begin{DoxyParamCaption}\item[{double}]{cutoff\+Frequency,  }\item[{double}]{ripple\+Db }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients of the filter at the order Filter\+Order 
\begin{DoxyParams}{Parameters}
{\em cutoff\+Frequency} & Normalised cutoff frequency (0..1/2) \\
\hline
{\em ripple\+Db} & Permitted ripples in dB in the passband \\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{structIir_1_1ChebyshevI_1_1HighPass_a634d827d4c59b83e4eb29ad5a2c97eb5}\label{structIir_1_1ChebyshevI_1_1HighPass_a634d827d4c59b83e4eb29ad5a2c97eb5}} 
\index{Iir::ChebyshevI::HighPass$<$ FilterOrder, StateType $>$@{Iir::ChebyshevI::HighPass$<$ FilterOrder, StateType $>$}!setupN@{setupN}}
\index{setupN@{setupN}!Iir::ChebyshevI::HighPass$<$ FilterOrder, StateType $>$@{Iir::ChebyshevI::HighPass$<$ FilterOrder, StateType $>$}}
\doxyparagraph{\texorpdfstring{setupN()}{setupN()}\hspace{0.1cm}{\footnotesize\ttfamily [2/2]}}
{\footnotesize\ttfamily template$<$int Filter\+Order = 4, class State\+Type  = Direct\+Form\+II$>$ \\
void \mbox{\hyperlink{structIir_1_1ChebyshevI_1_1HighPass}{Iir\+::\+Chebyshev\+I\+::\+High\+Pass}}$<$ Filter\+Order, State\+Type $>$\+::setupN (\begin{DoxyParamCaption}\item[{int}]{req\+Order,  }\item[{double}]{cutoff\+Frequency,  }\item[{double}]{ripple\+Db }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients of the filter at specified order 
\begin{DoxyParams}{Parameters}
{\em req\+Order} & Actual order for the filter calculations \\
\hline
{\em cutoff\+Frequency} & Normalised cutoff frequency (0..1/2) \\
\hline
{\em ripple\+Db} & Permitted ripples in dB in the passband \\
\hline
\end{DoxyParams}


The documentation for this struct was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
iir/Chebyshev\+I.\+h\end{DoxyCompactItemize}
