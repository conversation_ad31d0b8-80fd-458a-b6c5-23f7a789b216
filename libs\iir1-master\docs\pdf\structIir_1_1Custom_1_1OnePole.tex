\hypertarget{structIir_1_1Custom_1_1OnePole}{}\doxysubsection{Iir\+::Custom\+::One\+Pole Struct Reference}
\label{structIir_1_1Custom_1_1OnePole}\index{Iir::Custom::OnePole@{Iir::Custom::OnePole}}


{\ttfamily \#include $<$Custom.\+h$>$}

Inheritance diagram for Iir\+::Custom\+::One\+Pole\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=2.000000cm]{structIir_1_1Custom_1_1OnePole}
\end{center}
\end{figure}
\doxysubsubsection*{Additional Inherited Members}


\doxysubsubsection{Detailed Description}
Setting up a filter with with one real pole, real zero and scale it by the scale factor 
\begin{DoxyParams}{Parameters}
{\em scale} & Scale the FIR coefficients by this factor \\
\hline
{\em pole} & Position of the pole on the real axis \\
\hline
{\em zero} & Position of the zero on the real axis \\
\hline
\end{DoxyParams}


The documentation for this struct was generated from the following files\+:\begin{DoxyCompactItemize}
\item 
iir/Custom.\+h\item 
iir/Custom.\+cpp\end{DoxyCompactItemize}
