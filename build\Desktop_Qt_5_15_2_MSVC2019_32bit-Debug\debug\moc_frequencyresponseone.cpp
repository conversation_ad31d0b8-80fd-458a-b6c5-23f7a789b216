/****************************************************************************
** Meta object code from reading C++ file 'frequencyresponseone.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.15.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../SeqEditALL/SonWindows/frequencyresponseone.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'frequencyresponseone.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.15.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_FrequencyResponseOne_t {
    QByteArrayData data[14];
    char stringdata0[195];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_FrequencyResponseOne_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_FrequencyResponseOne_t qt_meta_stringdata_FrequencyResponseOne = {
    {
QT_MOC_LITERAL(0, 0, 20), // "FrequencyResponseOne"
QT_MOC_LITERAL(1, 21, 19), // "parametersConfirmed"
QT_MOC_LITERAL(2, 41, 0), // ""
QT_MOC_LITERAL(3, 42, 6), // "params"
QT_MOC_LITERAL(4, 49, 12), // "windowClosed"
QT_MOC_LITERAL(5, 62, 17), // "on_adjust_clicked"
QT_MOC_LITERAL(6, 80, 20), // "on_universal_toggled"
QT_MOC_LITERAL(7, 101, 7), // "checked"
QT_MOC_LITERAL(8, 109, 18), // "onTableItemChanged"
QT_MOC_LITERAL(9, 128, 17), // "QTableWidgetItem*"
QT_MOC_LITERAL(10, 146, 4), // "item"
QT_MOC_LITERAL(11, 151, 17), // "onCurvesGenerated"
QT_MOC_LITERAL(12, 169, 21), // "closeTableItemChanged"
QT_MOC_LITERAL(13, 191, 3) // "row"

    },
    "FrequencyResponseOne\0parametersConfirmed\0"
    "\0params\0windowClosed\0on_adjust_clicked\0"
    "on_universal_toggled\0checked\0"
    "onTableItemChanged\0QTableWidgetItem*\0"
    "item\0onCurvesGenerated\0closeTableItemChanged\0"
    "row"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_FrequencyResponseOne[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       7,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       2,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   49,    2, 0x06 /* Public */,
       4,    1,   52,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       5,    0,   55,    2, 0x08 /* Private */,
       6,    1,   56,    2, 0x08 /* Private */,
       8,    1,   59,    2, 0x08 /* Private */,
      11,    0,   62,    2, 0x08 /* Private */,
      12,    1,   63,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::QVariantMap,    3,
    QMetaType::Void, QMetaType::QVariantMap,    3,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void, QMetaType::Bool,    7,
    QMetaType::Void, 0x80000000 | 9,   10,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int,   13,

       0        // eod
};

void FrequencyResponseOne::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<FrequencyResponseOne *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->parametersConfirmed((*reinterpret_cast< const QVariantMap(*)>(_a[1]))); break;
        case 1: _t->windowClosed((*reinterpret_cast< const QVariantMap(*)>(_a[1]))); break;
        case 2: _t->on_adjust_clicked(); break;
        case 3: _t->on_universal_toggled((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 4: _t->onTableItemChanged((*reinterpret_cast< QTableWidgetItem*(*)>(_a[1]))); break;
        case 5: _t->onCurvesGenerated(); break;
        case 6: _t->closeTableItemChanged((*reinterpret_cast< int(*)>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (FrequencyResponseOne::*)(const QVariantMap & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&FrequencyResponseOne::parametersConfirmed)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (FrequencyResponseOne::*)(const QVariantMap & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&FrequencyResponseOne::windowClosed)) {
                *result = 1;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject FrequencyResponseOne::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_meta_stringdata_FrequencyResponseOne.data,
    qt_meta_data_FrequencyResponseOne,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *FrequencyResponseOne::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *FrequencyResponseOne::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_FrequencyResponseOne.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int FrequencyResponseOne::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 7)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 7;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 7)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 7;
    }
    return _id;
}

// SIGNAL 0
void FrequencyResponseOne::parametersConfirmed(const QVariantMap & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void FrequencyResponseOne::windowClosed(const QVariantMap & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
