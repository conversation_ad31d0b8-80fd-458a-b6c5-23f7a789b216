\hypertarget{structIir_1_1RBJ_1_1BandStop}{}\doxysubsection{Iir\+::RBJ\+::Band\+Stop Struct Reference}
\label{structIir_1_1RBJ_1_1BandStop}\index{Iir::RBJ::BandStop@{Iir::RBJ::BandStop}}


{\ttfamily \#include $<$RBJ.\+h$>$}

Inheritance diagram for Iir\+::RBJ\+::Band\+Stop\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=3.000000cm]{structIir_1_1RBJ_1_1BandStop}
\end{center}
\end{figure}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
void \mbox{\hyperlink{structIir_1_1RBJ_1_1BandStop_a52326e7ac833ac14e7a8859548fb321d}{setupN}} (double center\+Frequency, double band\+Width)
\item 
void \mbox{\hyperlink{structIir_1_1RBJ_1_1BandStop_afe29086ad05ecfe2c0697a09bfa45c49}{setup}} (double sample\+Rate, double center\+Frequency, double band\+Width)
\end{DoxyCompactItemize}


\doxysubsubsection{Detailed Description}
Bandstop filter. Warning\+: the bandwidth might not be accurate for narrow notches. 

\doxysubsubsection{Member Function Documentation}
\mbox{\Hypertarget{structIir_1_1RBJ_1_1BandStop_afe29086ad05ecfe2c0697a09bfa45c49}\label{structIir_1_1RBJ_1_1BandStop_afe29086ad05ecfe2c0697a09bfa45c49}} 
\index{Iir::RBJ::BandStop@{Iir::RBJ::BandStop}!setup@{setup}}
\index{setup@{setup}!Iir::RBJ::BandStop@{Iir::RBJ::BandStop}}
\doxyparagraph{\texorpdfstring{setup()}{setup()}}
{\footnotesize\ttfamily void Iir\+::\+RBJ\+::\+Band\+Stop\+::setup (\begin{DoxyParamCaption}\item[{double}]{sample\+Rate,  }\item[{double}]{center\+Frequency,  }\item[{double}]{band\+Width }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients 
\begin{DoxyParams}{Parameters}
{\em sample\+Rate} & Sampling rate \\
\hline
{\em center\+Frequency} & Center frequency of the bandstop \\
\hline
{\em band\+Width} & Bandwidth in octaves \\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{structIir_1_1RBJ_1_1BandStop_a52326e7ac833ac14e7a8859548fb321d}\label{structIir_1_1RBJ_1_1BandStop_a52326e7ac833ac14e7a8859548fb321d}} 
\index{Iir::RBJ::BandStop@{Iir::RBJ::BandStop}!setupN@{setupN}}
\index{setupN@{setupN}!Iir::RBJ::BandStop@{Iir::RBJ::BandStop}}
\doxyparagraph{\texorpdfstring{setupN()}{setupN()}}
{\footnotesize\ttfamily void Iir\+::\+RBJ\+::\+Band\+Stop\+::setupN (\begin{DoxyParamCaption}\item[{double}]{center\+Frequency,  }\item[{double}]{band\+Width }\end{DoxyParamCaption})}

Calculates the coefficients 
\begin{DoxyParams}{Parameters}
{\em center\+Frequency} & Normalised Centre frequency of the bandstop \\
\hline
{\em band\+Width} & Bandwidth in octaves \\
\hline
\end{DoxyParams}


The documentation for this struct was generated from the following files\+:\begin{DoxyCompactItemize}
\item 
iir/RBJ.\+h\item 
iir/RBJ.\+cpp\end{DoxyCompactItemize}
