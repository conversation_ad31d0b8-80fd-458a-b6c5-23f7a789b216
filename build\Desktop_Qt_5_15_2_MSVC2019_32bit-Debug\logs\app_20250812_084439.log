2025-08-12 08:44:41.413 [WARNING] QWidget::setLayout: Attempting to set QLayout "horizontalLayout" on ChanalSetForm "ChanalSetForm", which already has a layout (kernel\qwidget.cpp:9978)
2025-08-12 08:44:48.231 [WARNING] QWindowsWindow::setGeometry: Unable to set geometry 120x30+344+420 (frame: 136x69+336+389) on QWidgetWindow/"UserManageDialogClassWindow" on "\\.\DISPLAY1". Resulting geometry: 583x122+344+420 (frame: 599x161+336+389) margins: 8, 31, 8, 8 minimum size: 583x122 MINMAXINFO maxSize=0,0 maxpos=0,0 mintrack=599,161 maxtrack=0,0) (qwindowswindow.cpp:1891)
2025-08-12 08:44:50.544 [WARNING] QWindowsWindow::setGeometry: Unable to set geometry 120x30+535+453 (frame: 136x69+527+422) on QWidgetWindow/"LoginDialogClassWindow" on "\\.\DISPLAY1". Resulting geometry: 120x177+535+453 (frame: 136x216+527+422) margins: 8, 31, 8, 8 minimum size: 109x177 MINMAXINFO maxSize=0,0 maxpos=0,0 mintrack=125,216 maxtrack=0,0) (qwindowswindow.cpp:1891)
2025-08-12 08:44:52.534 [WARNING] QWindowsWindow::setGeometry: Unable to set geometry 120x30+344+420 (frame: 136x69+336+389) on QWidgetWindow/"UserManageDialogClassWindow" on "\\.\DISPLAY1". Resulting geometry: 583x122+344+420 (frame: 599x161+336+389) margins: 8, 31, 8, 8 minimum size: 583x122 MINMAXINFO maxSize=0,0 maxpos=0,0 mintrack=599,161 maxtrack=0,0) (qwindowswindow.cpp:1891)
2025-08-12 08:44:58.746 [WARNING] QWindowsWindow::setGeometry: Unable to set geometry 120x30+535+453 (frame: 136x69+527+422) on QWidgetWindow/"LoginDialogClassWindow" on "\\.\DISPLAY1". Resulting geometry: 120x177+535+453 (frame: 136x216+527+422) margins: 8, 31, 8, 8 minimum size: 109x177 MINMAXINFO maxSize=0,0 maxpos=0,0 mintrack=125,216 maxtrack=0,0) (qwindowswindow.cpp:1891)
