\hypertarget{structIir_1_1Butterworth_1_1HighPass}{}\doxysubsection{Iir\+::Butterworth\+::High\+Pass$<$ Filter\+Order, State\+Type $>$ Struct Template Reference}
\label{structIir_1_1Butterworth_1_1HighPass}\index{Iir::Butterworth::HighPass$<$ FilterOrder, StateType $>$@{Iir::Butterworth::HighPass$<$ FilterOrder, StateType $>$}}


{\ttfamily \#include $<$Butterworth.\+h$>$}

Inheritance diagram for Iir\+::Butterworth\+::High\+Pass$<$ Filter\+Order, State\+Type $>$\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=2.400000cm]{structIir_1_1Butterworth_1_1HighPass}
\end{center}
\end{figure}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
void \mbox{\hyperlink{structIir_1_1Butterworth_1_1HighPass_af7f7939a2e78245305cccd35aae4c75f}{setup}} (double sample\+Rate, double cutoff\+Frequency)
\item 
void \mbox{\hyperlink{structIir_1_1Butterworth_1_1HighPass_ac9835800f12a982c160720cebef2f963}{setup}} (int req\+Order, double sample\+Rate, double cutoff\+Frequency)
\item 
void \mbox{\hyperlink{structIir_1_1Butterworth_1_1HighPass_ac33912cbd0d3d65acdda0c53d5b64274}{setupN}} (double cutoff\+Frequency)
\item 
void \mbox{\hyperlink{structIir_1_1Butterworth_1_1HighPass_a7602019a900b288a16963126de49c2f1}{setupN}} (int req\+Order, double cutoff\+Frequency)
\end{DoxyCompactItemize}


\doxysubsubsection{Detailed Description}
\subsubsection*{template$<$int Filter\+Order = 4, class State\+Type = Direct\+Form\+II$>$\newline
struct Iir\+::\+Butterworth\+::\+High\+Pass$<$ Filter\+Order, State\+Type $>$}

\mbox{\hyperlink{namespaceIir_1_1Butterworth}{Butterworth}} Highpass filter. 
\begin{DoxyParams}{Parameters}
{\em Filter\+Order} & Reserves memory for a filter of the order Filter\+Order \\
\hline
{\em State\+Type} & The filter topology\+: \mbox{\hyperlink{classIir_1_1DirectFormI}{Direct\+FormI}}, \mbox{\hyperlink{classIir_1_1DirectFormII}{Direct\+Form\+II}}, ... \\
\hline
\end{DoxyParams}


\doxysubsubsection{Member Function Documentation}
\mbox{\Hypertarget{structIir_1_1Butterworth_1_1HighPass_af7f7939a2e78245305cccd35aae4c75f}\label{structIir_1_1Butterworth_1_1HighPass_af7f7939a2e78245305cccd35aae4c75f}} 
\index{Iir::Butterworth::HighPass$<$ FilterOrder, StateType $>$@{Iir::Butterworth::HighPass$<$ FilterOrder, StateType $>$}!setup@{setup}}
\index{setup@{setup}!Iir::Butterworth::HighPass$<$ FilterOrder, StateType $>$@{Iir::Butterworth::HighPass$<$ FilterOrder, StateType $>$}}
\doxyparagraph{\texorpdfstring{setup()}{setup()}\hspace{0.1cm}{\footnotesize\ttfamily [1/2]}}
{\footnotesize\ttfamily template$<$int Filter\+Order = 4, class State\+Type  = Direct\+Form\+II$>$ \\
void \mbox{\hyperlink{structIir_1_1Butterworth_1_1HighPass}{Iir\+::\+Butterworth\+::\+High\+Pass}}$<$ Filter\+Order, State\+Type $>$\+::setup (\begin{DoxyParamCaption}\item[{double}]{sample\+Rate,  }\item[{double}]{cutoff\+Frequency }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients with the filter order provided by the instantiation 
\begin{DoxyParams}{Parameters}
{\em sample\+Rate} & Sampling rate \\
\hline
{\em cutoff\+Frequency} & Cutoff frequency \\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{structIir_1_1Butterworth_1_1HighPass_ac9835800f12a982c160720cebef2f963}\label{structIir_1_1Butterworth_1_1HighPass_ac9835800f12a982c160720cebef2f963}} 
\index{Iir::Butterworth::HighPass$<$ FilterOrder, StateType $>$@{Iir::Butterworth::HighPass$<$ FilterOrder, StateType $>$}!setup@{setup}}
\index{setup@{setup}!Iir::Butterworth::HighPass$<$ FilterOrder, StateType $>$@{Iir::Butterworth::HighPass$<$ FilterOrder, StateType $>$}}
\doxyparagraph{\texorpdfstring{setup()}{setup()}\hspace{0.1cm}{\footnotesize\ttfamily [2/2]}}
{\footnotesize\ttfamily template$<$int Filter\+Order = 4, class State\+Type  = Direct\+Form\+II$>$ \\
void \mbox{\hyperlink{structIir_1_1Butterworth_1_1HighPass}{Iir\+::\+Butterworth\+::\+High\+Pass}}$<$ Filter\+Order, State\+Type $>$\+::setup (\begin{DoxyParamCaption}\item[{int}]{req\+Order,  }\item[{double}]{sample\+Rate,  }\item[{double}]{cutoff\+Frequency }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients 
\begin{DoxyParams}{Parameters}
{\em req\+Order} & The actual order which can be less than the instantiated one \\
\hline
{\em sample\+Rate} & Sampling rate \\
\hline
{\em cutoff\+Frequency} & Cutoff frequency \\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{structIir_1_1Butterworth_1_1HighPass_ac33912cbd0d3d65acdda0c53d5b64274}\label{structIir_1_1Butterworth_1_1HighPass_ac33912cbd0d3d65acdda0c53d5b64274}} 
\index{Iir::Butterworth::HighPass$<$ FilterOrder, StateType $>$@{Iir::Butterworth::HighPass$<$ FilterOrder, StateType $>$}!setupN@{setupN}}
\index{setupN@{setupN}!Iir::Butterworth::HighPass$<$ FilterOrder, StateType $>$@{Iir::Butterworth::HighPass$<$ FilterOrder, StateType $>$}}
\doxyparagraph{\texorpdfstring{setupN()}{setupN()}\hspace{0.1cm}{\footnotesize\ttfamily [1/2]}}
{\footnotesize\ttfamily template$<$int Filter\+Order = 4, class State\+Type  = Direct\+Form\+II$>$ \\
void \mbox{\hyperlink{structIir_1_1Butterworth_1_1HighPass}{Iir\+::\+Butterworth\+::\+High\+Pass}}$<$ Filter\+Order, State\+Type $>$\+::setupN (\begin{DoxyParamCaption}\item[{double}]{cutoff\+Frequency }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients with the filter order provided by the instantiation 
\begin{DoxyParams}{Parameters}
{\em cutoff\+Frequency} & Normalised cutoff frequency (0..1/2) \\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{structIir_1_1Butterworth_1_1HighPass_a7602019a900b288a16963126de49c2f1}\label{structIir_1_1Butterworth_1_1HighPass_a7602019a900b288a16963126de49c2f1}} 
\index{Iir::Butterworth::HighPass$<$ FilterOrder, StateType $>$@{Iir::Butterworth::HighPass$<$ FilterOrder, StateType $>$}!setupN@{setupN}}
\index{setupN@{setupN}!Iir::Butterworth::HighPass$<$ FilterOrder, StateType $>$@{Iir::Butterworth::HighPass$<$ FilterOrder, StateType $>$}}
\doxyparagraph{\texorpdfstring{setupN()}{setupN()}\hspace{0.1cm}{\footnotesize\ttfamily [2/2]}}
{\footnotesize\ttfamily template$<$int Filter\+Order = 4, class State\+Type  = Direct\+Form\+II$>$ \\
void \mbox{\hyperlink{structIir_1_1Butterworth_1_1HighPass}{Iir\+::\+Butterworth\+::\+High\+Pass}}$<$ Filter\+Order, State\+Type $>$\+::setupN (\begin{DoxyParamCaption}\item[{int}]{req\+Order,  }\item[{double}]{cutoff\+Frequency }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients 
\begin{DoxyParams}{Parameters}
{\em req\+Order} & The actual order which can be less than the instantiated one \\
\hline
{\em cutoff\+Frequency} & Normalised cutoff frequency (0..1/2) \\
\hline
\end{DoxyParams}


The documentation for this struct was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
iir/Butterworth.\+h\end{DoxyCompactItemize}
