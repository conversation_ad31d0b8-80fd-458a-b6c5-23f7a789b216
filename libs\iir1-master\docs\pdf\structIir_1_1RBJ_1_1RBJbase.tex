\hypertarget{structIir_1_1RBJ_1_1RBJbase}{}\doxysubsection{Iir\+::RBJ\+::RBJbase Struct Reference}
\label{structIir_1_1RBJ_1_1RBJbase}\index{Iir::RBJ::RBJbase@{Iir::RBJ::RBJbase}}


{\ttfamily \#include $<$RBJ.\+h$>$}

Inheritance diagram for Iir\+::RBJ\+::RBJbase\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=12.000000cm]{structIir_1_1RBJ_1_1RBJbase}
\end{center}
\end{figure}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
\mbox{\Hypertarget{structIir_1_1RBJ_1_1RBJbase_a5dd179c8491e29966d5c33036cabe151}\label{structIir_1_1RBJ_1_1RBJbase_a5dd179c8491e29966d5c33036cabe151}} 
{\footnotesize template$<$typename Sample $>$ }\\Sample \mbox{\hyperlink{structIir_1_1RBJ_1_1RBJbase_a5dd179c8491e29966d5c33036cabe151}{filter}} (Sample s)
\begin{DoxyCompactList}\small\item\em filter operation \end{DoxyCompactList}\item 
\mbox{\Hypertarget{structIir_1_1RBJ_1_1RBJbase_a0bd9a0b60e60b9326a14e43acbb430ab}\label{structIir_1_1RBJ_1_1RBJbase_a0bd9a0b60e60b9326a14e43acbb430ab}} 
void \mbox{\hyperlink{structIir_1_1RBJ_1_1RBJbase_a0bd9a0b60e60b9326a14e43acbb430ab}{reset}} ()
\begin{DoxyCompactList}\small\item\em resets the delay lines to zero \end{DoxyCompactList}\item 
\mbox{\Hypertarget{structIir_1_1RBJ_1_1RBJbase_a8093409edfce007a4972fa2992d69670}\label{structIir_1_1RBJ_1_1RBJbase_a8093409edfce007a4972fa2992d69670}} 
const \mbox{\hyperlink{classIir_1_1DirectFormI}{Direct\+FormI}} \& \mbox{\hyperlink{structIir_1_1RBJ_1_1RBJbase_a8093409edfce007a4972fa2992d69670}{get\+State}} ()
\begin{DoxyCompactList}\small\item\em gets the delay lines (=state) of the filter \end{DoxyCompactList}\end{DoxyCompactItemize}


\doxysubsubsection{Detailed Description}
The base class of all RBJ filters 

The documentation for this struct was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
iir/RBJ.\+h\end{DoxyCompactItemize}
