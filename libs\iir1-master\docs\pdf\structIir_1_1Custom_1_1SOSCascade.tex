\hypertarget{structIir_1_1Custom_1_1SOSCascade}{}\doxysubsection{Iir\+::Custom\+::SOSCascade$<$ NSOS, State\+Type $>$ Struct Template Reference}
\label{structIir_1_1Custom_1_1SOSCascade}\index{Iir::Custom::SOSCascade$<$ NSOS, StateType $>$@{Iir::Custom::SOSCascade$<$ NSOS, StateType $>$}}


{\ttfamily \#include $<$Custom.\+h$>$}

Inheritance diagram for Iir\+::Custom\+::SOSCascade$<$ NSOS, State\+Type $>$\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=2.000000cm]{structIir_1_1Custom_1_1SOSCascade}
\end{center}
\end{figure}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{structIir_1_1Custom_1_1SOSCascade_a04671c1c84cacaf1c822adeb850ccc63}{SOSCascade}} ()=default
\item 
\mbox{\hyperlink{structIir_1_1Custom_1_1SOSCascade_a60654fe4bd861799dff82a6dce9dfd42}{SOSCascade}} (const double(\&sos\+Coefficients)\mbox{[}NSOS\mbox{]}\mbox{[}6\mbox{]})
\item 
void \mbox{\hyperlink{structIir_1_1Custom_1_1SOSCascade_aae603eb1bd4330411a6003e09ba36efc}{setup}} (const double(\&sos\+Coefficients)\mbox{[}NSOS\mbox{]}\mbox{[}6\mbox{]})
\end{DoxyCompactItemize}


\doxysubsubsection{Detailed Description}
\subsubsection*{template$<$int NSOS, class State\+Type = Direct\+Form\+II$>$\newline
struct Iir\+::\+Custom\+::\+SOSCascade$<$ NSOS, State\+Type $>$}

A custom cascade of 2nd order (SOS / biquads) filters. 
\begin{DoxyParams}{Parameters}
{\em NSOS} & The number of 2nd order filters / biquads. \\
\hline
{\em State\+Type} & The filter topology\+: \mbox{\hyperlink{classIir_1_1DirectFormI}{Direct\+FormI}}, \mbox{\hyperlink{classIir_1_1DirectFormII}{Direct\+Form\+II}}, ... \\
\hline
\end{DoxyParams}


\doxysubsubsection{Constructor \& Destructor Documentation}
\mbox{\Hypertarget{structIir_1_1Custom_1_1SOSCascade_a04671c1c84cacaf1c822adeb850ccc63}\label{structIir_1_1Custom_1_1SOSCascade_a04671c1c84cacaf1c822adeb850ccc63}} 
\index{Iir::Custom::SOSCascade$<$ NSOS, StateType $>$@{Iir::Custom::SOSCascade$<$ NSOS, StateType $>$}!SOSCascade@{SOSCascade}}
\index{SOSCascade@{SOSCascade}!Iir::Custom::SOSCascade$<$ NSOS, StateType $>$@{Iir::Custom::SOSCascade$<$ NSOS, StateType $>$}}
\doxyparagraph{\texorpdfstring{SOSCascade()}{SOSCascade()}\hspace{0.1cm}{\footnotesize\ttfamily [1/2]}}
{\footnotesize\ttfamily template$<$int NSOS, class State\+Type  = Direct\+Form\+II$>$ \\
\mbox{\hyperlink{structIir_1_1Custom_1_1SOSCascade}{Iir\+::\+Custom\+::\+SOSCascade}}$<$ NSOS, State\+Type $>$\+::\mbox{\hyperlink{structIir_1_1Custom_1_1SOSCascade}{SOSCascade}} (\begin{DoxyParamCaption}{ }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [default]}}

Default constructor which creates a unity gain filter of NSOS biquads. Set the filter coefficients later with the \mbox{\hyperlink{structIir_1_1Custom_1_1SOSCascade_aae603eb1bd4330411a6003e09ba36efc}{setup()}} method. \mbox{\Hypertarget{structIir_1_1Custom_1_1SOSCascade_a60654fe4bd861799dff82a6dce9dfd42}\label{structIir_1_1Custom_1_1SOSCascade_a60654fe4bd861799dff82a6dce9dfd42}} 
\index{Iir::Custom::SOSCascade$<$ NSOS, StateType $>$@{Iir::Custom::SOSCascade$<$ NSOS, StateType $>$}!SOSCascade@{SOSCascade}}
\index{SOSCascade@{SOSCascade}!Iir::Custom::SOSCascade$<$ NSOS, StateType $>$@{Iir::Custom::SOSCascade$<$ NSOS, StateType $>$}}
\doxyparagraph{\texorpdfstring{SOSCascade()}{SOSCascade()}\hspace{0.1cm}{\footnotesize\ttfamily [2/2]}}
{\footnotesize\ttfamily template$<$int NSOS, class State\+Type  = Direct\+Form\+II$>$ \\
\mbox{\hyperlink{structIir_1_1Custom_1_1SOSCascade}{Iir\+::\+Custom\+::\+SOSCascade}}$<$ NSOS, State\+Type $>$\+::\mbox{\hyperlink{structIir_1_1Custom_1_1SOSCascade}{SOSCascade}} (\begin{DoxyParamCaption}\item[{const double(\&)}]{sos\+Coefficients\mbox{[}\+NSOS\mbox{]}\mbox{[}6\mbox{]} }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Python scipy.\+signal-\/friendly setting of coefficients. Initialises the coefficients of the whole chain of biquads / SOS. The argument is a 2D array where the 1st dimension holds an array of 2nd order biquad / SOS coefficients. The six SOS coefficients are ordered \char`\"{}\+Python\char`\"{} style with first the FIR coefficients (B) and then the IIR coefficients (A). The 2D const double array needs to have exactly the size \mbox{[}NSOS\mbox{]}\mbox{[}6\mbox{]}. 
\begin{DoxyParams}{Parameters}
{\em sos\+Coefficients} & 2D array Python style sos\mbox{[}NSOS\mbox{]}\mbox{[}6\mbox{]}. Indexing\+: 0-\/2\+: FIR-\/, 3-\/5\+: IIR-\/coefficients. \\
\hline
\end{DoxyParams}


\doxysubsubsection{Member Function Documentation}
\mbox{\Hypertarget{structIir_1_1Custom_1_1SOSCascade_aae603eb1bd4330411a6003e09ba36efc}\label{structIir_1_1Custom_1_1SOSCascade_aae603eb1bd4330411a6003e09ba36efc}} 
\index{Iir::Custom::SOSCascade$<$ NSOS, StateType $>$@{Iir::Custom::SOSCascade$<$ NSOS, StateType $>$}!setup@{setup}}
\index{setup@{setup}!Iir::Custom::SOSCascade$<$ NSOS, StateType $>$@{Iir::Custom::SOSCascade$<$ NSOS, StateType $>$}}
\doxyparagraph{\texorpdfstring{setup()}{setup()}}
{\footnotesize\ttfamily template$<$int NSOS, class State\+Type  = Direct\+Form\+II$>$ \\
void \mbox{\hyperlink{structIir_1_1Custom_1_1SOSCascade}{Iir\+::\+Custom\+::\+SOSCascade}}$<$ NSOS, State\+Type $>$\+::setup (\begin{DoxyParamCaption}\item[{const double(\&)}]{sos\+Coefficients\mbox{[}\+NSOS\mbox{]}\mbox{[}6\mbox{]} }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Python scipy.\+signal-\/friendly setting of coefficients. Sets the coefficients of the whole chain of biquads / SOS. The argument is a 2D array where the 1st dimension holds an array of 2nd order biquad / SOS coefficients. The six SOS coefficients are ordered \char`\"{}\+Python\char`\"{} style with first the FIR coefficients (B) and then the IIR coefficients (A). The 2D const double array needs to have exactly the size \mbox{[}NSOS\mbox{]}\mbox{[}6\mbox{]}. 
\begin{DoxyParams}{Parameters}
{\em sos\+Coefficients} & 2D array Python style sos\mbox{[}NSOS\mbox{]}\mbox{[}6\mbox{]}. Indexing\+: 0-\/2\+: FIR-\/, 3-\/5\+: IIR-\/coefficients. \\
\hline
\end{DoxyParams}


The documentation for this struct was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
iir/Custom.\+h\end{DoxyCompactItemize}
