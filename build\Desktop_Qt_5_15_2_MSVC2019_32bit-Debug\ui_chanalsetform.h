/********************************************************************************
** Form generated from reading UI file 'chanalsetform.ui'
**
** Created by: Qt User Interface Compiler version 5.15.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_CHANALSETFORM_H
#define UI_CHANALSETFORM_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QTableWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_ChanalSetForm
{
public:
    QHBoxLayout *horizontalLayout_6;
    QVBoxLayout *verticalLayout;
    QWidget *widget_4;
    QHBoxLayout *horizontalLayout_3;
    QWidget *widget;
    QVBoxLayout *verticalLayout_3;
    QLabel *label;
    QTableWidget *OutChNameTableWidget;
    QWidget *widget_2;
    QVBoxLayout *verticalLayout_5;
    QLabel *label_2;
    QLabel *OutChInfo;
    QWidget *widget_5;
    QHBoxLayout *horizontalLayout_5;
    QWidget *widget_6;
    QVBoxLayout *verticalLayout_2;
    QLabel *label_4;
    QTableWidget *InChNameTableWidget;
    QWidget *widget_7;
    QVBoxLayout *verticalLayout_4;
    QLabel *label_5;
    QLabel *InChInfo;
    QWidget *widget_3;
    QHBoxLayout *horizontalLayout_4;
    QHBoxLayout *horizontalLayout;
    QPushButton *OutChannelCalibrateBtn;
    QSpacerItem *horizontalSpacer_3;
    QPushButton *InChannelCalibrateBtn;
    QSpacerItem *horizontalSpacer_4;
    QPushButton *OUTINChannelCalibrateBtn;
    QSpacerItem *horizontalSpacer;
    QHBoxLayout *horizontalLayout_2;
    QPushButton *SaveBtn;
    QSpacerItem *horizontalSpacer_5;
    QPushButton *ExitBtn;
    QSpacerItem *horizontalSpacer_2;

    void setupUi(QWidget *ChanalSetForm)
    {
        if (ChanalSetForm->objectName().isEmpty())
            ChanalSetForm->setObjectName(QString::fromUtf8("ChanalSetForm"));
        ChanalSetForm->resize(913, 592);
        horizontalLayout_6 = new QHBoxLayout(ChanalSetForm);
        horizontalLayout_6->setObjectName(QString::fromUtf8("horizontalLayout_6"));
        verticalLayout = new QVBoxLayout();
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        widget_4 = new QWidget(ChanalSetForm);
        widget_4->setObjectName(QString::fromUtf8("widget_4"));
        widget_4->setStyleSheet(QString::fromUtf8("QWidget#widget_4 { border: 1px solid blue; border-radius: 5px; }"));
        horizontalLayout_3 = new QHBoxLayout(widget_4);
        horizontalLayout_3->setObjectName(QString::fromUtf8("horizontalLayout_3"));
        widget = new QWidget(widget_4);
        widget->setObjectName(QString::fromUtf8("widget"));
        QSizePolicy sizePolicy(QSizePolicy::Minimum, QSizePolicy::Preferred);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(widget->sizePolicy().hasHeightForWidth());
        widget->setSizePolicy(sizePolicy);
        widget->setMaximumSize(QSize(270, 16777215));
        widget->setStyleSheet(QString::fromUtf8(""));
        verticalLayout_3 = new QVBoxLayout(widget);
        verticalLayout_3->setObjectName(QString::fromUtf8("verticalLayout_3"));
        label = new QLabel(widget);
        label->setObjectName(QString::fromUtf8("label"));
        label->setMinimumSize(QSize(256, 30));
        label->setMaximumSize(QSize(256, 30));

        verticalLayout_3->addWidget(label);

        OutChNameTableWidget = new QTableWidget(widget);
        OutChNameTableWidget->setObjectName(QString::fromUtf8("OutChNameTableWidget"));
        OutChNameTableWidget->setMinimumSize(QSize(256, 0));
        OutChNameTableWidget->setMaximumSize(QSize(256, 16777215));

        verticalLayout_3->addWidget(OutChNameTableWidget);


        horizontalLayout_3->addWidget(widget);

        widget_2 = new QWidget(widget_4);
        widget_2->setObjectName(QString::fromUtf8("widget_2"));
        widget_2->setMinimumSize(QSize(300, 0));
        widget_2->setStyleSheet(QString::fromUtf8(""));
        verticalLayout_5 = new QVBoxLayout(widget_2);
        verticalLayout_5->setObjectName(QString::fromUtf8("verticalLayout_5"));
        label_2 = new QLabel(widget_2);
        label_2->setObjectName(QString::fromUtf8("label_2"));
        label_2->setMinimumSize(QSize(0, 30));
        label_2->setMaximumSize(QSize(16777215, 30));

        verticalLayout_5->addWidget(label_2);

        OutChInfo = new QLabel(widget_2);
        OutChInfo->setObjectName(QString::fromUtf8("OutChInfo"));
        OutChInfo->setStyleSheet(QString::fromUtf8("background-color: rgb(234, 234, 234);"));
        OutChInfo->setAlignment(Qt::AlignLeading|Qt::AlignLeft|Qt::AlignTop);

        verticalLayout_5->addWidget(OutChInfo);


        horizontalLayout_3->addWidget(widget_2);


        verticalLayout->addWidget(widget_4);

        widget_5 = new QWidget(ChanalSetForm);
        widget_5->setObjectName(QString::fromUtf8("widget_5"));
        widget_5->setStyleSheet(QString::fromUtf8("QWidget#widget_5 { border: 1px solid blue; border-radius: 5px; }"));
        horizontalLayout_5 = new QHBoxLayout(widget_5);
        horizontalLayout_5->setObjectName(QString::fromUtf8("horizontalLayout_5"));
        widget_6 = new QWidget(widget_5);
        widget_6->setObjectName(QString::fromUtf8("widget_6"));
        sizePolicy.setHeightForWidth(widget_6->sizePolicy().hasHeightForWidth());
        widget_6->setSizePolicy(sizePolicy);
        widget_6->setMaximumSize(QSize(270, 16777215));
        widget_6->setStyleSheet(QString::fromUtf8(""));
        verticalLayout_2 = new QVBoxLayout(widget_6);
        verticalLayout_2->setObjectName(QString::fromUtf8("verticalLayout_2"));
        label_4 = new QLabel(widget_6);
        label_4->setObjectName(QString::fromUtf8("label_4"));
        label_4->setMinimumSize(QSize(256, 30));
        label_4->setMaximumSize(QSize(256, 30));

        verticalLayout_2->addWidget(label_4);

        InChNameTableWidget = new QTableWidget(widget_6);
        InChNameTableWidget->setObjectName(QString::fromUtf8("InChNameTableWidget"));
        InChNameTableWidget->setMinimumSize(QSize(256, 0));
        InChNameTableWidget->setMaximumSize(QSize(256, 16777215));

        verticalLayout_2->addWidget(InChNameTableWidget);


        horizontalLayout_5->addWidget(widget_6);

        widget_7 = new QWidget(widget_5);
        widget_7->setObjectName(QString::fromUtf8("widget_7"));
        widget_7->setMinimumSize(QSize(300, 0));
        widget_7->setStyleSheet(QString::fromUtf8(""));
        verticalLayout_4 = new QVBoxLayout(widget_7);
        verticalLayout_4->setObjectName(QString::fromUtf8("verticalLayout_4"));
        label_5 = new QLabel(widget_7);
        label_5->setObjectName(QString::fromUtf8("label_5"));
        label_5->setMinimumSize(QSize(0, 30));
        label_5->setMaximumSize(QSize(16777215, 30));

        verticalLayout_4->addWidget(label_5);

        InChInfo = new QLabel(widget_7);
        InChInfo->setObjectName(QString::fromUtf8("InChInfo"));
        InChInfo->setStyleSheet(QString::fromUtf8("background-color: rgb(234, 234, 234);"));
        InChInfo->setAlignment(Qt::AlignLeading|Qt::AlignLeft|Qt::AlignTop);

        verticalLayout_4->addWidget(InChInfo);


        horizontalLayout_5->addWidget(widget_7);


        verticalLayout->addWidget(widget_5);

        widget_3 = new QWidget(ChanalSetForm);
        widget_3->setObjectName(QString::fromUtf8("widget_3"));
        widget_3->setMaximumSize(QSize(16777215, 50));
        horizontalLayout_4 = new QHBoxLayout(widget_3);
        horizontalLayout_4->setObjectName(QString::fromUtf8("horizontalLayout_4"));
        horizontalLayout = new QHBoxLayout();
        horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));
        OutChannelCalibrateBtn = new QPushButton(widget_3);
        OutChannelCalibrateBtn->setObjectName(QString::fromUtf8("OutChannelCalibrateBtn"));

        horizontalLayout->addWidget(OutChannelCalibrateBtn);

        horizontalSpacer_3 = new QSpacerItem(40, 20, QSizePolicy::Minimum, QSizePolicy::Minimum);

        horizontalLayout->addItem(horizontalSpacer_3);

        InChannelCalibrateBtn = new QPushButton(widget_3);
        InChannelCalibrateBtn->setObjectName(QString::fromUtf8("InChannelCalibrateBtn"));

        horizontalLayout->addWidget(InChannelCalibrateBtn);

        horizontalSpacer_4 = new QSpacerItem(40, 20, QSizePolicy::Minimum, QSizePolicy::Minimum);

        horizontalLayout->addItem(horizontalSpacer_4);

        OUTINChannelCalibrateBtn = new QPushButton(widget_3);
        OUTINChannelCalibrateBtn->setObjectName(QString::fromUtf8("OUTINChannelCalibrateBtn"));

        horizontalLayout->addWidget(OUTINChannelCalibrateBtn);


        horizontalLayout_4->addLayout(horizontalLayout);

        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_4->addItem(horizontalSpacer);

        horizontalLayout_2 = new QHBoxLayout();
        horizontalLayout_2->setObjectName(QString::fromUtf8("horizontalLayout_2"));
        SaveBtn = new QPushButton(widget_3);
        SaveBtn->setObjectName(QString::fromUtf8("SaveBtn"));

        horizontalLayout_2->addWidget(SaveBtn);

        horizontalSpacer_5 = new QSpacerItem(40, 20, QSizePolicy::Minimum, QSizePolicy::Minimum);

        horizontalLayout_2->addItem(horizontalSpacer_5);

        ExitBtn = new QPushButton(widget_3);
        ExitBtn->setObjectName(QString::fromUtf8("ExitBtn"));

        horizontalLayout_2->addWidget(ExitBtn);


        horizontalLayout_4->addLayout(horizontalLayout_2);

        horizontalSpacer_2 = new QSpacerItem(40, 20, QSizePolicy::Minimum, QSizePolicy::Minimum);

        horizontalLayout_4->addItem(horizontalSpacer_2);


        verticalLayout->addWidget(widget_3);


        horizontalLayout_6->addLayout(verticalLayout);


        retranslateUi(ChanalSetForm);

        QMetaObject::connectSlotsByName(ChanalSetForm);
    } // setupUi

    void retranslateUi(QWidget *ChanalSetForm)
    {
        ChanalSetForm->setWindowTitle(QCoreApplication::translate("ChanalSetForm", "\351\200\232\351\201\223\350\256\276\347\275\256", nullptr));
        label->setText(QCoreApplication::translate("ChanalSetForm", "\350\276\223\345\207\272\351\200\232\351\201\223", nullptr));
        label_2->setText(QCoreApplication::translate("ChanalSetForm", "\351\200\232\351\201\223\344\277\241\346\201\257", nullptr));
        OutChInfo->setText(QCoreApplication::translate("ChanalSetForm", "TextLabel", nullptr));
        label_4->setText(QCoreApplication::translate("ChanalSetForm", "\350\276\223\345\205\245\351\200\232\351\201\223", nullptr));
        label_5->setText(QCoreApplication::translate("ChanalSetForm", "\351\200\232\351\201\223\344\277\241\346\201\257", nullptr));
        InChInfo->setText(QCoreApplication::translate("ChanalSetForm", "<html><head/><body><p>TextLabel</p></body></html>", nullptr));
        OutChannelCalibrateBtn->setText(QCoreApplication::translate("ChanalSetForm", "\350\276\223\345\207\272\351\200\232\351\201\223\350\276\203\345\207\206", nullptr));
        InChannelCalibrateBtn->setText(QCoreApplication::translate("ChanalSetForm", "\350\276\223\345\205\245\351\200\232\351\201\223\350\276\203\345\207\206", nullptr));
        OUTINChannelCalibrateBtn->setText(QCoreApplication::translate("ChanalSetForm", "\350\276\223\345\205\245\345\222\214\350\276\223\345\207\272\351\200\232\351\201\223\350\276\203\345\207\206", nullptr));
        SaveBtn->setText(QCoreApplication::translate("ChanalSetForm", "\344\277\235\345\255\230", nullptr));
        ExitBtn->setText(QCoreApplication::translate("ChanalSetForm", "\351\200\200\345\207\272", nullptr));
    } // retranslateUi

};

namespace Ui {
    class ChanalSetForm: public Ui_ChanalSetForm {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_CHANALSETFORM_H
