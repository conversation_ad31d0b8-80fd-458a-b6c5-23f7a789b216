<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>iir1: Iir::Biquad Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">iir1
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespaceIir.html">Iir</a></li><li class="navelem"><a class="el" href="classIir_1_1Biquad.html">Biquad</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="classIir_1_1Biquad-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">Iir::Biquad Class Reference</div>  </div>
</div><!--header-->
<div class="contents">
<div class="dynheader">
Inheritance diagram for Iir::Biquad:</div>
<div class="dyncontent">
 <div class="center">
  <img src="classIir_1_1Biquad.png" usemap="#Iir::Biquad_map" alt=""/>
  <map id="Iir::Biquad_map" name="Iir::Biquad_map">
<area href="structIir_1_1Custom_1_1OnePole.html" alt="Iir::Custom::OnePole" shape="rect" coords="0,56,125,80"/>
<area href="structIir_1_1Custom_1_1TwoPole.html" alt="Iir::Custom::TwoPole" shape="rect" coords="135,56,260,80"/>
<area href="structIir_1_1RBJ_1_1RBJbase.html" alt="Iir::RBJ::RBJbase" shape="rect" coords="270,56,395,80"/>
<area href="structIir_1_1RBJ_1_1AllPass.html" alt="Iir::RBJ::AllPass" shape="rect" coords="405,112,530,136"/>
<area href="structIir_1_1RBJ_1_1BandPass1.html" alt="Iir::RBJ::BandPass1" shape="rect" coords="405,168,530,192"/>
<area href="structIir_1_1RBJ_1_1BandPass2.html" alt="Iir::RBJ::BandPass2" shape="rect" coords="405,224,530,248"/>
<area href="structIir_1_1RBJ_1_1BandShelf.html" alt="Iir::RBJ::BandShelf" shape="rect" coords="405,280,530,304"/>
<area href="structIir_1_1RBJ_1_1BandStop.html" alt="Iir::RBJ::BandStop" shape="rect" coords="405,336,530,360"/>
<area href="structIir_1_1RBJ_1_1HighPass.html" alt="Iir::RBJ::HighPass" shape="rect" coords="405,392,530,416"/>
<area href="structIir_1_1RBJ_1_1HighShelf.html" alt="Iir::RBJ::HighShelf" shape="rect" coords="405,448,530,472"/>
<area href="structIir_1_1RBJ_1_1IIRNotch.html" alt="Iir::RBJ::IIRNotch" shape="rect" coords="405,504,530,528"/>
<area href="structIir_1_1RBJ_1_1LowPass.html" alt="Iir::RBJ::LowPass" shape="rect" coords="405,560,530,584"/>
<area href="structIir_1_1RBJ_1_1LowShelf.html" alt="Iir::RBJ::LowShelf" shape="rect" coords="405,616,530,640"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:ace03653f66fe65eddc55d48840458440"><td class="memItemLeft" align="right" valign="top">complex_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classIir_1_1Biquad.html#ace03653f66fe65eddc55d48840458440">response</a> (double normalizedFrequency) const</td></tr>
<tr class="separator:ace03653f66fe65eddc55d48840458440"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a63c78d766bc40be10004a34aaebfb8e7"><td class="memItemLeft" align="right" valign="top">std::vector&lt; <a class="el" href="structIir_1_1PoleZeroPair.html">PoleZeroPair</a> &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classIir_1_1Biquad.html#a63c78d766bc40be10004a34aaebfb8e7">getPoleZeros</a> () const</td></tr>
<tr class="separator:a63c78d766bc40be10004a34aaebfb8e7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7debc9f6ef5e64622c710b8c9ba96056"><td class="memItemLeft" align="right" valign="top">double&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classIir_1_1Biquad.html#a7debc9f6ef5e64622c710b8c9ba96056">getA0</a> () const</td></tr>
<tr class="separator:a7debc9f6ef5e64622c710b8c9ba96056"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a459116db7aa381281997f428f15334cf"><td class="memItemLeft" align="right" valign="top">double&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classIir_1_1Biquad.html#a459116db7aa381281997f428f15334cf">getA1</a> () const</td></tr>
<tr class="separator:a459116db7aa381281997f428f15334cf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a061e2402d528312fc6095db6551c9141"><td class="memItemLeft" align="right" valign="top">double&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classIir_1_1Biquad.html#a061e2402d528312fc6095db6551c9141">getA2</a> () const</td></tr>
<tr class="separator:a061e2402d528312fc6095db6551c9141"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a84686da988e160a216f3e7057682ffbb"><td class="memItemLeft" align="right" valign="top">double&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classIir_1_1Biquad.html#a84686da988e160a216f3e7057682ffbb">getB0</a> () const</td></tr>
<tr class="separator:a84686da988e160a216f3e7057682ffbb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af418a5f260baadbcffe5a7029f089937"><td class="memItemLeft" align="right" valign="top">double&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classIir_1_1Biquad.html#af418a5f260baadbcffe5a7029f089937">getB1</a> () const</td></tr>
<tr class="separator:af418a5f260baadbcffe5a7029f089937"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8f3d697bb7c2def508da648938f6afc3"><td class="memItemLeft" align="right" valign="top">double&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classIir_1_1Biquad.html#a8f3d697bb7c2def508da648938f6afc3">getB2</a> () const</td></tr>
<tr class="separator:a8f3d697bb7c2def508da648938f6afc3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a527b9666d6aef0e576193fdce7e19750"><td class="memTemplParams" colspan="2">template&lt;class StateType &gt; </td></tr>
<tr class="memitem:a527b9666d6aef0e576193fdce7e19750"><td class="memTemplItemLeft" align="right" valign="top">double&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="classIir_1_1Biquad.html#a527b9666d6aef0e576193fdce7e19750">filter</a> (double s, StateType &amp;state) const</td></tr>
<tr class="separator:a527b9666d6aef0e576193fdce7e19750"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7f005089c194c68aeecdc66a3e6c6a78"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classIir_1_1Biquad.html#a7f005089c194c68aeecdc66a3e6c6a78">setCoefficients</a> (double a0, double a1, double a2, double b0, double b1, double b2)</td></tr>
<tr class="separator:a7f005089c194c68aeecdc66a3e6c6a78"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a455fd42a2e99ac84b09184becf2d047f"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classIir_1_1Biquad.html#a455fd42a2e99ac84b09184becf2d047f">setOnePole</a> (complex_t pole, complex_t zero)</td></tr>
<tr class="separator:a455fd42a2e99ac84b09184becf2d047f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6c614e84db8493b8495b314dd860d0bc"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classIir_1_1Biquad.html#a6c614e84db8493b8495b314dd860d0bc">setTwoPole</a> (complex_t pole1, complex_t zero1, complex_t pole2, complex_t zero2)</td></tr>
<tr class="separator:a6c614e84db8493b8495b314dd860d0bc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a69b4a2eaedb4b51aea9fb46a99c3d3c2"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classIir_1_1Biquad.html#a69b4a2eaedb4b51aea9fb46a99c3d3c2">setPoleZeroPair</a> (const <a class="el" href="structIir_1_1PoleZeroPair.html">PoleZeroPair</a> &amp;pair)</td></tr>
<tr class="separator:a69b4a2eaedb4b51aea9fb46a99c3d3c2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a53b066d077ce559a91f26fc3c4201c2c"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classIir_1_1Biquad.html#a53b066d077ce559a91f26fc3c4201c2c">setIdentity</a> ()</td></tr>
<tr class="separator:a53b066d077ce559a91f26fc3c4201c2c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a38c8e327dca53dbfa9a25758b7be8227"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classIir_1_1Biquad.html#a38c8e327dca53dbfa9a25758b7be8227">applyScale</a> (double scale)</td></tr>
<tr class="separator:a38c8e327dca53dbfa9a25758b7be8227"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="a38c8e327dca53dbfa9a25758b7be8227"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a38c8e327dca53dbfa9a25758b7be8227">&#9670;&nbsp;</a></span>applyScale()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Iir::Biquad::applyScale </td>
          <td>(</td>
          <td class="paramtype">double&#160;</td>
          <td class="paramname"><em>scale</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Performs scaling operation on the FIR coefficients </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">scale</td><td>Mulitplies the coefficients b0,b1,b2 with the scaling factor scale. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a527b9666d6aef0e576193fdce7e19750"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a527b9666d6aef0e576193fdce7e19750">&#9670;&nbsp;</a></span>filter()</h2>

<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;class StateType &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">double Iir::Biquad::filter </td>
          <td>(</td>
          <td class="paramtype">double&#160;</td>
          <td class="paramname"><em>s</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">StateType &amp;&#160;</td>
          <td class="paramname"><em>state</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Filter a sample with the coefficients provided here and the State provided as an argument. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">s</td><td>The sample to be filtered. </td></tr>
    <tr><td class="paramname">state</td><td>The Delay lines (instance of a state from <a class="el" href="State_8h_source.html">State.h</a>) </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The filtered sample. </dd></dl>

</div>
</div>
<a id="a7debc9f6ef5e64622c710b8c9ba96056"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7debc9f6ef5e64622c710b8c9ba96056">&#9670;&nbsp;</a></span>getA0()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">double Iir::Biquad::getA0 </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns 1st IIR coefficient (usually one) </p>

</div>
</div>
<a id="a459116db7aa381281997f428f15334cf"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a459116db7aa381281997f428f15334cf">&#9670;&nbsp;</a></span>getA1()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">double Iir::Biquad::getA1 </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns 2nd IIR coefficient </p>

</div>
</div>
<a id="a061e2402d528312fc6095db6551c9141"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a061e2402d528312fc6095db6551c9141">&#9670;&nbsp;</a></span>getA2()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">double Iir::Biquad::getA2 </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns 3rd IIR coefficient </p>

</div>
</div>
<a id="a84686da988e160a216f3e7057682ffbb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a84686da988e160a216f3e7057682ffbb">&#9670;&nbsp;</a></span>getB0()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">double Iir::Biquad::getB0 </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns 1st FIR coefficient </p>

</div>
</div>
<a id="af418a5f260baadbcffe5a7029f089937"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af418a5f260baadbcffe5a7029f089937">&#9670;&nbsp;</a></span>getB1()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">double Iir::Biquad::getB1 </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns 2nd FIR coefficient </p>

</div>
</div>
<a id="a8f3d697bb7c2def508da648938f6afc3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8f3d697bb7c2def508da648938f6afc3">&#9670;&nbsp;</a></span>getB2()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">double Iir::Biquad::getB2 </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns 3rd FIR coefficient </p>

</div>
</div>
<a id="a63c78d766bc40be10004a34aaebfb8e7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a63c78d766bc40be10004a34aaebfb8e7">&#9670;&nbsp;</a></span>getPoleZeros()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">std::vector&lt; <a class="el" href="structIir_1_1PoleZeroPair.html">PoleZeroPair</a> &gt; Iir::Biquad::getPoleZeros </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Returns the pole / zero Pairs as a vector. </p>

</div>
</div>
<a id="ace03653f66fe65eddc55d48840458440"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ace03653f66fe65eddc55d48840458440">&#9670;&nbsp;</a></span>response()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">complex_t Iir::Biquad::response </td>
          <td>(</td>
          <td class="paramtype">double&#160;</td>
          <td class="paramname"><em>normalizedFrequency</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Calculate filter response at the given normalized frequency and return the complex response.</p>
<p>Gets the frequency response of the <a class="el" href="classIir_1_1Biquad.html">Biquad</a> </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">normalizedFrequency</td><td>Normalised frequency (0 to 0.5) </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a7f005089c194c68aeecdc66a3e6c6a78"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7f005089c194c68aeecdc66a3e6c6a78">&#9670;&nbsp;</a></span>setCoefficients()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Iir::Biquad::setCoefficients </td>
          <td>(</td>
          <td class="paramtype">double&#160;</td>
          <td class="paramname"><em>a0</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">double&#160;</td>
          <td class="paramname"><em>a1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">double&#160;</td>
          <td class="paramname"><em>a2</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">double&#160;</td>
          <td class="paramname"><em>b0</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">double&#160;</td>
          <td class="paramname"><em>b1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">double&#160;</td>
          <td class="paramname"><em>b2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Sets all coefficients </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">a0</td><td>1st IIR coefficient </td></tr>
    <tr><td class="paramname">a1</td><td>2nd IIR coefficient </td></tr>
    <tr><td class="paramname">a2</td><td>3rd IIR coefficient </td></tr>
    <tr><td class="paramname">b0</td><td>1st FIR coefficient </td></tr>
    <tr><td class="paramname">b1</td><td>2nd FIR coefficient </td></tr>
    <tr><td class="paramname">b2</td><td>3rd FIR coefficient </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a53b066d077ce559a91f26fc3c4201c2c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a53b066d077ce559a91f26fc3c4201c2c">&#9670;&nbsp;</a></span>setIdentity()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Iir::Biquad::setIdentity </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Sets the coefficiens as pass through. (b0=1,a0=1, rest zero) </p>

</div>
</div>
<a id="a455fd42a2e99ac84b09184becf2d047f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a455fd42a2e99ac84b09184becf2d047f">&#9670;&nbsp;</a></span>setOnePole()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Iir::Biquad::setOnePole </td>
          <td>(</td>
          <td class="paramtype">complex_t&#160;</td>
          <td class="paramname"><em>pole</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">complex_t&#160;</td>
          <td class="paramname"><em>zero</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Sets one (real) pole and zero. Throws exception if imaginary components. </p>

</div>
</div>
<a id="a69b4a2eaedb4b51aea9fb46a99c3d3c2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a69b4a2eaedb4b51aea9fb46a99c3d3c2">&#9670;&nbsp;</a></span>setPoleZeroPair()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void Iir::Biquad::setPoleZeroPair </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structIir_1_1PoleZeroPair.html">PoleZeroPair</a> &amp;&#160;</td>
          <td class="paramname"><em>pair</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Sets a complex conjugate pair </p>

</div>
</div>
<a id="a6c614e84db8493b8495b314dd860d0bc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6c614e84db8493b8495b314dd860d0bc">&#9670;&nbsp;</a></span>setTwoPole()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Iir::Biquad::setTwoPole </td>
          <td>(</td>
          <td class="paramtype">complex_t&#160;</td>
          <td class="paramname"><em>pole1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">complex_t&#160;</td>
          <td class="paramname"><em>zero1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">complex_t&#160;</td>
          <td class="paramname"><em>pole2</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">complex_t&#160;</td>
          <td class="paramname"><em>zero2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Sets two poles/zoes as a pair. Needs to be complex conjugate. </p>

</div>
</div>
<hr/>The documentation for this class was generated from the following files:<ul>
<li>iir/<a class="el" href="Biquad_8h_source.html">Biquad.h</a></li>
<li>iir/Biquad.cpp</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Fri Jan 6 2023 23:53:33 for iir1 by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
