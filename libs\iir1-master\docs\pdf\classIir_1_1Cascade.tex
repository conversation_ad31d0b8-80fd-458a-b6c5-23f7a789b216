\hypertarget{classIir_1_1Cascade}{}\doxysubsection{Iir\+::Cascade Class Reference}
\label{classIir_1_1Cascade}\index{Iir::Cascade@{Iir::Cascade}}


{\ttfamily \#include $<$Cascade.\+h$>$}

Inheritance diagram for Iir\+::Cascade\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=8.860760cm]{classIir_1_1Cascade}
\end{center}
\end{figure}
\doxysubsubsection*{Classes}
\begin{DoxyCompactItemize}
\item 
struct \mbox{\hyperlink{structIir_1_1Cascade_1_1Storage}{Storage}}
\end{DoxyCompactItemize}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
int \mbox{\hyperlink{classIir_1_1Cascade_a9694b85c160e3689a4d71fd51ca6175d}{get\+Num\+Stages}} () const
\item 
const \mbox{\hyperlink{classIir_1_1Biquad}{Biquad}} \& \mbox{\hyperlink{classIir_1_1Cascade_afc58e4b464b2cdef11e77b01e1a80668}{operator\mbox{[}$\,$\mbox{]}}} (int index)
\item 
complex\+\_\+t \mbox{\hyperlink{classIir_1_1Cascade_aa76e09e3868829a80e954d0444390f90}{response}} (double normalized\+Frequency) const
\item 
std\+::vector$<$ \mbox{\hyperlink{structIir_1_1PoleZeroPair}{Pole\+Zero\+Pair}} $>$ \mbox{\hyperlink{classIir_1_1Cascade_a18df8bebec4a5e8e3ddc28c35b6bb2f8}{get\+Pole\+Zeros}} () const
\end{DoxyCompactItemize}


\doxysubsubsection{Detailed Description}
Holds coefficients for a cascade of second order sections. 

\doxysubsubsection{Member Function Documentation}
\mbox{\Hypertarget{classIir_1_1Cascade_a9694b85c160e3689a4d71fd51ca6175d}\label{classIir_1_1Cascade_a9694b85c160e3689a4d71fd51ca6175d}} 
\index{Iir::Cascade@{Iir::Cascade}!getNumStages@{getNumStages}}
\index{getNumStages@{getNumStages}!Iir::Cascade@{Iir::Cascade}}
\doxyparagraph{\texorpdfstring{getNumStages()}{getNumStages()}}
{\footnotesize\ttfamily int Iir\+::\+Cascade\+::get\+Num\+Stages (\begin{DoxyParamCaption}{ }\end{DoxyParamCaption}) const\hspace{0.3cm}{\ttfamily [inline]}}

Returns the number of Biquads kept here \mbox{\Hypertarget{classIir_1_1Cascade_a18df8bebec4a5e8e3ddc28c35b6bb2f8}\label{classIir_1_1Cascade_a18df8bebec4a5e8e3ddc28c35b6bb2f8}} 
\index{Iir::Cascade@{Iir::Cascade}!getPoleZeros@{getPoleZeros}}
\index{getPoleZeros@{getPoleZeros}!Iir::Cascade@{Iir::Cascade}}
\doxyparagraph{\texorpdfstring{getPoleZeros()}{getPoleZeros()}}
{\footnotesize\ttfamily std\+::vector$<$ \mbox{\hyperlink{structIir_1_1PoleZeroPair}{Pole\+Zero\+Pair}} $>$ Iir\+::\+Cascade\+::get\+Pole\+Zeros (\begin{DoxyParamCaption}{ }\end{DoxyParamCaption}) const}

Returns a vector with all pole/zero pairs of the whole Biqad cascade \mbox{\Hypertarget{classIir_1_1Cascade_afc58e4b464b2cdef11e77b01e1a80668}\label{classIir_1_1Cascade_afc58e4b464b2cdef11e77b01e1a80668}} 
\index{Iir::Cascade@{Iir::Cascade}!operator\mbox{[}\mbox{]}@{operator[]}}
\index{operator\mbox{[}\mbox{]}@{operator[]}!Iir::Cascade@{Iir::Cascade}}
\doxyparagraph{\texorpdfstring{operator[]()}{operator[]()}}
{\footnotesize\ttfamily const \mbox{\hyperlink{classIir_1_1Biquad}{Biquad}}\& Iir\+::\+Cascade\+::operator\mbox{[}$\,$\mbox{]} (\begin{DoxyParamCaption}\item[{int}]{index }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Returns a reference to a biquad \mbox{\Hypertarget{classIir_1_1Cascade_aa76e09e3868829a80e954d0444390f90}\label{classIir_1_1Cascade_aa76e09e3868829a80e954d0444390f90}} 
\index{Iir::Cascade@{Iir::Cascade}!response@{response}}
\index{response@{response}!Iir::Cascade@{Iir::Cascade}}
\doxyparagraph{\texorpdfstring{response()}{response()}}
{\footnotesize\ttfamily complex\+\_\+t Iir\+::\+Cascade\+::response (\begin{DoxyParamCaption}\item[{double}]{normalized\+Frequency }\end{DoxyParamCaption}) const}

Calculate filter response at the given normalized frequency 
\begin{DoxyParams}{Parameters}
{\em normalized\+Frequency} & Frequency from 0 to 0.\+5 (Nyquist) \\
\hline
\end{DoxyParams}


The documentation for this class was generated from the following files\+:\begin{DoxyCompactItemize}
\item 
iir/Cascade.\+h\item 
iir/Cascade.\+cpp\end{DoxyCompactItemize}
