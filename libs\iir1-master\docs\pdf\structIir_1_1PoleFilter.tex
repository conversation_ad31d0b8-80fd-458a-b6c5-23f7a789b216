\hypertarget{structIir_1_1PoleFilter}{}\doxysubsection{Iir\+::Pole\+Filter$<$ Base\+Class, State\+Type, Max\+Analog\+Poles, Max\+Digital\+Poles $>$ Struct Template Reference}
\label{structIir_1_1PoleFilter}\index{Iir::PoleFilter$<$ BaseClass, StateType, MaxAnalogPoles, MaxDigitalPoles $>$@{Iir::PoleFilter$<$ BaseClass, StateType, MaxAnalogPoles, MaxDigitalPoles $>$}}


{\ttfamily \#include $<$Pole\+Filter.\+h$>$}

Inheritance diagram for Iir\+::Pole\+Filter$<$ Base\+Class, State\+Type, Max\+Analog\+Poles, Max\+Digital\+Poles $>$\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=1.266968cm]{structIir_1_1PoleFilter}
\end{center}
\end{figure}
\doxysubsubsection*{Additional Inherited Members}


\doxysubsubsection{Detailed Description}
\subsubsection*{template$<$class Base\+Class, class State\+Type, int Max\+Analog\+Poles, int Max\+Digital\+Poles = Max\+Analog\+Poles$>$\newline
struct Iir\+::\+Pole\+Filter$<$ Base\+Class, State\+Type, Max\+Analog\+Poles, Max\+Digital\+Poles $>$}

Storage for pole filters 

The documentation for this struct was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
iir/Pole\+Filter.\+h\end{DoxyCompactItemize}
