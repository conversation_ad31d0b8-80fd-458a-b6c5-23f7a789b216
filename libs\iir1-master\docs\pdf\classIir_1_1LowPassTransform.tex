\hypertarget{classIir_1_1LowPassTransform}{}\doxysubsection{Iir\+::Low\+Pass\+Transform Class Reference}
\label{classIir_1_1LowPassTransform}\index{Iir::LowPassTransform@{Iir::LowPassTransform}}


{\ttfamily \#include $<$Pole\+Filter.\+h$>$}



\doxysubsubsection{Detailed Description}
s-\/plane to z-\/plane transforms

For pole filters, an analog prototype is created via placement of poles and zeros in the s-\/plane. The analog prototype is either a halfband low pass or a halfband low shelf. The poles, zeros, and normalization parameters are transformed into the z-\/plane using variants of the bilinear transformation. low pass to low pass 

The documentation for this class was generated from the following files\+:\begin{DoxyCompactItemize}
\item 
iir/Pole\+Filter.\+h\item 
iir/Pole\+Filter.\+cpp\end{DoxyCompactItemize}
