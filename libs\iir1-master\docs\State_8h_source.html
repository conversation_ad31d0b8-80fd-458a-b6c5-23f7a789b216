<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>iir1: iir/State.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">iir1
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_5231e4e0b49b19c482755f817f3ac47e.html">iir</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">State.h</div>  </div>
</div><!--header-->
<div class="contents">
<div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160; </div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;<span class="preprocessor">#ifndef IIR1_STATE_H</span></div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;<span class="preprocessor">#define IIR1_STATE_H</span></div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160; </div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;<span class="preprocessor">#include &quot;Common.h&quot;</span></div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;<span class="preprocessor">#include &quot;Biquad.h&quot;</span></div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160; </div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160; </div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;<span class="preprocessor">#define DEFAULT_STATE DirectFormII</span></div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160; </div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespaceIir.html">Iir</a> {</div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160; </div>
<div class="line"><a name="l00055"></a><span class="lineno"><a class="line" href="classIir_1_1DirectFormI.html">   55</a></span>&#160;        <span class="keyword">class </span>IIR_EXPORT <a class="code" href="classIir_1_1DirectFormI.html">DirectFormI</a></div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;        {</div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;        <span class="keyword">public</span>:</div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;        <a class="code" href="classIir_1_1DirectFormI.html">DirectFormI</a> () = <span class="keywordflow">default</span>;</div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;        </div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;        <span class="keywordtype">void</span> reset ()</div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;        {</div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;                m_x1 = 0;</div>
<div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;                m_x2 = 0;</div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;                m_y1 = 0;</div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;                m_y2 = 0;</div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;        }</div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;        </div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;        <span class="keyword">inline</span> <span class="keywordtype">double</span> filter(<span class="keyword">const</span> <span class="keywordtype">double</span> in,</div>
<div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;                             <span class="keyword">const</span> <a class="code" href="classIir_1_1Biquad.html">Biquad</a>&amp; s)</div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;        {</div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;                <span class="keyword">const</span> <span class="keywordtype">double</span> out = s.m_b0*in + s.m_b1*m_x1 + s.m_b2*m_x2</div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;                - s.m_a1*m_y1 - s.m_a2*m_y2;</div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;                m_x2 = m_x1;</div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;                m_y2 = m_y1;</div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;                m_x1 = in;</div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;                m_y1 = out;</div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;                </div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;                <span class="keywordflow">return</span> out;</div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;        }</div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;        </div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;        <span class="keyword">protected</span>:</div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;        <span class="keywordtype">double</span> m_x2 = 0.0; <span class="comment">// x[n-2]</span></div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;        <span class="keywordtype">double</span> m_y2 = 0.0; <span class="comment">// y[n-2]</span></div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;        <span class="keywordtype">double</span> m_x1 = 0.0; <span class="comment">// x[n-1]</span></div>
<div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;        <span class="keywordtype">double</span> m_y1 = 0.0; <span class="comment">// y[n-1]</span></div>
<div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;        };</div>
<div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160; </div>
<div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;<span class="comment">//------------------------------------------------------------------------------</span></div>
<div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160; </div>
<div class="line"><a name="l00099"></a><span class="lineno"><a class="line" href="classIir_1_1DirectFormII.html">   99</a></span>&#160;        <span class="keyword">class </span>IIR_EXPORT <a class="code" href="classIir_1_1DirectFormII.html">DirectFormII</a></div>
<div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;        {</div>
<div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;        <span class="keyword">public</span>:</div>
<div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;        <a class="code" href="classIir_1_1DirectFormII.html">DirectFormII</a> () = <span class="keywordflow">default</span>;</div>
<div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;        </div>
<div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;        <span class="keywordtype">void</span> reset ()</div>
<div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;        {</div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;                m_v1 = 0.0;</div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;                m_v2 = 0.0;</div>
<div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;        }</div>
<div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;        </div>
<div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;        <span class="keyword">inline</span> <span class="keywordtype">double</span> filter(<span class="keyword">const</span> <span class="keywordtype">double</span> in,</div>
<div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;                             <span class="keyword">const</span> <a class="code" href="classIir_1_1Biquad.html">Biquad</a>&amp; s)</div>
<div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;        {</div>
<div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;                <span class="keyword">const</span> <span class="keywordtype">double</span> w   = in - s.m_a1*m_v1 - s.m_a2*m_v2;</div>
<div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;                <span class="keyword">const</span> <span class="keywordtype">double</span> out =      s.m_b0*w    + s.m_b1*m_v1 + s.m_b2*m_v2;</div>
<div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;                </div>
<div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;                m_v2 = m_v1;</div>
<div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;                m_v1 = w;</div>
<div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;                </div>
<div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;                <span class="keywordflow">return</span> out;</div>
<div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;        }</div>
<div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;        </div>
<div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;        <span class="keyword">private</span>:</div>
<div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;        <span class="keywordtype">double</span> m_v1 = 0.0; <span class="comment">// v[-1]</span></div>
<div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;        <span class="keywordtype">double</span> m_v2 = 0.0; <span class="comment">// v[-2]</span></div>
<div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;        };</div>
<div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160; </div>
<div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160; </div>
<div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;<span class="comment">//------------------------------------------------------------------------------</span></div>
<div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;        </div>
<div class="line"><a name="l00130"></a><span class="lineno"><a class="line" href="classIir_1_1TransposedDirectFormII.html">  130</a></span>&#160;        <span class="keyword">class </span>IIR_EXPORT <a class="code" href="classIir_1_1TransposedDirectFormII.html">TransposedDirectFormII</a></div>
<div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;        {</div>
<div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;        <span class="keyword">public</span>:</div>
<div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;        <a class="code" href="classIir_1_1TransposedDirectFormII.html">TransposedDirectFormII</a>() = <span class="keywordflow">default</span>;</div>
<div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;        </div>
<div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;        <span class="keywordtype">void</span> reset ()</div>
<div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;        {</div>
<div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;                m_s1 = 0.0;</div>
<div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;                m_s1_1 = 0.0;</div>
<div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;                m_s2 = 0.0;</div>
<div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;                m_s2_1 = 0.0;</div>
<div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;        }</div>
<div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;        </div>
<div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;        <span class="keyword">inline</span> <span class="keywordtype">double</span> filter(<span class="keyword">const</span> <span class="keywordtype">double</span> in,</div>
<div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;                             <span class="keyword">const</span> <a class="code" href="classIir_1_1Biquad.html">Biquad</a>&amp; s)</div>
<div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;        {</div>
<div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;                <span class="keyword">const</span> <span class="keywordtype">double</span> out = m_s1_1 + s.m_b0*in;</div>
<div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;                m_s1 = m_s2_1 + s.m_b1*in - s.m_a1*out;</div>
<div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;                m_s2 = s.m_b2*in - s.m_a2*out;</div>
<div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;                m_s1_1 = m_s1;</div>
<div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;                m_s2_1 = m_s2;</div>
<div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;                </div>
<div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;                <span class="keywordflow">return</span> out;</div>
<div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;        }</div>
<div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;        </div>
<div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;        <span class="keyword">private</span>:</div>
<div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;        <span class="keywordtype">double</span> m_s1 = 0.0;</div>
<div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;        <span class="keywordtype">double</span> m_s1_1 = 0.0;</div>
<div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;        <span class="keywordtype">double</span> m_s2 = 0.0;</div>
<div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;        <span class="keywordtype">double</span> m_s2_1 = 0.0;</div>
<div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;        };</div>
<div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160; </div>
<div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;}</div>
<div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160; </div>
<div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="ttc" id="aclassIir_1_1Biquad_html"><div class="ttname"><a href="classIir_1_1Biquad.html">Iir::Biquad</a></div><div class="ttdef"><b>Definition:</b> Biquad.h:52</div></div>
<div class="ttc" id="aclassIir_1_1DirectFormII_html"><div class="ttname"><a href="classIir_1_1DirectFormII.html">Iir::DirectFormII</a></div><div class="ttdef"><b>Definition:</b> State.h:100</div></div>
<div class="ttc" id="aclassIir_1_1DirectFormI_html"><div class="ttname"><a href="classIir_1_1DirectFormI.html">Iir::DirectFormI</a></div><div class="ttdef"><b>Definition:</b> State.h:56</div></div>
<div class="ttc" id="aclassIir_1_1TransposedDirectFormII_html"><div class="ttname"><a href="classIir_1_1TransposedDirectFormII.html">Iir::TransposedDirectFormII</a></div><div class="ttdef"><b>Definition:</b> State.h:131</div></div>
<div class="ttc" id="anamespaceIir_html"><div class="ttname"><a href="namespaceIir.html">Iir</a></div><div class="ttdef"><b>Definition:</b> Biquad.cpp:40</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Fri Jan 6 2023 23:53:32 for iir1 by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
