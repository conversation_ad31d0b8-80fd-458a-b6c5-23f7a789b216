{"headers": ["显示名称", "步骤信息", "备注"], "tree": [{"checked": true, "children": [{"checked": true, "params": {}, "texts": ["初始化序列结束", "", ""]}], "params": {}, "texts": ["初始化序列", "", ""]}, {"checked": true, "children": [{"checked": true, "params": {"select": 2, "select1": 2, "selectedInputChannel": "InChannel_0", "selectedInputConfig": {"Channel_Gain": 1, "Channel_Max_Value": 1, "Channel_Min_Value": -1, "DriverID": 129, "DriverTypeIndexes": 0, "Driver_Name": "LISSIG USB Audio Device", "Driver_Type": "ASIO", "Sound_Card_Channel_Name": "IN_1"}, "selectedOutputChannel": "OutChannel_0", "selectedOutputConfig": {"Channel_Gain": 1, "Channel_Max_Value": 1, "Channel_Min_Value": -1, "DriverID": 129, "DriverTypeIndexes": 0, "Driver_Name": "LISSIG USB Audio Device", "Driver_Type": "ASIO", "Sound_Card_Channel_Name": "OUT_1"}, "selectedSensor": "Mic", "selectedSensorConfig": {"calibrationDays": 365, "frequency": 0, "isExpired": false, "lastCalibrationDate": "2025-08-09", "name": "Mic", "nextCalibrationDate": "2026-08-09", "periodicCheck": false, "sensitivity": 0, "sortOrder": 0, "type": "microphone", "unit": "mV/Pa"}, "selectedStimConfig": {"Gain_constant": 1, "PS_stepped_customized_freq": [], "PS_stepped_freq_type": 0, "PS_stepped_points": 10, "PS_stepped_start_freq": 20, "PS_stepped_step_type": 0, "PS_stepped_stop_freq": 20000, "Step_sweep_custom_freq_circles": [], "Step_sweep_custom_freq_points": [], "Step_sweep_fre_point_type": 0, "Step_sweep_min_circles": 10, "Step_sweep_min_duration": 0.01, "Step_sweep_octave_type": 0, "Step_sweep_start_freq": 20, "Step_sweep_stop_freq": 20000, "conlogsweep_delay_duration": 0.01, "conlogsweep_duration": 1, "conlogsweep_pre_duration": 0.01, "conlogsweep_start_freq": 20, "conlogsweep_stop_freq": 20000, "gain_dB": 0, "level_step_amplitudeType": 0, "level_step_customized_amplitude": [], "level_step_frequency": 1000, "level_step_levelType": 0, "level_step_points": 10, "level_step_start_level": 0, "level_step_step_circles": 10, "level_step_stop_level": 1, "output_level": 1, "sample_rate": 44100, "signal_amplitude": 1, "signal_unit": 1, "stimulus_signal_type": 0}, "selectedStimSet": "AMouth", "selectedStimSetConfig": {"calibrationDays": 365, "isExpired": false, "lastCalibrationDate": "2025-08-09", "name": "AMouth", "nextCalibrationDate": "2026-08-09", "periodicCheck": false, "sortOrder": 0, "type": "artificial_mouth"}, "selectedStimSignal": "12", "type": "频响测试（单通道）"}, "texts": ["342", "频响测试（单通道）", ""]}, {"checked": true, "params": {"select": 2, "select1": 0, "selectedInputChannel": "InChannel_0", "selectedInputConfig": {"Channel_Gain": 1, "Channel_Max_Value": 1, "Channel_Min_Value": -1, "DriverID": 129, "DriverTypeIndexes": 0, "Driver_Name": "LISSIG USB Audio Device", "Driver_Type": "ASIO", "Sound_Card_Channel_Name": "IN_1"}, "selectedOutputChannel": "OutChannel_0", "selectedOutputConfig": {"Channel_Gain": 1, "Channel_Max_Value": 1, "Channel_Min_Value": -1, "DriverID": 129, "DriverTypeIndexes": 0, "Driver_Name": "LISSIG USB Audio Device", "Driver_Type": "ASIO", "Sound_Card_Channel_Name": "OUT_1"}, "selectedSensor": "Mic", "selectedSensorConfig": {"calibrationDays": 365, "frequency": 0, "isExpired": false, "lastCalibrationDate": "2025-08-09", "name": "Mic", "nextCalibrationDate": "2026-08-09", "periodicCheck": false, "sensitivity": 0, "sortOrder": 0, "type": "microphone", "unit": "mV/Pa"}, "selectedStimConfig": {"Gain_constant": 1, "PS_stepped_customized_freq": [], "PS_stepped_freq_type": 0, "PS_stepped_points": 10, "PS_stepped_start_freq": 20, "PS_stepped_step_type": 0, "PS_stepped_stop_freq": 20000, "Step_sweep_custom_freq_circles": [], "Step_sweep_custom_freq_points": [], "Step_sweep_fre_point_type": 0, "Step_sweep_min_circles": 10, "Step_sweep_min_duration": 0.01, "Step_sweep_octave_type": 0, "Step_sweep_start_freq": 20, "Step_sweep_stop_freq": 20000, "conlogsweep_delay_duration": 0.01, "conlogsweep_duration": 1, "conlogsweep_pre_duration": 0.01, "conlogsweep_start_freq": 20, "conlogsweep_stop_freq": 20000, "gain_dB": 0, "level_step_amplitudeType": 0, "level_step_customized_amplitude": [], "level_step_frequency": 1000, "level_step_levelType": 0, "level_step_points": 10, "level_step_start_level": 0, "level_step_step_circles": 10, "level_step_stop_level": 1, "output_level": 1, "sample_rate": 44100, "signal_amplitude": 1, "signal_unit": 1, "stimulus_signal_type": 0}, "selectedStimSet": "AMouth", "selectedStimSetConfig": {"calibrationDays": 365, "isExpired": false, "lastCalibrationDate": "2025-08-09", "name": "AMouth", "nextCalibrationDate": "2026-08-09", "periodicCheck": false, "sortOrder": 0, "type": "artificial_mouth"}, "selectedStimSignal": "12", "type": "频响测试（单通道）"}, "texts": ["123", "频响测试（单通道）", ""]}, {"checked": true, "params": {}, "texts": ["主序列结束", "", ""]}], "params": {}, "texts": ["主序列", "", ""]}, {"checked": true, "children": [{"checked": true, "params": {}, "texts": ["结束序列结束", "", ""]}], "params": {}, "texts": ["结束序列", "", ""]}]}