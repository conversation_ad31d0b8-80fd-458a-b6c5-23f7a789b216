﻿#include "audioprocessor.h"

#define FORMAT RTAUDIO_FLOAT32
#define SCALE 1.0f
#define PI 3.14159265358979323846
unsigned int testiiiii=0;
unsigned int testOOOOO=0;
AudioProcessor::AudioProcessor(QObject* parent)
    : QObject(parent),
//      bufferFrames_(512),
      //useBuffer1_(true),
      //displayPoints_(static_cast<unsigned int>(44100 * 0.2)),
      configManager_("config/Channel_Info.json"),
      displayTimer(new QTimer(this)) {
    if (!configManager_.loadFromFile()) {
        qDebug() << "Failed to load channel configurations";
        emit errorOccurred("Failed to load channel configurations");
    }
    updateSoundCardConfig(); // 新增：调用声卡配置更新函数，初始化设备 ID
    connect(displayTimer, &QTimer::timeout, this, &AudioProcessor::updateAllDisplayBuffers);//确保每次定时器超时时，自动调用 updateAllDisplayBuffers 来更新音频数据的显示。
    displayTimer->start(100); //启动定时器
}

AudioProcessor::~AudioProcessor() {
    stopStream({}, {});
    if (displayTimer->isActive()) {
        QMetaObject::invokeMethod(displayTimer, "stop", Qt::QueuedConnection);
    }
    displayTimer->deleteLater();
    asioHosts.clear();
    wasapiHosts.clear();
    registeredChannels.clear();
}


bool AudioProcessor::updateSoundCardConfig() { // 搜索 ASIO 和 WASAPI 声卡，更新配置
    QMutexLocker locker(&mutex_); // 创建互斥锁，保护 configManager_ 访问
    bool updated = false; // 标记是否更新了配置

    // 创建 ASIO 和 WASAPI 的 RtAudio 实例
    QSharedPointer<RtAudio> asioRtAudio = QSharedPointer<RtAudio>(new RtAudio(RtAudio::WINDOWS_ASIO)); // 初始化 ASIO 实例
    QSharedPointer<RtAudio> wasapiRtAudio = QSharedPointer<RtAudio>(new RtAudio(RtAudio::WINDOWS_WASAPI)); // 初始化 WASAPI 实例
    QMap<QString, unsigned int> asioDevices; // 存储 ASIO 设备名称和 ID
    QMap<QString, unsigned int> wasapiDevices; // 存储 WASAPI 设备名称和 ID
    //qDebug() << "updateDeviceCache1";



    std::vector<unsigned int> asioDeviceIds = asioRtAudio->getDeviceIds();
    if (asioDeviceIds.size()  > 0) { // 检查是否有 ASIO 设备
        for (unsigned int id : asioDeviceIds) { // 遍历 ASIO 设备
            RtAudio::DeviceInfo info = asioRtAudio->getDeviceInfo(id); // 获取设备信息
            if (info.ID != 0) { // 确保设备信息有效
                asioDevices[QString::fromStdString(info.name)] = info.ID; // 存储设备名称和 ID
               // qDebug() << "搜索ASIO声卡driverNamedriverType" << info.ID;
               // qDebug() << "搜索ASIO声卡driverNamedriverName" << QString::fromStdString(info.name);
            }
        }
    }

    // 搜索 WASAPI 声卡
    asioDeviceIds = wasapiRtAudio->getDeviceIds();
    if (wasapiRtAudio->getDeviceCount() > 0) { // 检查是否有 WASAPI 设备
        for (unsigned int id : asioDeviceIds) { // 遍历 WASAPI 设备
            RtAudio::DeviceInfo info = wasapiRtAudio->getDeviceInfo(id); // 获取设备信息
            if (info.ID != 0) { // 确保设备信息有效
                wasapiDevices[QString::fromStdString(info.name)] = info.ID; // 存储设备名称和 ID
                //qDebug() << "搜索WASAPI声卡driverNamedriverType" << info.ID;
                 //qDebug() << "搜索WASAPI声卡driverNamedriverName" << QString::fromStdString(info.name);
            }
        }
    }

    // 更新输入通道配置
    for (auto it = configManager_.audioInputs[AudioChannelConfigManager::ChannelType::isInput].begin(); it != configManager_.audioInputs[AudioChannelConfigManager::ChannelType::isInput].end(); ++it) { // 遍历输入通道
        auto& config = it.value(); // 获取通道配置
        QString driverType = config.Driver_Type == "WDM" ? "WASAPI" : config.Driver_Type; // 确定驱动类型，WDM 转换为 WASAPI
        QString driverName = config.Driver_Name; // 获取声卡名称
        unsigned int currentId = config.DriverID; // 获取当前配置的设备 ID
//        qDebug() << "driverNamedriverType" << driverType;
//         qDebug() << "driverNamedriverName" << driverName;
//          qDebug() << "driverNamecurrentId" << currentId;
        if (driverType == "ASIO" && asioDevices.contains(driverName)) { // 如果是 ASIO 且设备存在
            unsigned int newId = asioDevices[driverName]; // 获取最新设备 ID
            if (newId != currentId) { // 检查 ID 是否变化
                config.DriverID = newId; // 更新配置中的设备 ID
                updated = true; // 标记已更新
                qDebug() << "Updated ASIO device" << driverName << "ID from" << currentId << "to" << newId; // 打印调试信息
            }
        } else if (driverType == "WASAPI" && wasapiDevices.contains(driverName)) { // 如果是 WASAPI 且设备存在
            unsigned int newId = wasapiDevices[driverName]; // 获取最新设备 ID
            if (newId != currentId) { // 检查 ID 是否变化
                config.DriverID = newId; // 更新配置中的设备 ID
                updated = true; // 标记已更新
                qDebug() << "Updated WASAPI device" << driverName << "ID from" << currentId << "to" << newId; // 打印调试信息
            }
        }
        else{
             qDebug() << "updateSoundCardConfig不知道输入类型" ;
        }
    }

    // 更新输出通道配置
    for (auto it = configManager_.audioOutputs[AudioChannelConfigManager::ChannelType::isOutput].begin(); it != configManager_.audioOutputs[AudioChannelConfigManager::ChannelType::isOutput].end(); ++it) { // 遍历输出通道
        auto& config = it.value(); // 获取通道配置
        QString driverType = config.Driver_Type == "WDM" ? "WASAPI" : config.Driver_Type; // 确定驱动类型
        QString driverName = config.Driver_Name; // 获取声卡名称
        unsigned int currentId = config.DriverID; // 获取当前配置的设备 ID
//        qDebug() << "driverNamedriverType" << driverType;
//         qDebug() << "driverNamedriverName" << driverName;
//          qDebug() << "driverNamecurrentId" << currentId;
        if (driverType == "ASIO" && asioDevices.contains(driverName)) { // 如果是 ASIO 且设备存在
            unsigned int newId = asioDevices[driverName]; // 获取最新设备 ID
            if (newId != currentId) { // 检查 ID 是否变化
                config.DriverID = newId; // 更新配置中的设备 ID
                updated = true; // 标记已更新
                qDebug() << "Updated ASIO device" << driverName << "ID from" << currentId << "to" << newId; // 打印调试信息
            }
        } else if (driverType == "WASAPI" && wasapiDevices.contains(driverName)) { // 如果是 WASAPI 且设备存在
            unsigned int newId = wasapiDevices[driverName]; // 获取最新设备 ID
            if (newId != currentId) { // 检查 ID 是否变化
                config.DriverID = newId; // 更新配置中的设备 ID
                updated = true; // 标记已更新
                qDebug() << "Updated WASAPI device" << driverName << "ID from" << currentId << "to" << newId; // 打印调试信息
            }
        }
        else{
             qDebug() << "updateSoundCardConfig不知道输出类型" ;
        }
    }

    if (updated) { // 如果配置有更新
         qDebug() << "updated成功"; //
//        if (!configManager_.saveToFile()) { // 尝试保存到 JSON 文件
//            qDebug() << "Failed to save updated channel configurations"; // 打印调试信息：保存失败
//            emit errorOccurred("Failed to save updated channel configurations"); // 发出错误信号
//            return false; // 返回 false，表示更新失败
//        }
    }

    return true; // 返回 true，表示更新成功或无需更新
}


bool AudioProcessor::addChannels(const QStringList& inputChannels, const QStringList& outputChannels, // 注册输入和输出通道
                                unsigned int sampleRate, QString& errorMessage) { // 参数：输入通道列表、输出通道列表、采样率、错误信息
    QMutexLocker locker(&mutex_); // 创建互斥锁，保护对主机和通道的访问

    for (const QString& channelName : inputChannels) { // 遍历输入通道列表
        if (!configManager_.audioInputs[AudioChannelConfigManager::ChannelType::isInput].contains(channelName)) { // 检查通道是否在配置中存在
            errorMessage = QString("Input channel %1 not found").arg(channelName); // 设置错误信息：输入通道未找到
            return false; // 返回 false，表示注册失败
        }
        if (registeredChannels.contains(channelName)) { // 检查通道是否已被其他界面注册
            errorMessage = QString("Input channel %1 already in use").arg(channelName); // 设置错误信息：输入通道已被使用
            return false; // 返回 false，表示注册失败
        }
    }
    for (const QString& channelName : outputChannels) { // 遍历输出通道列表
        if (!configManager_.audioOutputs[AudioChannelConfigManager::ChannelType::isOutput].contains(channelName)) { // 检查通道是否在配置中存在
            errorMessage = QString("Output channel %1 not found").arg(channelName); // 设置错误信息：输出通道未找到
            return false; // 返回 false，表示注册失败
        }
        if (registeredChannels.contains(channelName)) { // 检查通道是否已被其他界面注册
            errorMessage = QString("Output channel %1 already in use").arg(channelName); // 设置错误信息：输出通道已被使用
            return false; // 返回 false，表示注册失败
        }
    }

    if (!validateChannels(inputChannels, outputChannels, sampleRate, errorMessage)) { // 验证通道有效性和采样率一致性
        return false; // 若验证失败，返回 false
    }

    for (const QString& channelName : inputChannels) { // 再次遍历输入通道列表，注册通道
        const auto& config = configManager_.audioInputs[AudioChannelConfigManager::ChannelType::isInput][channelName]; // 获取通道配置
        QString driverType = config.Driver_Type == "WDM" ? "WASAPI" : config.Driver_Type; // 确定驱动类型，若为 WDM 则转换为 WASAPI
        QString key = QString("%1_%2_%3").arg(config.Driver_Name).arg(driverType).arg(config.DriverID); // 生成设备键：Driver_Name_Driver_Type_DriverID
        auto info = QSharedPointer<ChannelInfo>::create(); // 创建 ChannelInfo 智能指针
        info->deviceName = channelName; // 设置通道名称
        info->deviceKey = key; // 设置设备键
        info->deviceId = config.DriverID; // 设置设备 ID
        info->api = driverType == "ASIO" ? RtAudio::WINDOWS_ASIO : RtAudio::WINDOWS_WASAPI; // 设置音频 API 类型
        info->channelIndex = config.Sound_Card_Channel_Name.split("_").last().toUInt() - 1; // 设置通道索引（从通道名称提取）
        info->gain = config.Channel_Gain; // 设置通道增益
        info->active = false; // 初始化为非活跃状态
        info->sampleRate = sampleRate; // 设置采样率
        info->displayPoints_ = sampleRate * 0.1;

        //qDebug() << "info->deviceId" << info->deviceId;
        //qDebug() << "info->channelIndex" << info->channelIndex;
        //qDebug() << "info->sampleRate" << info->sampleRate;
        //qDebug() << "info->displayPoints_" << info->displayPoints_;
        info->inputQueue = QSharedPointer<moodycamel::ConcurrentQueue<float>>( // 创建输入无锁队列
            new moodycamel::ConcurrentQueue<float>(sampleRate * 600)); // 队列容量为 10 分钟样本数
        registeredChannels[channelName] = info; // 将通道信息添加到已注册通道映射
        displayQueues_[isInput][channelName] = QSharedPointer<moodycamel::ConcurrentQueue<float>>( // 新增：创建输入显示队列
            new moodycamel::ConcurrentQueue<float>(sampleRate * 600));
        if (driverType == "ASIO") { // 如果是 ASIO 驱动
            if (!asioHosts.contains(key)) { // 检查是否已有对应的 ASIO 主机
                asioHosts[key] = QSharedPointer<AsioHost>(new AsioHost(key, this)); // 创建新的 ASIO 主机
            }
            if (!asioHosts[key]->addChannel(channelName, isInput, config, sampleRate, errorMessage)) { // 向 ASIO 主机添加通道
                registeredChannels.remove(channelName); // 若添加失败，移除已注册的通道
                displayQueues_[isInput].remove(channelName); // 新增：移除输入显示队列
                return false; // 返回 false，表示注册失败
            }
        } else { // 如果是 WASAPI 驱动
            if (!wasapiHosts.contains(key)) { // 检查是否已有对应的 WASAPI 主机
                wasapiHosts[key] = QSharedPointer<WasapiHost>(new WasapiHost(key, this)); // 创建新的 WASAPI 主机
            }
            if (!wasapiHosts[key]->addChannel(channelName, isInput, config, sampleRate, errorMessage)) { // 向 WASAPI 主机添加通道
                registeredChannels.remove(channelName); // 若添加失败，移除已注册的通道
                 displayQueues_[isInput].remove(channelName); // 新增：移除输入显示队列
                return false; // 返回 false，表示注册失败
            }
        }
    }

    for (const QString& channelName : outputChannels) { // 遍历输出通道列表，注册通道
        const auto& config = configManager_.audioOutputs[AudioChannelConfigManager::ChannelType::isOutput][channelName]; // 获取通道配置
        QString driverType = config.Driver_Type == "WDM" ? "WASAPI" : config.Driver_Type; // 确定驱动类型，若为 WDM 则转换为 WASAPI
        QString key = QString("%1_%2_%3").arg(config.Driver_Name).arg(driverType).arg(config.DriverID); // 生成设备键
        auto info = QSharedPointer<ChannelInfo>::create(); // 创建 ChannelInfo 智能指针
        info->deviceName = channelName; // 设置通道名称
        info->deviceKey = key; // 设置设备键
        info->deviceId = config.DriverID; // 设置设备 ID
        info->api = driverType == "ASIO" ? RtAudio::WINDOWS_ASIO : RtAudio::WINDOWS_WASAPI; // 设置音频 API 类型
        info->channelIndex = config.Sound_Card_Channel_Name.split("_").last().toUInt() - 1; // 设置通道索引
        info->gain = config.Channel_Gain; // 设置通道增益
        info->active = false; // 初始化为非活跃状态
        info->sampleRate = sampleRate; // 设置采样率
        info->displayPoints_ = sampleRate * 0.1;
        info->outputQueue = QSharedPointer<moodycamel::ConcurrentQueue<float>>( // 创建输出无锁队列
            new moodycamel::ConcurrentQueue<float>(sampleRate * 600)); // 队列容量为 10 分钟样本数
        registeredChannels[channelName] = info; // 将通道信息添加到已注册通道映射
       // displayBuffer1_[isOutput][channelName].resize(displayPoints_, 0.0); // 初始化显示缓冲区 1，输出通道
       // displayBuffer2_[isOutput][channelName].resize(displayPoints_, 0.0); // 初始化显示缓冲区 2，输出通道
        displayQueues_[isOutput][channelName] = QSharedPointer<moodycamel::ConcurrentQueue<float>>( // 新增：创建输出显示队列
                  new moodycamel::ConcurrentQueue<float>(sampleRate * 600));

        if (driverType == "ASIO") { // 如果是 ASIO 驱动
            if (!asioHosts.contains(key)) { // 检查是否已有对应的 ASIO 主机
                asioHosts[key] = QSharedPointer<AsioHost>(new AsioHost(key, this)); // 创建新的 ASIO 主机
            }
            if (!asioHosts[key]->addChannel(channelName, isOutput, config, sampleRate, errorMessage)) { // 向 ASIO 主机添加通道
                registeredChannels.remove(channelName); // 若添加失败，移除已注册的通道
                displayQueues_[isOutput].remove(channelName); // 新增：移除输出显示队列
                return false; // 返回 false，表示注册失败
            }
        } else { // 如果是 WASAPI 驱动
            if (!wasapiHosts.contains(key)) { // 检查是否已有对应的 WASAPI 主机
                wasapiHosts[key] = QSharedPointer<WasapiHost>(new WasapiHost(key, this)); // 创建新的 WASAPI 主机
            }
            if (!wasapiHosts[key]->addChannel(channelName, isOutput, config, sampleRate, errorMessage)) { // 向 WASAPI 主机添加通道
                registeredChannels.remove(channelName); // 若添加失败，移除已注册的通道
                displayQueues_[isOutput].remove(channelName); // 新增：移除输出显示队列
                return false; // 返回 false，表示注册失败
            }
        }
    }

    return true; // 返回 true，表示所有通道注册成功
}


bool AudioProcessor::validateChannels(const QStringList& inputChannels, const QStringList& outputChannels, // 验证输入和输出通道的有效性
                                     unsigned int sampleRate, QString& errorMessage) { // 参数：输入通道列表、输出通道列表、采样率、错误信息
    QSet<QString> deviceKeys; // 创建设备键集合，用于存储唯一设备标识
    QMap<QString, unsigned int> deviceSampleRates; // 创建设备采样率映射，存储每个设备的采样率

    for (const QString& channelName : inputChannels) { // 遍历输入通道列表
        if (!configManager_.audioInputs[AudioChannelConfigManager::ChannelType::isInput].contains(channelName)) { // 检查通道是否在配置中存在
            errorMessage = QString("Input channel %1 not found").arg(channelName); // 设置错误信息：输入通道未找到
            return false; // 返回 false，表示验证失败
        }
        const auto& config = configManager_.audioInputs[AudioChannelConfigManager::ChannelType::isInput][channelName]; // 获取通道配置
        QString driverType = config.Driver_Type == "WDM" ? "WASAPI" : config.Driver_Type; // 确定驱动类型，WDM 转换为 WASAPI
        QString key = QString("%1_%2_%3").arg(config.Driver_Name).arg(driverType).arg(config.DriverID); // 生成设备键：Driver_Name_Driver_Type_DriverID
        deviceKeys.insert(key); // 将设备键添加到集合
        if (deviceSampleRates.contains(key) && deviceSampleRates[key] != sampleRate) { // 检查同一设备的采样率是否一致
            errorMessage = QString("Sample rate %1 for device %2 conflicts with existing rate %3") // 设置错误信息：采样率冲突
                           .arg(sampleRate).arg(key).arg(deviceSampleRates[key]);
            return false; // 返回 false，表示验证失败
        }
        deviceSampleRates[key] = sampleRate; // 记录设备的采样率
    }
    for (const QString& channelName : outputChannels) { // 遍历输出通道列表
        if (!configManager_.audioOutputs[AudioChannelConfigManager::ChannelType::isOutput].contains(channelName)) { // 检查通道是否在配置中存在
            errorMessage = QString("Output channel %1 not found").arg(channelName); // 设置错误信息：输出通道未找到
            return false; // 返回 false，表示验证失败
        }
        const auto& config = configManager_.audioOutputs[AudioChannelConfigManager::ChannelType::isOutput][channelName]; // 获取通道配置
        QString driverType = config.Driver_Type == "WDM" ? "WASAPI" : config.Driver_Type; // 确定驱动类型，WDM 转换为 WASAPI
        QString key = QString("%1_%2_%3").arg(config.Driver_Name).arg(driverType).arg(config.DriverID); // 生成设备键
        deviceKeys.insert(key); // 将设备键添加到集合
        if (deviceSampleRates.contains(key) && deviceSampleRates[key] != sampleRate) { // 检查同一设备的采样率是否一致
            errorMessage = QString("Sample rate %1 for device %2 conflicts with existing rate %3") // 设置错误信息：采样率冲突
                           .arg(sampleRate).arg(key).arg(deviceSampleRates[key]);
            return false; // 返回 false，表示验证失败
        }
        deviceSampleRates[key] = sampleRate; // 记录设备的采样率
    }

    for (const QString& key : deviceKeys) { // 遍历所有设备键
        bool hasInput = false, hasOutput = false; // 初始化标志，表示设备是否有输入或输出通道
        for (const QString& channelName : inputChannels) { // 检查输入通道
            const auto& config = configManager_.audioInputs[AudioChannelConfigManager::ChannelType::isInput][channelName]; // 获取通道配置
            QString driverType = config.Driver_Type == "WDM" ? "WASAPI" : config.Driver_Type; // 确定驱动类型
            if (key == QString("%1_%2_%3").arg(config.Driver_Name).arg(driverType).arg(config.DriverID)) { // 匹配设备键
                hasInput = true; // 标记设备有输入通道
            }
        }
        for (const QString& channelName : outputChannels) { // 检查输出通道
            const auto& config = configManager_.audioOutputs[AudioChannelConfigManager::ChannelType::isOutput][channelName]; // 获取通道配置
            QString driverType = config.Driver_Type == "WDM" ? "WASAPI" : config.Driver_Type; // 确定驱动类型
            if (key == QString("%1_%2_%3").arg(config.Driver_Name).arg(driverType).arg(config.DriverID)) { // 匹配设备键
                hasOutput = true; // 标记设备有输出通道
            }
        }
        if (!hasInput && !hasOutput) { // 如果设备既无输入也无输出通道
            errorMessage = QString("No valid channels for device %1").arg(key); // 设置错误信息：设备无有效通道
            return false; // 返回 false，表示验证失败
        }
    }

    return true; // 返回 true，表示所有通道验证通过
}

void AudioProcessor::cleanupInFailedStream(const QStringList& inputChannels) { // 清理 startStream 失败的通道和声卡
    //QMutexLocker locker(&mutex_); // 创建互斥锁，保护 registeredChannels 和主机映射

    QSet<QString> devicesToCheck; // 存储需要检查的设备键集合
    for (const QString& channelName : inputChannels) { // 遍历输入通道列表
        if (registeredChannels.contains(channelName)) { // 检查通道是否已注册
            auto& info = registeredChannels[channelName]; // 获取通道信息
            devicesToCheck.insert(info->deviceKey); // 添加设备键到检查集合
            if (asioHosts.contains(info->deviceKey)) { // 如果是 ASIO 主机
                auto& stream = asioHosts[info->deviceKey]->getStream(); // 获取设备流
                stream.inputChannels.removeAll(info); // 从输入通道列表移除
                if (info->active) { // 如果通道是活跃的
                    stream.activeChannelCount--; // 减少活跃通道计数
                    info->active = false; // 标记通道为非活跃
                }
            } else if (wasapiHosts.contains(info->deviceKey)) { // 如果是 WASAPI 主机
                auto& stream = wasapiHosts[info->deviceKey]->getStream(); // 获取设备流
                stream.inputChannels.removeAll(info); // 从输入通道列表移除
                if (info->active) { // 如果通道是活跃的
                    stream.activeChannelCount--; // 减少活跃通道计数
                    info->active = false; // 标记通道为非活跃
                }
            }
            registeredChannels.remove(channelName); // 从已注册通道中移除
            displayQueues_[isInput].remove(channelName); // 从输入显示队列中移除
            qDebug() << "Removed input channel:" << channelName << "due to stream failure"; // 打印调试信息：移除输入通道
        }
    }


    for (const QString& key : devicesToCheck) { // 遍历需要检查的设备
        if (asioHosts.contains(key)) { // 如果是 ASIO 主机
            auto& host = asioHosts[key]; // 获取主机引用
            if (host->getStream().activeChannelCount == 0) { // 检查是否有活跃通道
                host->stopStream(); // 停止音频流
                asioHosts.remove(key); // 从 ASIO 主机映射中移除
                qDebug() << "Removed ASIO device:" << key << "due to no active channels"; // 打印调试信息：移除声卡
            }
        } else if (wasapiHosts.contains(key)) { // 如果是 WASAPI 主机
            auto& host = wasapiHosts[key]; // 获取主机引用
            if (host->getStream().activeChannelCount == 0) { // 检查是否有活跃通道
                host->stopStream(); // 停止音频流
                wasapiHosts.remove(key); // 从 WASAPI 主机映射中移除
                qDebug() << "Removed WASAPI device:" << key << "due to no active channels"; // 打印调试信息：移除声卡
            }
        }
    }
}

void AudioProcessor::cleanupOutFailedStream(const QStringList& outputChannels) { // 清理 startStream 失败的通道和声卡
    //QMutexLocker locker(&mutex_); // 创建互斥锁，保护 registeredChannels 和主机映射

    QSet<QString> devicesToCheck; // 存储需要检查的设备键集合
    for (const QString& channelName : outputChannels) { // 遍历输出通道列表
        if (registeredChannels.contains(channelName)) { // 检查通道是否已注册
            auto& info = registeredChannels[channelName]; // 获取通道信息
            devicesToCheck.insert(info->deviceKey); // 添加设备键到检查集合
            if (asioHosts.contains(info->deviceKey)) { // 如果是 ASIO 主机
                auto& stream = asioHosts[info->deviceKey]->getStream(); // 获取设备流
                stream.outputChannels.removeAll(info); // 从输出通道列表移除
                if (info->active) { // 如果通道是活跃的
                    stream.activeChannelCount--; // 减少活跃通道计数
                    info->active = false; // 标记通道为非活跃
                }
            } else if (wasapiHosts.contains(info->deviceKey)) { // 如果是 WASAPI 主机
                auto& stream = wasapiHosts[info->deviceKey]->getStream(); // 获取设备流
                stream.outputChannels.removeAll(info); // 从输出通道列表移除
                if (info->active) { // 如果通道是活跃的
                    stream.activeChannelCount--; // 减少活跃通道计数
                    info->active = false; // 标记通道为非活跃
                }
            }
            registeredChannels.remove(channelName); // 从已注册通道中移除
            displayQueues_[isOutput].remove(channelName); // 从输出显示队列中移除
            qDebug() << "Removed output channel:" << channelName << "due to stream failure"; // 打印调试信息：移除输出通道
        }
    }

    for (const QString& key : devicesToCheck) { // 遍历需要检查的设备
        if (asioHosts.contains(key)) { // 如果是 ASIO 主机
            auto& host = asioHosts[key]; // 获取主机引用
            if (host->getStream().activeChannelCount == 0) { // 检查是否有活跃通道
                host->stopStream(); // 停止音频流
                asioHosts.remove(key); // 从 ASIO 主机映射中移除
                qDebug() << "Removed ASIO device:" << key << "due to no active channels"; // 打印调试信息：移除声卡
            }
        } else if (wasapiHosts.contains(key)) { // 如果是 WASAPI 主机
            auto& host = wasapiHosts[key]; // 获取主机引用
            if (host->getStream().activeChannelCount == 0) { // 检查是否有活跃通道
                host->stopStream(); // 停止音频流
                wasapiHosts.remove(key); // 从 WASAPI 主机映射中移除
                qDebug() << "Removed WASAPI device:" << key << "due to no active channels"; // 打印调试信息：移除声卡
            }
        }
    }
}

void AudioProcessor::cleanupFailedStream(const QStringList& inputChannels, const QStringList& outputChannels) { // 清理 startStream 失败的通道和声卡
    //QMutexLocker locker(&mutex_); // 创建互斥锁，保护 registeredChannels 和主机映射

    QSet<QString> devicesToCheck; // 存储需要检查的设备键集合
    for (const QString& channelName : inputChannels) { // 遍历输入通道列表
        if (registeredChannels.contains(channelName)) { // 检查通道是否已注册
            auto& info = registeredChannels[channelName]; // 获取通道信息
            devicesToCheck.insert(info->deviceKey); // 添加设备键到检查集合
            if (asioHosts.contains(info->deviceKey)) { // 如果是 ASIO 主机
                auto& stream = asioHosts[info->deviceKey]->getStream(); // 获取设备流
                stream.inputChannels.removeAll(info); // 从输入通道列表移除
                if (info->active) { // 如果通道是活跃的
                    stream.activeChannelCount--; // 减少活跃通道计数
                    info->active = false; // 标记通道为非活跃
                }
            } else if (wasapiHosts.contains(info->deviceKey)) { // 如果是 WASAPI 主机
                auto& stream = wasapiHosts[info->deviceKey]->getStream(); // 获取设备流
                stream.inputChannels.removeAll(info); // 从输入通道列表移除
                if (info->active) { // 如果通道是活跃的
                    stream.activeChannelCount--; // 减少活跃通道计数
                    info->active = false; // 标记通道为非活跃
                }
            }
            registeredChannels.remove(channelName); // 从已注册通道中移除
            displayQueues_[isInput].remove(channelName); // 从输入显示队列中移除
            qDebug() << "Removed input channel:" << channelName << "due to stream failure"; // 打印调试信息：移除输入通道
        }
    }

    for (const QString& channelName : outputChannels) { // 遍历输出通道列表
        if (registeredChannels.contains(channelName)) { // 检查通道是否已注册
            auto& info = registeredChannels[channelName]; // 获取通道信息
            devicesToCheck.insert(info->deviceKey); // 添加设备键到检查集合
            if (asioHosts.contains(info->deviceKey)) { // 如果是 ASIO 主机
                auto& stream = asioHosts[info->deviceKey]->getStream(); // 获取设备流
                stream.outputChannels.removeAll(info); // 从输出通道列表移除
                if (info->active) { // 如果通道是活跃的
                    stream.activeChannelCount--; // 减少活跃通道计数
                    info->active = false; // 标记通道为非活跃
                }
            } else if (wasapiHosts.contains(info->deviceKey)) { // 如果是 WASAPI 主机
                auto& stream = wasapiHosts[info->deviceKey]->getStream(); // 获取设备流
                stream.outputChannels.removeAll(info); // 从输出通道列表移除
                if (info->active) { // 如果通道是活跃的
                    stream.activeChannelCount--; // 减少活跃通道计数
                    info->active = false; // 标记通道为非活跃
                }
            }
            registeredChannels.remove(channelName); // 从已注册通道中移除
            displayQueues_[isOutput].remove(channelName); // 从输出显示队列中移除
            qDebug() << "Removed output channel:" << channelName << "due to stream failure"; // 打印调试信息：移除输出通道
        }
    }

    for (const QString& key : devicesToCheck) { // 遍历需要检查的设备
        if (asioHosts.contains(key)) { // 如果是 ASIO 主机
            auto& host = asioHosts[key]; // 获取主机引用
            if (host->getStream().activeChannelCount == 0) { // 检查是否有活跃通道
                host->stopStream(); // 停止音频流
                asioHosts.remove(key); // 从 ASIO 主机映射中移除
                qDebug() << "Removed ASIO device:" << key << "due to no active channels"; // 打印调试信息：移除声卡
            }
        } else if (wasapiHosts.contains(key)) { // 如果是 WASAPI 主机
            auto& host = wasapiHosts[key]; // 获取主机引用
            if (host->getStream().activeChannelCount == 0) { // 检查是否有活跃通道
                host->stopStream(); // 停止音频流
                wasapiHosts.remove(key); // 从 WASAPI 主机映射中移除
                qDebug() << "Removed WASAPI device:" << key << "due to no active channels"; // 打印调试信息：移除声卡
            }
        }
    }
}

bool AudioProcessor::startStream(const QStringList& inputChannels, const QStringList& outputChannels, // 启动音频流
                                unsigned int sampleRate, QString& errorMessage) { // 参数：输入通道列表、输出通道列表、采样率、错误信息
    QMutexLocker locker(&mutex_); // 创建互斥锁，保护对主机和通道的访问
    testiiiii = 0;
    for (const QString& channelName : inputChannels) { // 遍历输入通道列表
        if (!registeredChannels.contains(channelName)) { // 检查通道是否已注册
            errorMessage = QString("Input channel %1 not registered").arg(channelName); // 设置错误信息：输入通道未注册
            cleanupInFailedStream(inputChannels); // 新增：失败时清理通道和声卡
            return false; // 返回 false，表示启动失败
        }
    }
    for (const QString& channelName : outputChannels) { // 遍历输出通道列表
        if (!registeredChannels.contains(channelName)) { // 检查通道是否已注册
            errorMessage = QString("Output channel %1 not registered").arg(channelName); // 设置错误信息：输出通道未注册
           cleanupOutFailedStream(outputChannels); // 新增：失败时清理通道和声卡
            return false; // 返回 false，表示启动失败
        }
    }

    QSet<QString> deviceKeys; // 创建设备键集合，存储需要启动的设备
    for (const QString& channelName : inputChannels) { // 遍历输入通道
        auto& info = registeredChannels[channelName]; // 获取通道信息
        deviceKeys.insert(info->deviceKey); // 添加设备键到集合
    }
    for (const QString& channelName : outputChannels) { // 遍历输出通道
        auto& info = registeredChannels[channelName]; // 获取通道信息
        deviceKeys.insert(info->deviceKey); // 添加设备键到集合
    }

    for (const QString& key : deviceKeys) { // 遍历所有设备键
        if (asioHosts.contains(key)) { // 如果设备是 ASIO 主机
            if (asioHosts[key]->isRunning()) { // 检查主机是否已在运行
                if (asioHosts[key]->getStream().sampleRate != sampleRate) { // 检查采样率是否一致
                    errorMessage = QString("Sample rate %1 for device %2 conflicts with running rate %3") // 设置错误信息：采样率冲突
                                   .arg(sampleRate).arg(key).arg(asioHosts[key]->getStream().sampleRate);
                    cleanupFailedStream(inputChannels, outputChannels); // 新增：失败时清理通道和声卡
                    return false; // 返回 false，表示启动失败
                }
            } else { // 如果主机未运行
                if (!asioHosts[key]->startStream(sampleRate, errorMessage)) { // 启动 ASIO 音频流
                    cleanupFailedStream(inputChannels, outputChannels); // 新增：失败时清理通道和声卡
                    return false; // 若启动失败，返回 false
                }
            }
        } else if (wasapiHosts.contains(key)) { // 如果设备是 WASAPI 主机
            if (wasapiHosts[key]->isRunning()) { // 检查主机是否已在运行
                if (wasapiHosts[key]->getStream().sampleRate != sampleRate) { // 检查采样率是否一致
                    errorMessage = QString("Sample rate %1 for device %2 conflicts with running rate %3") // 设置错误信息：采样率冲突
                                   .arg(sampleRate).arg(key).arg(wasapiHosts[key]->getStream().sampleRate);
                    cleanupFailedStream(inputChannels, outputChannels); // 新增：失败时清理通道和声卡
                    return false; // 返回 false，表示启动失败
                }
            } else { // 如果主机未运行
                if (!wasapiHosts[key]->startStream(sampleRate, errorMessage)) { // 启动 WASAPI 音频流
                    cleanupFailedStream(inputChannels, outputChannels); // 新增：失败时清理通道和声卡
                    return false; // 若启动失败，返回 false
                }
            }
        } else { // 如果设备键未找到对应主机
            errorMessage = QString("No host instance for device %1").arg(key); // 设置错误信息：无主机实例
            cleanupFailedStream(inputChannels, outputChannels); // 新增：失败时清理通道和声卡
            return false; // 返回 false，表示启动失败
        }
    }

    for (const QString& channelName : inputChannels) { // 遍历输入通道
        registeredChannels[channelName]->active = true; // 标记通道为活跃状态
    }
    for (const QString& channelName : outputChannels) { // 遍历输出通道
        registeredChannels[channelName]->active = true; // 标记通道为活跃状态
    }

    // 🚀 发送 streamLatency 信号
//     for (const QString& channelName : inputChannels + outputChannels) {
//         if (registeredChannels.contains(channelName)) {
//             const auto& info = registeredChannels[channelName];
//             QString deviceKey = info->deviceKey;

//             unsigned int latency = 0;
//             if (asioHosts.contains(deviceKey)) {
//                 latency = asioHosts[deviceKey]->getStream().streamLatency;
//             } else if (wasapiHosts.contains(deviceKey)) {
//                 latency = wasapiHosts[deviceKey]->getStream().streamLatency;
//             }

//             emit streamLatencyReported(channelName, latency);
//         }
//     }

    return true; // 返回 true，表示音频流启动成功
}

//void AudioProcessor::stopStream(const QStringList& inputChannels, const QStringList& outputChannels) {
//    QMutexLocker locker(&mutex_); // 创建互斥锁，保护对主机和通道的访问，确保线程安全

//    // 第一步：更新所有显示缓冲区，确保最后数据被处理
//    updateAllDisplayBuffers(); // 调用更新函数，将所有通道的 outputQueue 数据（包括补 0 数据）转移到 displayQueues_
//    qDebug() << "testiiiii" << testiiiii; // 调试输出：打印 testiiiii 计数器，用于跟踪回调入队次数
//    qDebug() << "testOOOOO" << testOOOOO; // 调试输出：打印 testOOOOO 计数器，用于跟踪入队失败情况

//    // 收集需要停止的设备
//    QSet<QString> devicesToStop; // 创建设备键集合，存储需要停止的设备标识（deviceKey）

//    // 处理输入通道
//    for (const QString& channelName : inputChannels) { // 遍历输入通道列表
//        if (registeredChannels.contains(channelName)) { // 检查通道是否在已注册通道映射中
//            auto& info = registeredChannels[channelName]; // 获取通道信息（ChannelInfo 智能指针）
//            devicesToStop.insert(info->deviceKey); // 将通道关联的设备键添加到待停止设备集合
//            if (info->active) { // 如果通道当前处于活跃状态
//                info->active = false; // 标记通道为非活跃，停止数据处理
//                if (asioHosts.contains(info->deviceKey)) { // 检查设备是否为 ASIO 主机
//                    asioHosts[info->deviceKey]->getStream().activeChannelCount--; // 减少 ASIO 主机的活跃通道计数
//                    asioHosts[info->deviceKey]->getStream().inputChannels.removeAll(info); // 从 ASIO 输入通道列表移除该通道
//                } else if (wasapiHosts.contains(info->deviceKey)) { // 检查设备是否为 WASAPI 主机
//                    wasapiHosts[info->deviceKey]->getStream().activeChannelCount--; // 减少 WASAPI 主机的活跃通道计数
//                    wasapiHosts[info->deviceKey]->getStream().inputChannels.removeAll(info); // 从 WASAPI 输入通道列表移除该通道
//                }
//                qDebug() << "Stopped input channel:" << channelName; // 调试输出：记录已停止的输入通道名称
//            }
//            registeredChannels.remove(channelName); // 从已注册通道映射中移除该通道
//            displayQueues_[isInput].remove(channelName); // 从输入显示队列映射中移除该通道
//        }
//    }

//    // 处理输出通道
//    for (const QString& channelName : outputChannels) { // 遍历输出通道列表
//        if (registeredChannels.contains(channelName)) { // 检查通道是否在已注册通道映射中
//            auto& info = registeredChannels[channelName]; // 获取通道信息（ChannelInfo 智能指针）
//            devicesToStop.insert(info->deviceKey); // 将通道关联的设备键添加到待停止设备集合
//            if (info->active || info->pendingRemoval) { // 如果通道活跃或标记为待移除
//                info->active = false; // 标记通道为非活跃，停止数据处理
//                if (asioHosts.contains(info->deviceKey)) { // 检查设备是否为 ASIO 主机
//                    auto& stream = asioHosts[info->deviceKey]->getStream(); // 获取 ASIO 设备流对象
//                    stream.activeChannelCount--; // 减少 ASIO 主机的活跃通道计数
//                    if (info->pendingRemoval) { // 如果通道标记为待移除
//                        emit playbackFinished(isOutput, channelName); // 触发播放完成信号，通知界面
//                        stream.outputChannels.removeAll(info); // 从 ASIO 输出通道列表移除该通道
//                        qDebug() << "Removed pending output channel:" << channelName; // 调试输出：记录移除的待移除输出通道
//                    }
//                } else if (wasapiHosts.contains(info->deviceKey)) { // 检查设备是否为 WASAPI 主机
//                    auto& stream = wasapiHosts[info->deviceKey]->getStream(); // 获取 WASAPI 设备流对象
//                    stream.activeChannelCount--; // 减少 WASAPI 主机的活跃通道计数
//                    if (info->pendingRemoval) { // 如果通道标记为待移除
//                        emit playbackFinished(isOutput, channelName); // 触发播放完成信号，通知界面
//                        stream.outputChannels.removeAll(info); // 从 WASAPI 输出通道列表移除该通道
//                        qDebug() << "Removed pending output channel:" << channelName; // 调试输出：记录移除的待移除输出通道
//                    }
//                }
//                qDebug() << "Stopped output channel:" << channelName; // 调试输出：记录已停止的输出通道名称
//            }
//            registeredChannels.remove(channelName); // 从已注册通道映射中移除该通道
//            displayQueues_[isOutput].remove(channelName); // 从输出显示队列映射中移除该通道
//        }
//    }

//    // 停止设备流并清理无活跃通道的设备
//    for (const QString& key : devicesToStop) { // 遍历需要停止的设备键集合
//        if (asioHosts.contains(key)) { // 检查是否存在对应的 ASIO 主机
//            auto& host = asioHosts[key]; // 获取 ASIO 主机引用
//            if (host->getStream().activeChannelCount == 0) { // 如果主机无活跃通道
//                host->stopStream(); // 调用主机停止流函数，关闭音频流
//                asioHosts.remove(key); // 从 ASIO 主机映射中移除该主机
//                qDebug() << "Unregistered ASIO device:" << key; // 调试输出：记录已注销的 ASIO 设备
//            }
//        } else if (wasapiHosts.contains(key)) { // 检查是否存在对应的 WASAPI 主机
//            auto& host = wasapiHosts[key]; // 获取 WASAPI 主机引用
//            if (host->getStream().activeChannelCount == 0) { // 如果主机无活跃通道
//                host->stopStream(); // 调用主机停止流函数，关闭音频流
//                wasapiHosts.remove(key); // 从 WASAPI 主机映射中移除该主机
//                qDebug() << "Unregistered WASAPI device:" << key; // 调试输出：记录已注销的 WASAPI 设备
//            }
//        }
//    }
//}
void AudioProcessor::stopStream(const QStringList& inputChannels, const QStringList& outputChannels) { // 停止音频流
    QMutexLocker locker(&mutex_); // 创建互斥锁，保护对主机和通道的访问
    updateAllDisplayBuffers();
    qDebug() << "testiiiii" << testiiiii;
    qDebug() << "testOOOOO" << testOOOOO;
    QSet<QString> devicesToStop; // 创建设备键集合，存储需要停止的设备
    for (const QString& channelName : inputChannels) { // 遍历输入通道列表
        if (registeredChannels.contains(channelName)) { // 检查通道是否已注册
            auto& info = registeredChannels[channelName]; // 获取通道信息
            if (info->active) { // 如果通道处于活跃状态
                info->active = false; // 标记通道为非活跃
                devicesToStop.insert(info->deviceKey); // 添加设备键到停止集合
                if (asioHosts.contains(info->deviceKey)) { // 如果是 ASIO 主机
                    asioHosts[info->deviceKey]->getStream().activeChannelCount--; // 减少活跃通道计数
                } else if (wasapiHosts.contains(info->deviceKey)) { // 如果是 WASAPI 主机
                    wasapiHosts[info->deviceKey]->getStream().activeChannelCount--; // 减少活跃通道计数
                }
                registeredChannels.remove(channelName); // 从已注册通道中移除
               // displayBuffer1_[isInput].remove(channelName); // 移除显示缓冲区 1 中的数据
               // displayBuffer2_[isInput].remove(channelName); // 移除显示缓冲区 2 中的数据
                 displayQueues_[isInput].remove(channelName); // 新增：移除输入显示队列
                qDebug() << "Stopped input channel:" << channelName; // 打印调试信息：停止输入通道
            }
            else{
                 registeredChannels.remove(channelName); // 从已注册通道中移除
            }
        }
    }
    for (const QString& channelName : outputChannels) { // 遍历输出通道列表
        if (registeredChannels.contains(channelName)) { // 检查通道是否已注册
            auto& info = registeredChannels[channelName]; // 获取通道信息
            if (info->active) { // 如果通道处于活跃状态
                info->active = false; // 标记通道为非活跃
                devicesToStop.insert(info->deviceKey); // 添加设备键到停止集合
                if (asioHosts.contains(info->deviceKey)) { // 如果是 ASIO 主机
                    asioHosts[info->deviceKey]->getStream().activeChannelCount--; // 减少活跃通道计数
                } else if (wasapiHosts.contains(info->deviceKey)) { // 如果是 WASAPI 主机
                    wasapiHosts[info->deviceKey]->getStream().activeChannelCount--; // 减少活跃通道计数
                }
                registeredChannels.remove(channelName); // 从已注册通道中移除
                //displayBuffer1_[isOutput].remove(channelName); // 移除显示缓冲区 1 中的数据
               // displayBuffer2_[isOutput].remove(channelName); // 移除显示缓冲区 2 中的数据
                displayQueues_[isOutput].remove(channelName); // 新增：移除输出显示队列
                qDebug() << "Stopped output channel:" << channelName; // 打印调试信息：停止输出通道
            }
            else{
                 registeredChannels.remove(channelName); // 从已注册通道中移除
            }
        }
    }

    for (const QString& key : devicesToStop) { // 遍历需要停止的设备
        if (asioHosts.contains(key)) { // 如果是 ASIO 主机
            auto& host = asioHosts[key]; // 获取主机引用
            if (host->getStream().activeChannelCount == 0) { // 如果主机无活跃通道
                host->stopStream(); // 停止音频流
                asioHosts.remove(key); // 从 ASIO 主机映射中移除
                qDebug() << "Unregistered ASIO device:" << key; // 打印调试信息：注销 ASIO 设备
            }
        } else if (wasapiHosts.contains(key)) { // 如果是 WASAPI 主机
            auto& host = wasapiHosts[key]; // 获取主机引用
            if (host->getStream().activeChannelCount == 0) { // 如果主机无活跃通道
                host->stopStream(); // 停止音频流
                wasapiHosts.remove(key); // 从 WASAPI 主机映射中移除
                qDebug() << "Unregistered WASAPI device:" << key; // 打印调试信息：注销 WASAPI 设备
            }
        }
    }
}



bool AudioProcessor::configureSignal(const QString& channelName, SignalType type, const QVariantMap& params) {
    QMutexLocker locker(&mutex_);

    if (!registeredChannels.contains(channelName)) {
        qDebug() << "Channel" << channelName << "not registered";
        emit errorOccurred(QString("Channel %1 not registered").arg(channelName));
        return false;
    }
    auto& info = registeredChannels[channelName];
    if (info->inputQueue) {
        qDebug() << "Cannot configure signal for input channel" << channelName;
        emit errorOccurred(QString("Cannot configure signal for input channel %1").arg(channelName));
        return false;
    }

    info->signalType = type;
    info->validSamples = 0;
    info->tempDataIndex = 0;
    info->tempData.clear();
    info->sampleOffset = 0;
    info->sineWaveData.clear();

    if (type == SignalType::Sine) {
        double frequency = params.value("frequency", 1000.0).toDouble();
        double amplitude = params.value("amplitude", 1.0).toDouble();

        int samples = info->sampleRate / frequency;
        if (samples < 1) samples = 1;
        info->sineWaveData.resize(samples);
        qDebug() << "samples" << samples;
        qDebug() << "sineWaveData size" << info->sineWaveData.size();
        for (int i = 0; i < samples; ++i) {
            info->sineWaveData[i] = amplitude * std::sin(2.0 * PI * i / samples);
             //qDebug() << "sineWaveData  " << info->sineWaveData[i] ;
        }
    } else if (type == SignalType::Sweep) {
        double startFreq = params.value("startFreq", 20.0).toDouble();
        double endFreq = params.value("endFreq", 20000.0).toDouble();
        double amplitude = params.value("amplitude", 1.0).toDouble();
        double cyclesPerFreq = params.value("cyclesPerFreq", 2.0).toDouble(); // 每个频率的周期数，默认 2
        double freqStep = params.value("freqStep", 1.0).toDouble(); // 频率步长，默认 1 Hz

        // 计算总样本数
        unsigned int totalSamples = 0;
        for (double freq = startFreq; freq <= endFreq; freq += freqStep) {
            double samplesPerFreq = (cyclesPerFreq * info->sampleRate) / freq; // 每个频率的样本数
            totalSamples += static_cast<unsigned int>(std::ceil(samplesPerFreq)); // 向上取整
        }
        info->validSamples = totalSamples;
        info->totalSamples = totalSamples;
        info->tempData.resize(totalSamples);

        // 生成扫频数据
        unsigned int sampleIndex = 0;
        for (double freq = startFreq; freq <= endFreq && sampleIndex < totalSamples; freq += freqStep) {
            double samplesPerFreq = (cyclesPerFreq * info->sampleRate) / freq;
            unsigned int numSamples = static_cast<unsigned int>(std::ceil(samplesPerFreq));
            for (unsigned int i = 0; i < numSamples && sampleIndex < totalSamples; ++i) {
                double t = static_cast<double>(i) / info->sampleRate; // 当前时间
                info->tempData[sampleIndex++] = amplitude * std::sin(2.0 * PI * freq * t); // 固定频率生成
            }
        }
    } else if (type == SignalType::Noise) {
        double amplitude = params.value("amplitude", 1.0).toDouble();
        double duration = params.value("duration", 10.0).toDouble();
        bool isPink = params.value("isPink", false).toBool();
        info->validSamples = static_cast<unsigned int>(duration * info->sampleRate);
        info->totalSamples = info->validSamples;
        info->tempData.resize(info->validSamples);

#ifdef _MSC_VER
        srand(static_cast<unsigned int>(time(nullptr)));
        if (isPink) {
            float prev = 0.0f;
            for (unsigned int i = 0; i < info->validSamples; ++i) {
                float white = (rand() / static_cast<float>(RAND_MAX)) * 2.0f - 1.0f;
                info->tempData[i] = amplitude * (0.5f * prev + 0.5f * white);
                prev = info->tempData[i];
            }
        } else {
            for (unsigned int i = 0; i < info->validSamples; ++i) {
                info->tempData[i] = amplitude * ((rand() / static_cast<float>(RAND_MAX)) * 2.0f - 1.0f);
            }
        }
#else
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_real_distribution<float> dis(-1.0f, 1.0f);
        if (isPink) {
            float prev = 0.0f;
            for (unsigned int i = 0; i < info->validSamples; ++i) {
                float white = dis(gen);
                info->tempData[i] = amplitude * (0.5f * prev + 0.5f * white);
                prev = info->tempData[i];
            }
        } else {
            for (unsigned int i = 0; i < info->validSamples; ++i) {
                info->tempData[i] = amplitude * dis(gen);
            }
        }
#endif
    } else if (type == SignalType::Wav) {
        QString filePath = params.value("filePath", "").toString();
        if (filePath.isEmpty()) {
            qDebug() << "WAV file path not provided";
            emit errorOccurred("WAV file path not provided");
            return false;
        }
        drwav wav;
        if (!drwav_init_file(&wav, filePath.toStdString().c_str(), nullptr)) {
            qDebug() << "Failed to load WAV file:" << filePath;
            emit errorOccurred(QString("Failed to load WAV file: %1").arg(filePath));
            return false;
        }
        if (wav.channels != 1 || wav.sampleRate != info->sampleRate) {
            drwav_uninit(&wav);
            qDebug() << "WAV file must be mono and match sample rate:" << info->sampleRate;
            emit errorOccurred(QString("WAV file must be mono and match sample rate %1").arg(info->sampleRate));
            return false;
        }
        info->validSamples = static_cast<unsigned int>(wav.totalPCMFrameCount);
        info->totalSamples = info->validSamples;
        info->tempData.resize(info->validSamples);
        drwav_read_pcm_frames_f32(&wav, info->validSamples, info->tempData.data());
        drwav_uninit(&wav);
    }

    info->active = true;
    qDebug() << "Configured signal for channel" << channelName << "type" << type;
    return true;
}
bool AudioProcessor::configureSignalForSweep(const QString& channelName, QVector<float> data) {
    QMutexLocker locker(&mutex_);

    if (!registeredChannels.contains(channelName)) {
        qDebug() << "Channel" << channelName << "not registered";
        emit errorOccurred(QString("Channel %1 not registered").arg(channelName));
        return false;
    }
    auto& info = registeredChannels[channelName];
    if (info->inputQueue) {
        qDebug() << "Cannot configure signal for input channel" << channelName;
        emit errorOccurred(QString("Cannot configure signal for input channel %1").arg(channelName));
        return false;
    }
     SignalType type = Sweep;
    info->signalType = type;
    info->validSamples = 0;
    info->tempDataIndex = 0;
    info->tempData.clear();
    info->sampleOffset = 0;
    info->sineWaveData.clear();
    // 计算总样本数
    info->validSamples = data.size();
    info->totalSamples = data.size();
    info->tempData.clear();
    info->tempData.resize(data.size());
    info->tempData = data.toStdVector();
    qDebug() << "info->validSamples" << info->validSamples;
    qDebug() << "info->totalSamples" << info->totalSamples;
    qDebug() << "info->tempData" << info->tempData.size();
    info->active = true;
    qDebug() << "Configured signal for channel" << channelName << "type" << type;
    return true;
}

bool AudioProcessor::configureSignalforCaculate(const QString& channelName, SignalType type, const QVariantMap& params,bool EnGain) {
    QMutexLocker locker(&mutex_);

    if (!registeredChannels.contains(channelName)) {
        qDebug() << "Channel" << channelName << "not registered";
        emit errorOccurred(QString("Channel %1 not registered").arg(channelName));
        return false;
    }
    auto& info = registeredChannels[channelName];
    if (info->inputQueue) {
        qDebug() << "Cannot configure signal for input channel" << channelName;
        emit errorOccurred(QString("Cannot configure signal for input channel %1").arg(channelName));
        return false;
    }

    info->signalType = type;
    info->validSamples = 0;
    info->tempDataIndex = 0;
    info->tempData.clear();
    info->sampleOffset = 0;
    info->sineWaveData.clear();

    if (type == SignalType::Sine) {
        double frequency = params.value("frequency", 1000.0).toDouble();
        double amplitude = params.value("amplitude", 1.0).toDouble();
        if(EnGain)
            amplitude = amplitude * info->gain;
        int samples = info->sampleRate / frequency;
        if (samples < 1) samples = 1;
        info->sineWaveData.resize(samples);
        qDebug() << "samples" << samples;
        qDebug() << "sineWaveData size" << info->sineWaveData.size();
        for (int i = 0; i < samples; ++i) {
            info->sineWaveData[i] = amplitude * std::sin(2.0 * PI * i / samples);
             //qDebug() << "sineWaveData  " << info->sineWaveData[i] ;
        }
    } else if (type == SignalType::Sweep) {
        double startFreq = params.value("startFreq", 20.0).toDouble();
        double endFreq = params.value("endFreq", 20000.0).toDouble();
        double amplitude = params.value("amplitude", 1.0).toDouble();
        double cyclesPerFreq = params.value("cyclesPerFreq", 2.0).toDouble(); // 每个频率的周期数，默认 2
        double freqStep = params.value("freqStep", 1.0).toDouble(); // 频率步长，默认 1 Hz

        // 计算总样本数
        unsigned int totalSamples = 0;
        for (double freq = startFreq; freq <= endFreq; freq += freqStep) {
            double samplesPerFreq = (cyclesPerFreq * info->sampleRate) / freq; // 每个频率的样本数
            totalSamples += static_cast<unsigned int>(std::ceil(samplesPerFreq)); // 向上取整
        }
        info->validSamples = totalSamples;
        info->totalSamples = totalSamples;
        info->tempData.resize(totalSamples);

        // 生成扫频数据
        unsigned int sampleIndex = 0;
        for (double freq = startFreq; freq <= endFreq && sampleIndex < totalSamples; freq += freqStep) {
            double samplesPerFreq = (cyclesPerFreq * info->sampleRate) / freq;
            unsigned int numSamples = static_cast<unsigned int>(std::ceil(samplesPerFreq));
            for (unsigned int i = 0; i < numSamples && sampleIndex < totalSamples; ++i) {
                double t = static_cast<double>(i) / info->sampleRate; // 当前时间
                info->tempData[sampleIndex++] = amplitude * std::sin(2.0 * PI * freq * t); // 固定频率生成
            }
        }
    } else if (type == SignalType::Noise) {
        double amplitude = params.value("amplitude", 1.0).toDouble();
        double duration = params.value("duration", 10.0).toDouble();
        bool isPink = params.value("isPink", false).toBool();
        info->validSamples = static_cast<unsigned int>(duration * info->sampleRate);
        info->totalSamples = info->validSamples;
        info->tempData.resize(info->validSamples);

#ifdef _MSC_VER
        srand(static_cast<unsigned int>(time(nullptr)));
        if (isPink) {
            float prev = 0.0f;
            for (unsigned int i = 0; i < info->validSamples; ++i) {
                float white = (rand() / static_cast<float>(RAND_MAX)) * 2.0f - 1.0f;
                info->tempData[i] = amplitude * (0.5f * prev + 0.5f * white);
                prev = info->tempData[i];
            }
        } else {
            for (unsigned int i = 0; i < info->validSamples; ++i) {
                info->tempData[i] = amplitude * ((rand() / static_cast<float>(RAND_MAX)) * 2.0f - 1.0f);
            }
        }
#else
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_real_distribution<float> dis(-1.0f, 1.0f);
        if (isPink) {
            float prev = 0.0f;
            for (unsigned int i = 0; i < info->validSamples; ++i) {
                float white = dis(gen);
                info->tempData[i] = amplitude * (0.5f * prev + 0.5f * white);
                prev = info->tempData[i];
            }
        } else {
            for (unsigned int i = 0; i < info->validSamples; ++i) {
                info->tempData[i] = amplitude * dis(gen);
            }
        }
#endif
    } else if (type == SignalType::Wav) {
        QString filePath = params.value("filePath", "").toString();
        if (filePath.isEmpty()) {
            qDebug() << "WAV file path not provided";
            emit errorOccurred("WAV file path not provided");
            return false;
        }
        drwav wav;
        if (!drwav_init_file(&wav, filePath.toStdString().c_str(), nullptr)) {
            qDebug() << "Failed to load WAV file:" << filePath;
            emit errorOccurred(QString("Failed to load WAV file: %1").arg(filePath));
            return false;
        }
        if (wav.channels != 1 || wav.sampleRate != info->sampleRate) {
            drwav_uninit(&wav);
            qDebug() << "WAV file must be mono and match sample rate:" << info->sampleRate;
            emit errorOccurred(QString("WAV file must be mono and match sample rate %1").arg(info->sampleRate));
            return false;
        }
        info->validSamples = static_cast<unsigned int>(wav.totalPCMFrameCount);
        info->totalSamples = info->validSamples;
        info->tempData.resize(info->validSamples);
        drwav_read_pcm_frames_f32(&wav, info->validSamples, info->tempData.data());
        drwav_uninit(&wav);
    }

    info->active = true;
    qDebug() << "Configured signal for channel" << channelName << "type" << type;
    return true;
}
bool AudioProcessor::getDisplayData(ChannelType type, const QString& channelName, unsigned int Len, std::vector<double>& data) { // 获取指定通道的显示数据，带样本数参数
    if (!displayQueues_[type].contains(channelName)) { // 检查指定类型的通道是否存在于显示队列中
        qDebug() << "Channel" << channelName << "not found in display queues"; // 打印调试信息：通道未找到
        return false; // 返回 false，表示通道不存在
    }

    auto& queue = displayQueues_[type][channelName]; // 获取通道对应的显示队列
    data.clear(); // 清空输出数据向量，确保无残留数据
    data.resize(Len); // 调整输出数据向量大小为指定样本数 Len
    std::vector<float> samples(Len); // 创建临时样本缓冲区，大小为 Len
    size_t samplesRead = 0;
    if(queue->size_approx() >= Len)
        samplesRead = queue->try_dequeue_bulk(samples.data(), Len); // 尝试从队列出队 Len 个样本
    else{
        //if (samplesRead < Len) { // 检查出队样本数是否达到要求
            qDebug() << "通道" << channelName << "样本" << queue->size_approx() << "不够 " << Len;
            return false; // 若样本不足，返回 false，不填充数据
        //}
    }
    for (size_t i = 0; i < samplesRead; ++i) { // 遍历出队的样本
        data[i] = static_cast<double>(samples[i]); // 将 float 样本转换为 double，存储到输出向量
    }

    return true; // 返回 true，表示成功获取 Len 个样本
}

bool AudioProcessor::getAllDisplayData(ChannelType type, const QString& channelName, QVector<double>& data) {
    if (!displayQueues_[type].contains(channelName)) { // 检查通道是否存在于显示队列
        qDebug() << "Channel" << channelName << "not found in display queues";
        return false;
    }

    auto& queue = displayQueues_[type][channelName]; // 获取通道对应的显示队列
    data.clear(); // 清空输出数据向量
    size_t availableSamples = queue->size_approx(); // 获取队列中所有可用样本数
    if (availableSamples == 0) { // 检查队列是否为空
        qDebug() << "No samples available for channel" << channelName;
        return false;
    }

    std::vector<float> samples(availableSamples); // 创建临时缓冲区，用于出队 float 数据
    size_t samplesRead = queue->try_dequeue_bulk(samples.data(), availableSamples); // 一次性出队所有样本
    if (samplesRead == 0) { // 确认是否成功出队
        qDebug() << "Failed to dequeue samples for channel" << channelName;
        return false;
    }

    data.resize(samplesRead); // 调整输出向量大小为实际读取的样本数
    for (size_t i = 0; i < samplesRead; ++i) { // 转换 float 到 double
        data[i] = static_cast<double>(samples[i]);
    }

    //qDebug() << "Retrieved" << samplesRead << "samples for channel" << channelName;
    return true; // 返回 true，表示成功获取所有数据
}



bool AudioProcessor::isDeviceRunning(const QString& deviceName) const {
    for (auto it = asioHosts.constBegin(); it != asioHosts.constEnd(); ++it) {
        if (it.key().contains(deviceName) && it.value()->isRunning()) {
            return true;
        }
    }
    for (auto it = wasapiHosts.constBegin(); it != wasapiHosts.constEnd(); ++it) {
        if (it.key().contains(deviceName) && it.value()->isRunning()) {
            return true;
        }
    }
    return false;
}
void AudioProcessor::updateAllDisplayBuffers() {
    for (auto it = asioHosts.constBegin(); it != asioHosts.constEnd(); ++it) {
        const DeviceStream& stream = it.value()->getStream();

        // 处理输出通道
       // qDebug() << "stream.outputChannels" << stream.outputChannels.size();
        for (const auto& channel : stream.outputChannels) {
            if (//channel->active &&
                channel->outputQueue &&
                displayQueues_[isOutput].contains(channel->deviceName)) {
                size_t availableSamples = channel->outputQueue->size_approx(); // 获取队列中所有可用样本数
                //qDebug() << "availableSamples" << availableSamples;
                if (availableSamples > 0) {
                    std::vector<float> samples(availableSamples);
                    size_t samplesRead = channel->outputQueue->try_dequeue_bulk(samples.data(), availableSamples);
                    if (samplesRead > 0) {
                        displayQueues_[isOutput][channel->deviceName]->enqueue_bulk(samples.data(), samplesRead);
                       // qDebug() << "Output channel" << channel->deviceName << "transferred" << samplesRead << "samples";
                        emit dataUpdated(isOutput, channel->deviceName);
                    }
                    else {
                      // qDebug() << "samplesRead不大于0";
                    }
                }
                else {
                   //qDebug() << "availableSamples不大于0";
                }
            }
            else{
               // channel->active
//                qDebug() << "channel->active" << channel->active;
//                qDebug() << "channel->active" << channel->outputQueue;
//                qDebug() << "channel->active" << displayQueues_[isOutput].contains(channel->deviceName);

            }
        }
        // 处理输入通道
        for (const auto& channel : stream.inputChannels) {
            if (channel->active && channel->inputQueue && displayQueues_[isInput].contains(channel->deviceName)) {
                size_t availableSamples = channel->inputQueue->size_approx(); // 获取队列中所有可用样本数
                if (availableSamples > 0) {
                    std::vector<float> samples(availableSamples);
                    size_t samplesRead = channel->inputQueue->try_dequeue_bulk(samples.data(), availableSamples);
                    if (samplesRead > 0) {
                        displayQueues_[isInput][channel->deviceName]->enqueue_bulk(samples.data(), samplesRead);
                        //qDebug() << "通道" << channel->deviceName << "样本" << displayQueues_[isInput][channel->deviceName]->size_approx() ;
                       // qDebug() << "Input channel" << channel->deviceName << "transferred" << samplesRead << "samples";
                        emit dataUpdated(isInput, channel->deviceName);
                    }
                }
            }
        }
    }

    for (auto it = wasapiHosts.constBegin(); it != wasapiHosts.constEnd(); ++it) {
        const DeviceStream& stream = it.value()->getStream();
        // 处理输入通道
        for (const auto& channel : stream.inputChannels) {
            if (channel->active && channel->inputQueue && displayQueues_[isInput].contains(channel->deviceName)) {
                size_t availableSamples = channel->inputQueue->size_approx(); // 获取队列中所有可用样本数
                if (availableSamples > 0) {
                    //qDebug() << "通道" << channel->deviceName << "样本" << availableSamples;
                    std::vector<float> samples(availableSamples);
                    size_t samplesRead = channel->inputQueue->try_dequeue_bulk(samples.data(), availableSamples);
                    if (samplesRead > 0) {
                        displayQueues_[isInput][channel->deviceName]->enqueue_bulk(samples.data(), samplesRead);
                        //qDebug() << "Input channel" << channel->deviceName << "transferred" << samplesRead << "samples";
                        //qDebug() << "通道" << channel->deviceName << "样本" << displayQueues_[isInput][channel->deviceName]->size_approx() ;
                        emit dataUpdated(isInput, channel->deviceName);
                    }
                }
            }
        }
        // 处理输出通道
        for (const auto& channel : stream.outputChannels) {
            if (channel->active && channel->outputQueue && displayQueues_[isOutput].contains(channel->deviceName)) {
                size_t availableSamples = channel->outputQueue->size_approx(); // 获取队列中所有可用样本数
                if (availableSamples > 0) {
                    std::vector<float> samples(availableSamples);
                    size_t samplesRead = channel->outputQueue->try_dequeue_bulk(samples.data(), availableSamples);
                    if (samplesRead > 0) {
                        displayQueues_[isOutput][channel->deviceName]->enqueue_bulk(samples.data(), samplesRead);
                        //qDebug() << "Output channel" << channel->deviceName << "transferred" << samplesRead << "samples";
                        emit dataUpdated(isOutput, channel->deviceName);
                    }
                }
            }
        }
    }
}
//void AudioProcessor::updateAllDisplayBuffers() { // 更新所有通道的显示队列
//    for (auto it = asioHosts.constBegin(); it != asioHosts.constEnd(); ++it) { // 遍历所有 ASIO 主机
//        const DeviceStream& stream = it.value()->getStream(); // 获取主机对应的设备流对象
//        for (const auto& channel : stream.inputChannels) { // 遍历输入通道
//            //displayQueues_
//            if (channel->active && channel->inputQueue && displayQueues_[isInput].contains(channel->deviceName)) { // 检查通道是否活跃、是否有输入队列且显示队列存在
//              // qDebug() << "channel->displayPoints_" << channel->displayPoints_;
//                std::vector<float> samples(channel->displayPoints_); // 创建样本缓冲区，大小为显示点数（默认 8820）
//                size_t samplesRead = channel->inputQueue->try_dequeue_bulk(samples.data(), channel->displayPoints_); // 从输入队列出队样本
//                if (samplesRead > 0) { // 检查是否成功出队样本
//                    displayQueues_[isInput][channel->deviceName]->enqueue_bulk(samples.data(), samplesRead); // 将样本入队到输入显示队列
//                    qDebug() << "通道" << channel->deviceName << "样本" << displayQueues_[isInput][channel->deviceName]->size_approx() ;
//                    emit dataUpdated(isInput, channel->deviceName); // 发出数据更新信号，通知界面刷新输入通道波形
//                }
//            }
//        }
//        for (const auto& channel : stream.outputChannels) { // 遍历输出通道
//            if (channel->active && channel->outputQueue && displayQueues_[isOutput].contains(channel->deviceName)) { // 检查通道是否活跃、是否有输出队列且显示队列存在
//               //qDebug() << "channel->displayPoints_" << channel->displayPoints_;
//                std::vector<float> samples(channel->displayPoints_); // 创建样本缓冲区，大小为显示点数
//                size_t samplesRead = channel->outputQueue->try_dequeue_bulk(samples.data(), channel->displayPoints_); // 从输出队列出队样本
//                if (samplesRead > 0) { // 检查是否成功出队样本
//                    displayQueues_[isOutput][channel->deviceName]->enqueue_bulk(samples.data(), samplesRead); // 将样本入队到输出显示队列
//                    emit dataUpdated(isOutput, channel->deviceName); // 发出数据更新信号，通知界面刷新输出通道波形
//                }
//            }
//        }
//    }

//    for (auto it = wasapiHosts.constBegin(); it != wasapiHosts.constEnd(); ++it) { // 遍历所有 WASAPI 主机
//        const DeviceStream& stream = it.value()->getStream(); // 获取主机对应的设备流对象
//        for (const auto& channel : stream.inputChannels) { // 遍历输入通道
//            if (channel->active && channel->inputQueue && displayQueues_[isInput].contains(channel->deviceName)) { // 检查通道是否活跃、是否有输入队列且显示队列存在
//               //qDebug() << "channel->displayPoints_" << channel->displayPoints_;
//                std::vector<float> samples(channel->displayPoints_); // 创建样本缓冲区，大小为显示点数
//                size_t samplesRead = channel->inputQueue->try_dequeue_bulk(samples.data(), channel->displayPoints_); // 从输入队列出队样本
//                if (samplesRead > 0) { // 检查是否成功出队样本
//                    displayQueues_[isInput][channel->deviceName]->enqueue_bulk(samples.data(), samplesRead); // 将样本入队到输入显示队列
//                    emit dataUpdated(isInput, channel->deviceName); // 发出数据更新信号，通知界面刷新输入通道波形
//                }
//            }
//        }
//        for (const auto& channel : stream.outputChannels) { // 遍历输出通道
//            if (channel->active && channel->outputQueue && displayQueues_[isOutput].contains(channel->deviceName)) { // 检查通道是否活跃、是否有输出队列且显示队列存在
//               //qDebug() << "channel->displayPoints_" << channel->displayPoints_;
//                std::vector<float> samples(channel->displayPoints_); // 创建样本缓冲区，大小为显示点数
//                size_t samplesRead = channel->outputQueue->try_dequeue_bulk(samples.data(), channel->displayPoints_); // 从输出队列出队样本
//                if (samplesRead > 0) { // 检查是否成功出队样本
//                    displayQueues_[isOutput][channel->deviceName]->enqueue_bulk(samples.data(), samplesRead); // 将样本入队到输出显示队列
//                    emit dataUpdated(isOutput, channel->deviceName); // 发出数据更新信号，通知界面刷新输出通道波形
//                }
//            }
//        }
//    }
//}
/*
void AudioProcessor::updateAllDisplayBuffers() {
    for (auto it = asioHosts.constBegin(); it != asioHosts.constEnd(); ++it) {
        const DeviceStream& stream = it.value()->getStream();
        for (const auto& channel : stream.inputChannels) {
            if (channel->active && channel->inputQueue && displayQueues_[isInput].contains(channel->deviceName)) { // 新增：额外检查显示队列
                std::vector<float> samples( channel->sampleRate);
                size_t samplesRead = channel->inputQueue->try_dequeue_bulk(samples.data(), channel->sampleRate);

                if (samplesRead > 0) { // 新增：仅当有样本时入队
                    displayQueues_[isInput][channel->deviceName]->enqueue_bulk(samples.data(), samplesRead); // 新增：入队到显示队列
                    emit dataUpdated(isInput, channel->deviceName);
                }
                else{
                     qDebug() << "ASIO没有有样本时入队";
                }
            }
            else{
                 qDebug() << "ASIO没有通道";
            }
        }
        for (const auto& channel : stream.outputChannels) {
            if (channel->active && channel->outputQueue && displayQueues_[isOutput].contains(channel->deviceName)) { // 新增：额外检查显示队列
                std::vector<float> samples(channel->sampleRate);
                size_t samplesRead = channel->outputQueue->try_dequeue_bulk(samples.data(), channel->sampleRate);

                if (samplesRead > 0) { // 新增：仅当有样本时入队
                    displayQueues_[isOutput][channel->deviceName]->enqueue_bulk(samples.data(), samplesRead); // 新增：入队到显示队列
                    emit dataUpdated(isOutput, channel->deviceName);
                }
                else{
                     qDebug() << "ASIO没有输出样本时入队";
                }
            }
            else{
                 qDebug() << "ASIO没有输出通道";
            }
        }
    }

    for (auto it = wasapiHosts.constBegin(); it != wasapiHosts.constEnd(); ++it) {
        const DeviceStream& stream = it.value()->getStream();
        for (const auto& channel : stream.inputChannels) {
            // if (channel->active && channel->inputQueue) { // 删除：检查活跃和输入队列
            if (channel->active && channel->inputQueue && displayQueues_[isInput].contains(channel->deviceName)) { // 新增：额外检查显示队列
                std::vector<float> samples(channel->sampleRate);
                size_t samplesRead = channel->inputQueue->try_dequeue_bulk(samples.data(), channel->sampleRate);
                // auto& buffer = useBuffer1_ ? displayBuffer1_[isInput][channel->deviceName] // 删除：选择双缓冲区
                //                            : displayBuffer2_[isInput][channel->deviceName];
                // for (size_t i = 0; i < samplesRead; ++i) { // 删除：写入双缓冲区
                //     buffer[i] = samples[i];
                // }
                // for (size_t i = samplesRead; i < displayPoints_; ++i) { // 删除：补 0
                //     buffer[i] = 0.0f;
                // }
                if (samplesRead > 0) { // 新增：仅当有样本时入队
                    displayQueues_[isInput][channel->deviceName]->enqueue_bulk(samples.data(), samplesRead); // 新增：入队到显示队列
                    emit dataUpdated(isInput, channel->deviceName);
                }
            }
        }
        for (const auto& channel : stream.outputChannels) {
            // if (channel->active && channel->outputQueue) { // 删除：检查活跃和输出队列
            if (channel->active && channel->outputQueue && displayQueues_[isOutput].contains(channel->deviceName)) { // 新增：额外检查显示队列
                std::vector<float> samples(channel->sampleRate);
                size_t samplesRead = channel->outputQueue->try_dequeue_bulk(samples.data(), channel->sampleRate);
                // auto& buffer = useBuffer1_ ? displayBuffer1_[isOutput][channel->deviceName] // 删除：选择双缓冲区
                //                            : displayBuffer2_[isOutput][channel->deviceName];
                // for (size_t i = 0; i < samplesRead; ++i) { // 删除：写入双缓冲区
                //     buffer[i] = samples[i];
                // }
                // for (size_t i = samplesRead; i < displayPoints_; ++i) { // 删除：补 0
                //     buffer[i] = 0.0f;
                // }
                if (samplesRead > 0) { // 新增：仅当有样本时入队
                    displayQueues_[isOutput][channel->deviceName]->enqueue_bulk(samples.data(), samplesRead); // 新增：入队到显示队列
                    emit dataUpdated(isOutput, channel->deviceName);
                }
            }
        }
    }

    // useBuffer1_ = !useBuffer1_; // 删除：双缓冲切换
}



void AudioProcessor::updateAllDisplayBuffers() {
    for (auto it = asioHosts.constBegin(); it != asioHosts.constEnd(); ++it) {
        const DeviceStream& stream = it.value()->getStream();
        for (const auto& channel : stream.inputChannels) {
            if (channel->active && channel->inputQueue && displayQueues_[isInput].contains(channel->deviceName)) { // 新增：额外检查显示队列
                std::vector<float> samples( displayPoints_);
                size_t samplesRead = channel->inputQueue->try_dequeue_bulk(samples.data(), displayPoints_);

                if (samplesRead > 0) { // 新增：仅当有样本时入队
                    displayQueues_[isInput][channel->deviceName]->enqueue_bulk(samples.data(), samplesRead); // 新增：入队到显示队列
                    emit dataUpdated(isInput, channel->deviceName);
                }
                else{
                     qDebug() << "ASIO没有有样本时入队";
                }
            }
            else{
                 qDebug() << "ASIO没有通道";
            }
        }
        for (const auto& channel : stream.outputChannels) {
            if (channel->active && channel->outputQueue && displayQueues_[isOutput].contains(channel->deviceName)) { // 新增：额外检查显示队列
                std::vector<float> samples(displayPoints_);
                size_t samplesRead = channel->outputQueue->try_dequeue_bulk(samples.data(), displayPoints_);

                if (samplesRead > 0) { // 新增：仅当有样本时入队
                    displayQueues_[isOutput][channel->deviceName]->enqueue_bulk(samples.data(), samplesRead); // 新增：入队到显示队列
                    emit dataUpdated(isOutput, channel->deviceName);
                }
                else{
                     qDebug() << "ASIO没有输出样本时入队";
                }
            }
            else{
                 qDebug() << "ASIO没有输出通道";
            }
        }
    }

    for (auto it = wasapiHosts.constBegin(); it != wasapiHosts.constEnd(); ++it) {
        const DeviceStream& stream = it.value()->getStream();
        for (const auto& channel : stream.inputChannels) {
            // if (channel->active && channel->inputQueue) { // 删除：检查活跃和输入队列
            if (channel->active && channel->inputQueue && displayQueues_[isInput].contains(channel->deviceName)) { // 新增：额外检查显示队列
                std::vector<float> samples(displayPoints_);
                size_t samplesRead = channel->inputQueue->try_dequeue_bulk(samples.data(), displayPoints_);
                // auto& buffer = useBuffer1_ ? displayBuffer1_[isInput][channel->deviceName] // 删除：选择双缓冲区
                //                            : displayBuffer2_[isInput][channel->deviceName];
                // for (size_t i = 0; i < samplesRead; ++i) { // 删除：写入双缓冲区
                //     buffer[i] = samples[i];
                // }
                // for (size_t i = samplesRead; i < displayPoints_; ++i) { // 删除：补 0
                //     buffer[i] = 0.0f;
                // }
                if (samplesRead > 0) { // 新增：仅当有样本时入队
                    displayQueues_[isInput][channel->deviceName]->enqueue_bulk(samples.data(), samplesRead); // 新增：入队到显示队列
                    emit dataUpdated(isInput, channel->deviceName);
                }
            }
        }
        for (const auto& channel : stream.outputChannels) {
            // if (channel->active && channel->outputQueue) { // 删除：检查活跃和输出队列
            if (channel->active && channel->outputQueue && displayQueues_[isOutput].contains(channel->deviceName)) { // 新增：额外检查显示队列
                std::vector<float> samples(displayPoints_);
                size_t samplesRead = channel->outputQueue->try_dequeue_bulk(samples.data(), displayPoints_);
                // auto& buffer = useBuffer1_ ? displayBuffer1_[isOutput][channel->deviceName] // 删除：选择双缓冲区
                //                            : displayBuffer2_[isOutput][channel->deviceName];
                // for (size_t i = 0; i < samplesRead; ++i) { // 删除：写入双缓冲区
                //     buffer[i] = samples[i];
                // }
                // for (size_t i = samplesRead; i < displayPoints_; ++i) { // 删除：补 0
                //     buffer[i] = 0.0f;
                // }
                if (samplesRead > 0) { // 新增：仅当有样本时入队
                    displayQueues_[isOutput][channel->deviceName]->enqueue_bulk(samples.data(), samplesRead); // 新增：入队到显示队列
                    emit dataUpdated(isOutput, channel->deviceName);
                }
            }
        }
    }

    // useBuffer1_ = !useBuffer1_; // 删除：双缓冲切换
}
*/
QString AudioProcessor::apiToString(RtAudio::Api api) {
    switch (api) {
    case RtAudio::WINDOWS_ASIO: return "ASIO";
    case RtAudio::WINDOWS_WASAPI: return "WASAPI";
    default: return "Unknown";
    }
}

AudioProcessor::AsioHost::AsioHost(const QString& key, AudioProcessor* parent)
    : key(key), parent(parent),
      rtAudio(new RtAudio(RtAudio::WINDOWS_ASIO,
                          std::bind(&AsioHost::errorCallback, this, std::placeholders::_1, std::placeholders::_2))) {}

void AudioProcessor::AsioHost::errorCallback(RtAudioErrorType type, const std::string& errorText) {
    QString errorTypeStr;
    switch (type) {
        case RtAudioErrorType::RTAUDIO_NO_ERROR: errorTypeStr = "RTAUDIO_NO_ERROR"; break;
        case RtAudioErrorType::RTAUDIO_WARNING: errorTypeStr = "RTAUDIO_WARNING"; break;
        case RtAudioErrorType::RTAUDIO_UNKNOWN_ERROR: errorTypeStr = "RTAUDIO_UNKNOWN_ERROR"; break;
        case RtAudioErrorType::RTAUDIO_NO_DEVICES_FOUND: errorTypeStr = "RTAUDIO_NO_DEVICES_FOUND"; break;
        case RtAudioErrorType::RTAUDIO_INVALID_DEVICE: errorTypeStr = "RTAUDIO_INVALID_DEVICE"; break;
        case RtAudioErrorType::RTAUDIO_DEVICE_DISCONNECT: errorTypeStr = "RTAUDIO_DEVICE_DISCONNECT"; break;
        case RtAudioErrorType::RTAUDIO_MEMORY_ERROR: errorTypeStr = "RTAUDIO_MEMORY_ERROR"; break;
        case RtAudioErrorType::RTAUDIO_INVALID_PARAMETER: errorTypeStr = "RTAUDIO_INVALID_PARAMETER"; break;
        case RtAudioErrorType::RTAUDIO_INVALID_USE: errorTypeStr = "RTAUDIO_INVALID_USE"; break;
        case RtAudioErrorType::RTAUDIO_DRIVER_ERROR: errorTypeStr = "RTAUDIO_DRIVER_ERROR"; break;
        case RtAudioErrorType::RTAUDIO_SYSTEM_ERROR: errorTypeStr = "RTAUDIO_SYSTEM_ERROR"; break;
        case RtAudioErrorType::RTAUDIO_THREAD_ERROR: errorTypeStr = "RTAUDIO_THREAD_ERROR"; break;
        default: errorTypeStr = "UNKNOWN"; break;
    }

    QString msg = QString("%1 ASIO error: %2 (%3)").arg(key).arg(errorTypeStr).arg(QString::fromStdString(errorText));
    qDebug() << msg;

    if (type != RtAudioErrorType::RTAUDIO_WARNING && stream.isRunning && rtAudio->isStreamOpen()) {
        rtAudio->stopStream();
        if (rtAudio->isStreamOpen()) {
            rtAudio->closeStream();
        }
        stream.isRunning = false;
        qDebug() << "Closed stream for" << key << "due to error";
    }

    QMetaObject::invokeMethod(parent, "errorOccurred", Qt::QueuedConnection, Q_ARG(QString, msg));
}

// 获取指定通道的音频流延迟
// 参数：channelName - 要查询的通道名称
// 返回：延迟样本数（unsigned int），如果失败返回0
unsigned int AudioProcessor::getStreamLatencyForChannel(const QString& channelName){
    // 加锁，确保线程安全访问共享资源（registeredChannels）
    QMutexLocker locker(&mutex_);

    // 检查通道是否已注册，如果未找到，记录警告并返回0
    if (!registeredChannels.contains(channelName)) {
        qWarning() << "Channel not found:" << channelName;
        return 0;
    }

    // 获取通道信息
    const auto& info = registeredChannels[channelName];

    // 确定驱动类型（ASIO 或 WASAPI）
    QString driverType = (info->api == RtAudio::WINDOWS_ASIO) ? "ASIO" : "WASAPI";
    RtAudio::Api api = info->api;

    // 创建临时 RtAudio 实例，不影响主 dac，用于查询延迟
    QScopedPointer<RtAudio> tempAudio(new RtAudio(api));

    // 初始化流参数结构体
    RtAudio::StreamParameters inParams, outParams;

    // 设置默认缓冲帧数和采样率
    unsigned int bufferFrames = 512;
    unsigned int sampleRate = info->sampleRate;

    // 获取设备信息
    RtAudio::DeviceInfo deviceInfo = tempAudio->getDeviceInfo(info->deviceId);

    // 配置输入参数（如果设备有输入通道）
    if (deviceInfo.inputChannels > 0) {
        inParams.deviceId = info->deviceId;
        inParams.nChannels = deviceInfo.inputChannels;
        inParams.firstChannel = 0;
    }

    // 配置输出参数（如果设备有输出通道）
    if(deviceInfo.outputChannels > 0){
        outParams.deviceId = info->deviceId;
        outParams.nChannels = deviceInfo.outputChannels;
        outParams.firstChannel = 0;
    }

    // 函数指针设置为空，不启动流，只 openStream 获取 latency
    if(tempAudio->openStream(
        // 输出参数（如果有输出通道，否则nullptr）
        outParams.nChannels ? &outParams : nullptr,
        // 输入参数（如果有输入通道，否则nullptr）
        inParams.nChannels ? &inParams : nullptr,
        // 格式：RTAUDIO_FLOAT32
        FORMAT,
        // 采样率
        sampleRate,
        // 缓冲帧数（传入引用，可能被修改）
        &bufferFrames,
        // 回调函数：nullptr，不启动实际流
        nullptr,
        // 用户数据：nullptr
        nullptr) == RTAUDIO_NO_ERROR){

        // 获取流延迟
        unsigned int latency = tempAudio->getStreamLatency();

        // 关闭流
        tempAudio->closeStream();

        // 返回延迟值
        return latency;
    }else{
        // 如果打开流失败，关闭流并记录警告
        tempAudio->closeStream();
        qWarning() << "getStreamLatency error";
        return 0;
    }

    // 默认返回0（如果未进入if-else）
    return 0;
}



bool AudioProcessor::AsioHost::addChannel(const QString& channelName, ChannelType type, const AudioChannalInfoConfig& config, // 向 ASIO 主机添加通道
                                         unsigned int sampleRate, QString& errorMessage) { // 参数：通道名称、通道类型、配置、采样率、错误信息
    for (const auto& ch : type == isInput ? stream.inputChannels : stream.outputChannels) { // 遍历对应类型的通道列表（输入或输出）
        if (ch->deviceName == channelName) { // 检查通道是否已添加
            errorMessage = QString("%1 channel %2 already added").arg(type == isInput ? "Input" : "Output").arg(channelName); // 设置错误信息：通道已存在
            return false; // 返回 false，表示添加失败
        }
    }

    auto info = parent->registeredChannels[channelName]; // 从 AudioProcessor 获取已注册的通道信息
    //qDebug() << "AudioProcessor::AsioHost::addChannel sampleRate" << parent->registeredChannels[channelName]->sampleRate;
    if (type == isInput) { // 如果是输入通道
        stream.inputChannels.append(info); // 将通道添加到输入通道列表
    } else { // 如果是输出通道
        stream.outputChannels.append(info); // 将通道添加到输出通道列表
    }
    stream.activeChannelCount++; // 增加活跃通道计数
    qDebug() << "Added" << (type == isInput ? "input" : "output") << "channel:" << channelName // 打印调试信息：通道添加成功
             << "to device:" << key << "DriverID:" << info->deviceId;
    return true; // 返回 true，表示通道添加成功
}

bool AudioProcessor::AsioHost::startStream(unsigned int sampleRate, QString& errorMessage) { // 启动 ASIO 音频流
    if (stream.isRunning) { // 检查流是否已在运行
        errorMessage = QString("Device %1 is already running").arg(key); // 设置错误信息：设备已在运行
        return false; // 返回 false，表示启动失败
    }

    RtAudio::DeviceInfo deviceInfo = rtAudio->getDeviceInfo( // 获取设备信息
        stream.inputChannels.isEmpty() ? stream.outputChannels.first()->deviceId : stream.inputChannels.first()->deviceId); // 使用输入或输出通道的设备 ID
    RtAudio::StreamParameters iParams, oParams; // 创建输入和输出流参数对象
    if (deviceInfo.inputChannels > 0) { // 如果设备支持输入通道
        iParams.deviceId = deviceInfo.ID; // 设置输入设备 ID
        iParams.nChannels = deviceInfo.inputChannels; // 设置输入通道数
        iParams.firstChannel = 0; // 设置起始通道为 0
    }
    if (deviceInfo.outputChannels > 0) { // 如果设备支持输出通道
        oParams.deviceId = deviceInfo.ID; // 设置输出设备 ID
        oParams.nChannels = deviceInfo.outputChannels; // 设置输出通道数
        oParams.firstChannel = 0; // 设置起始通道为 0
    }
    if (!iParams.nChannels && !oParams.nChannels) { // 检查是否至少有一个有效通道
        errorMessage = QString("No valid input or output channels for %1").arg(key); // 设置错误信息：无有效通道
        return false; // 返回 false，表示启动失败
    }

    unsigned int bufferFrames = 512; // 设置缓冲区帧数，默认 512
    RtAudio::StreamOptions options; // 创建流选项对象
    options.flags =  RTAUDIO_SCHEDULE_REALTIME  ; // 设置流选项：最小化延迟和实时调度
    options.priority = 80;
//    qDebug() << "iParams.deviceId" << iParams.deviceId;
//     qDebug() << " iParams.nChannels" << iParams.nChannels;
//      qDebug() << "iParams.firstChannel" << iParams.firstChannel;
//      qDebug() << "oParams.deviceId" << oParams.deviceId;
//       qDebug() << "oParams.nChannels" << oParams.nChannels;
//        qDebug() << "oParams.firstChannel" << oParams.firstChannel;

     qDebug() << "AudioProcessor::AsioHost::startStream sampleRate" << sampleRate;

    if (rtAudio->openStream(oParams.nChannels ? &oParams : nullptr, iParams.nChannels ? &iParams : nullptr, // 打开音频流
                            FORMAT, sampleRate, &bufferFrames, &callback, this, &options) != RTAUDIO_NO_ERROR) { // 参数：输出/输入参数、格式、采样率、缓冲区帧数、回调函数、用户数据、选项
        errorMessage = QString("Failed to open stream for %1: %2").arg(key).arg(rtAudio->getErrorText().c_str()); // 设置错误信息：打开流失败
        return false; // 返回 false，表示启动失败
    }
    //qDebug() << "getStreamLatency" << );
    if (rtAudio->startStream() != RTAUDIO_NO_ERROR) { // 启动音频流
        errorMessage = QString("Failed to start stream for %1: %2").arg(key).arg(rtAudio->getErrorText().c_str()); // 设置错误信息：启动流失败
        rtAudio->closeStream(); // 关闭已打开的流
        return false; // 返回 false，表示启动失败
    }
    stream.Devi = deviceInfo;
    stream.dac = rtAudio.data(); // 保存 RtAudio 实例到流对象
    stream.isRunning = true; // 标记流为运行状态
    stream.sampleRate = sampleRate; // 设置流采样率
    stream.streamLatency = rtAudio->getStreamLatency(); // 设置流延迟（缓冲区帧数）
    qDebug() << "Started stream for" << key << "with latency" << stream.streamLatency << "samples"; // 打印调试信息：流启动成功
    return true; // 返回 true，表示流启动成功
}
void AudioProcessor::AsioHost::stopStream() {
    if (stream.isRunning && stream.dac && stream.dac->isStreamRunning()) {
        stream.dac->stopStream();
        if (stream.dac->isStreamOpen()) {
            stream.dac->closeStream();
        }
        stream.isRunning = false;
        stream.dac = nullptr;
        qDebug() << "Stopped stream for" << key;
    }
}

// 修改 AsioHost::callback 函数，优化播放完成逻辑

int AudioProcessor::AsioHost::callback(void* outputBuffer, void* inputBuffer, unsigned int nFrames,
                                      double streamTime, RtAudioStreamStatus status, void* userData) {
    AsioHost* host = static_cast<AsioHost*>(userData);
    DeviceStream& device = host->stream;

    float* in = static_cast<float*>(inputBuffer);
    if (in && !device.inputChannels.empty()) {
        unsigned int nInputChannels = device.Devi.inputChannels;
        for (const auto& channel : device.inputChannels) {
            //testiiiii++;
            if (channel->active && channel->inputQueue && channel->channelIndex < nInputChannels) {
                std::vector<float> temp(nFrames);
                for (unsigned int i = 0; i < nFrames; ++i) {
                    temp[i] = in[i * nInputChannels + channel->channelIndex];
                }
                channel->inputQueue->enqueue_bulk(temp.data(), nFrames);
            }
        }
    }

    float* out = static_cast<float*>(outputBuffer);
    if (out && !device.outputChannels.empty()) {
        unsigned int nOutputChannels = device.Devi.outputChannels;
        std::memset(out, 0, nFrames * nOutputChannels * sizeof(float));
        for (auto it = device.outputChannels.begin(); it != device.outputChannels.end(); ) {
            auto channel = *it;
            if (!channel->active || !channel->outputQueue || channel->channelIndex >= nOutputChannels) {
                ++it;
                continue;
            }

            std::vector<float> temp(nFrames, 0.0f); // 初始化为 0
            size_t toRead = 0;

            if (channel->signalType == SignalType::Sine && !channel->sineWaveData.empty()) {
                toRead = nFrames;
                for (unsigned int i = 0; i < nFrames; ++i) {
                    unsigned int index = (channel->sampleOffset + i) % channel->sineWaveData.size();
                    temp[i] = channel->sineWaveData[index];
                }
                channel->sampleOffset += nFrames;
            } else if (channel->validSamples > 0) {
                toRead = std::min(nFrames, channel->validSamples);
                for (size_t i = 0; i < toRead; ++i) {
                    if (channel->tempDataIndex < channel->totalSamples) {
                        temp[i] = channel->tempData[channel->tempDataIndex++];
                    } else {
                        temp[i] = 0.0f;
                    }
                }
                channel->validSamples -= toRead;
            }

            // 将有效样本写入输出缓冲区
            for (size_t i = 0; i < toRead; ++i) {
                out[i * nOutputChannels + channel->channelIndex] = temp[i];
            }

            // 始终入队完整的 nFrames 样本（包括补 0）
            channel->outputQueue->enqueue_bulk(temp.data(), nFrames);




            // 检查是否播放完成
            if (channel->validSamples == 0 && channel->signalType != SignalType::Sine) {
                channel->active = false;
                channel->pendingRemoval = true;
                device.activeChannelCount--;
                 //testOOOOO = testiiiii;
                emit host->parent->playbackFinished(ChannelType::isOutput, channel->deviceName);
                it = device.outputChannels.erase(it);
                continue;
            }



            ++it;
        }
    }

    device.lastStreamTime = streamTime;
    return 0;
}

AudioProcessor::WasapiHost::WasapiHost(const QString& key, AudioProcessor* parent)
    : key(key), parent(parent),
      rtAudio(new RtAudio(RtAudio::WINDOWS_WASAPI,
                          std::bind(&WasapiHost::errorCallback, this, std::placeholders::_1, std::placeholders::_2))) {}

void AudioProcessor::WasapiHost::errorCallback(RtAudioErrorType type, const std::string& errorText) {
    QString errorTypeStr;
    switch (type) {
        case RtAudioErrorType::RTAUDIO_NO_ERROR: errorTypeStr = "RTAUDIO_NO_ERROR"; break;
        case RtAudioErrorType::RTAUDIO_WARNING: errorTypeStr = "RTAUDIO_WARNING"; break;
        case RtAudioErrorType::RTAUDIO_UNKNOWN_ERROR: errorTypeStr = "RTAUDIO_UNKNOWN_ERROR"; break;
        case RtAudioErrorType::RTAUDIO_NO_DEVICES_FOUND: errorTypeStr = "RTAUDIO_NO_DEVICES_FOUND"; break;
        case RtAudioErrorType::RTAUDIO_INVALID_DEVICE: errorTypeStr = "RTAUDIO_INVALID_DEVICE"; break;
        case RtAudioErrorType::RTAUDIO_DEVICE_DISCONNECT: errorTypeStr = "RTAUDIO_DEVICE_DISCONNECT"; break;
        case RtAudioErrorType::RTAUDIO_MEMORY_ERROR: errorTypeStr = "RTAUDIO_MEMORY_ERROR"; break;
        case RtAudioErrorType::RTAUDIO_INVALID_PARAMETER: errorTypeStr = "RTAUDIO_INVALID_PARAMETER"; break;
        case RtAudioErrorType::RTAUDIO_INVALID_USE: errorTypeStr = "RTAUDIO_INVALID_USE"; break;
        case RtAudioErrorType::RTAUDIO_DRIVER_ERROR: errorTypeStr = "RTAUDIO_DRIVER_ERROR"; break;
        case RtAudioErrorType::RTAUDIO_SYSTEM_ERROR: errorTypeStr = "RTAUDIO_SYSTEM_ERROR"; break;
        case RtAudioErrorType::RTAUDIO_THREAD_ERROR: errorTypeStr = "RTAUDIO_THREAD_ERROR"; break;
        default: errorTypeStr = "UNKNOWN"; break;
    }

    QString msg = QString("%1 WASAPI error: %2 (%3)").arg(key).arg(errorTypeStr).arg(QString::fromStdString(errorText));
    qDebug() << msg;

    if (type != RtAudioErrorType::RTAUDIO_WARNING && stream.isRunning && rtAudio->isStreamOpen()) {
        rtAudio->stopStream();
        if (rtAudio->isStreamOpen()) {
            rtAudio->closeStream();
        }
        stream.isRunning = false;
        qDebug() << "Closed stream for" << key << "due to error";
    }

    QMetaObject::invokeMethod(parent, "errorOccurred", Qt::QueuedConnection, Q_ARG(QString, msg));
}

bool AudioProcessor::WasapiHost::addChannel(const QString& channelName, ChannelType type, const AudioChannalInfoConfig& config,
                                           unsigned int sampleRate, QString& errorMessage) {
    for (const auto& ch : type == isInput ? stream.inputChannels : stream.outputChannels) {
        if (ch->deviceName == channelName) {
            errorMessage = QString("%1 channel %2 already added").arg(type == isInput ? "Input" : "Output").arg(channelName);
            return false;
        }
    }

    auto info = parent->registeredChannels[channelName];
   // qDebug() << "AudioProcessor::WasapiHost::addChannel sampleRate" << parent->registeredChannels[channelName]->sampleRate;

    if (type == isInput) {
        stream.inputChannels.append(info);
    } else {
        stream.outputChannels.append(info);
    }
    stream.activeChannelCount++;
    qDebug() << "Added" << (type == isInput ? "input" : "output") << "channel:" << channelName
             << "to device:" << key << "DriverID:" << info->deviceId;
    return true;
}

bool AudioProcessor::WasapiHost::startStream(unsigned int sampleRate, QString& errorMessage) {
    if (stream.isRunning) {
        errorMessage = QString("Device %1 is already running").arg(key);
        return false;
    }

    RtAudio::DeviceInfo deviceInfo = rtAudio->getDeviceInfo(
        stream.inputChannels.isEmpty() ? stream.outputChannels.first()->deviceId : stream.inputChannels.first()->deviceId);
    RtAudio::StreamParameters iParams, oParams;
    if (deviceInfo.inputChannels > 0) {
        iParams.deviceId = deviceInfo.ID;
        iParams.nChannels = deviceInfo.inputChannels;
        iParams.firstChannel = 0;
    }
    if (deviceInfo.outputChannels > 0) {
        oParams.deviceId = deviceInfo.ID;
        oParams.nChannels = deviceInfo.outputChannels;
        oParams.firstChannel = 0;
    }
    if (!iParams.nChannels && !oParams.nChannels) {
        errorMessage = QString("No valid input or output channels for %1").arg(key);
        return false;
    }
//    qDebug() << "WasapiHostiParams.deviceId" << iParams.deviceId;
//    qDebug() << "WasapiHostiParams.deviceId" << iParams.deviceId;
//     qDebug() << " WasapiHostiParams.nChannels" << iParams.nChannels;
//      qDebug() << "WasapiHostiParams.firstChannel" << iParams.firstChannel;
//      qDebug() << "WasapiHostoParams.deviceId" << oParams.deviceId;
//       qDebug() << "WasapiHostoParams.nChannels" << oParams.nChannels;
//        qDebug() << "WasapiHostoParams.firstChannel" << oParams.firstChannel;
     qDebug() << "AudioProcessor::WasapiHost::startStream sampleRate" << sampleRate;
    unsigned int bufferFrames = 512;
    RtAudio::StreamOptions options;
    options.flags = RTAUDIO_MINIMIZE_LATENCY | RTAUDIO_SCHEDULE_REALTIME;
    //options.numberOfBuffers = 4;
    //options.streamName = key.toStdString();

    if (rtAudio->openStream(oParams.nChannels ? &oParams : nullptr, iParams.nChannels ? &iParams : nullptr,
                            FORMAT, sampleRate, &bufferFrames, &callback, this, &options) != RTAUDIO_NO_ERROR) {
        errorMessage = QString("Failed to open stream for %1: %2").arg(key).arg(rtAudio->getErrorText().c_str());
        return false;
    }

    if (rtAudio->startStream() != RTAUDIO_NO_ERROR) {
        errorMessage = QString("Failed to start stream for %1: %2").arg(key).arg(rtAudio->getErrorText().c_str());
        rtAudio->closeStream();
        return false;
    }
    stream.Devi = deviceInfo;
    stream.dac = rtAudio.data();
    stream.isRunning = true;
    stream.sampleRate = sampleRate;
    stream.streamLatency = rtAudio->getStreamLatency();
    qDebug() << "Started stream for" << key << "with latency" << stream.streamLatency << "samples";
    return true;
}

void AudioProcessor::WasapiHost::stopStream() {
    if (stream.isRunning && stream.dac && stream.dac->isStreamRunning()) {
        stream.dac->stopStream();
        if (stream.dac->isStreamOpen()) {
            stream.dac->closeStream();
        }
        stream.isRunning = false;
        stream.dac = nullptr;
        qDebug() << "Stopped stream for" << key;
    }
}

int AudioProcessor::WasapiHost::callback(void* outputBuffer, void* inputBuffer, unsigned int nFrames,
                                        double streamTime, RtAudioStreamStatus status, void* userData) {
    WasapiHost* host = static_cast<WasapiHost*>(userData);
    DeviceStream& device = host->stream;
testiiiii++;
    float* in = static_cast<float*>(inputBuffer);
    if (in && !device.inputChannels.empty()) {
        unsigned int nInputChannels = device.Devi.inputChannels;
        for (const auto& channel : device.inputChannels) {
            if (channel->active && channel->inputQueue && channel->channelIndex < nInputChannels) {
                std::vector<float> temp(nFrames);
                for (unsigned int i = 0; i < nFrames; ++i) {
                    temp[i] = in[i * nInputChannels + channel->channelIndex];
                }
                channel->inputQueue->enqueue_bulk(temp.data(), nFrames);
            }
        }
    }

    float* out = static_cast<float*>(outputBuffer);
    if (out && !device.outputChannels.empty()) {
        unsigned int nOutputChannels = device.Devi.outputChannels;
        std::memset(out, 0, nFrames * nOutputChannels * sizeof(float));
        for (auto it = device.outputChannels.begin(); it != device.outputChannels.end(); ) {
            auto channel = *it;
            if (!channel->active || !channel->outputQueue || channel->channelIndex >= nOutputChannels) {
                ++it;
                continue;
            }

            std::vector<float> temp(nFrames, 0.0f); // 初始化为 0
            size_t toRead = 0;

            if (channel->signalType == SignalType::Sine && !channel->sineWaveData.empty()) {
                toRead = nFrames;
                for (unsigned int i = 0; i < nFrames; ++i) {
                    unsigned int index = (channel->sampleOffset + i) % channel->sineWaveData.size();
                    temp[i] = channel->sineWaveData[index];
                }
                channel->sampleOffset += nFrames;
            } else if (channel->validSamples > 0) {
                toRead = std::min(nFrames, channel->validSamples);
                for (size_t i = 0; i < toRead; ++i) {
                    if (channel->tempDataIndex < channel->totalSamples) {
                        temp[i] = channel->tempData[channel->tempDataIndex++];
                    } else {
                        temp[i] = 0.0f;
                    }
                }
                channel->validSamples -= toRead;
                if (channel->validSamples == 0) {
                    channel->active = false;
                    device.activeChannelCount--;
                    it = device.outputChannels.erase(it);
                    emit host->parent->playbackFinished(ChannelType::isOutput, channel->deviceName);
                    continue;
                }
            }

            // 补 0 逻辑：剩余部分已初始化为 0，全部入队
            for (size_t i = 0; i < toRead; ++i) {
                out[i * nOutputChannels + channel->channelIndex] = temp[i];
            }
            channel->outputQueue->enqueue_bulk(temp.data(), nFrames); // 入队整个缓冲区，包括补 0
            ++it;
        }
    }

    device.lastStreamTime = streamTime;
    return 0;
}
