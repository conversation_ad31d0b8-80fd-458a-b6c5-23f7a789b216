\hypertarget{structIir_1_1ChebyshevI_1_1HighShelf}{}\doxysubsection{Iir\+::ChebyshevI\+::High\+Shelf$<$ Filter\+Order, State\+Type $>$ Struct Template Reference}
\label{structIir_1_1ChebyshevI_1_1HighShelf}\index{Iir::ChebyshevI::HighShelf$<$ FilterOrder, StateType $>$@{Iir::ChebyshevI::HighShelf$<$ FilterOrder, StateType $>$}}


{\ttfamily \#include $<$Chebyshev\+I.\+h$>$}

Inheritance diagram for Iir\+::ChebyshevI\+::High\+Shelf$<$ Filter\+Order, State\+Type $>$\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=2.400000cm]{structIir_1_1ChebyshevI_1_1HighShelf}
\end{center}
\end{figure}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
void \mbox{\hyperlink{structIir_1_1ChebyshevI_1_1HighShelf_a9fe4f6cee3b4b1aa4688ea21d3f5202f}{setup}} (double sample\+Rate, double cutoff\+Frequency, double gain\+Db, double ripple\+Db)
\item 
void \mbox{\hyperlink{structIir_1_1ChebyshevI_1_1HighShelf_a6e8901d8a4c2b804f4a4df780f1937cc}{setup}} (int req\+Order, double sample\+Rate, double cutoff\+Frequency, double gain\+Db, double ripple\+Db)
\item 
void \mbox{\hyperlink{structIir_1_1ChebyshevI_1_1HighShelf_aa078bdf604ac9feeedd282174bd0cea9}{setupN}} (double cutoff\+Frequency, double gain\+Db, double ripple\+Db)
\item 
void \mbox{\hyperlink{structIir_1_1ChebyshevI_1_1HighShelf_adbfa0e3487393075e3f178d50293f0bb}{setupN}} (int req\+Order, double cutoff\+Frequency, double gain\+Db, double ripple\+Db)
\end{DoxyCompactItemize}


\doxysubsubsection{Detailed Description}
\subsubsection*{template$<$int Filter\+Order = 4, class State\+Type = Direct\+Form\+II$>$\newline
struct Iir\+::\+Chebyshev\+I\+::\+High\+Shelf$<$ Filter\+Order, State\+Type $>$}

\mbox{\hyperlink{namespaceIir_1_1ChebyshevI}{ChebyshevI}} high shelf filter. Specified gain in the passband. Otherwise 0 dB. 
\begin{DoxyParams}{Parameters}
{\em Filter\+Order} & Reserves memory for a filter of the order Filter\+Order \\
\hline
{\em State\+Type} & The filter topology\+: \mbox{\hyperlink{classIir_1_1DirectFormI}{Direct\+FormI}}, \mbox{\hyperlink{classIir_1_1DirectFormII}{Direct\+Form\+II}}, ... \\
\hline
\end{DoxyParams}


\doxysubsubsection{Member Function Documentation}
\mbox{\Hypertarget{structIir_1_1ChebyshevI_1_1HighShelf_a9fe4f6cee3b4b1aa4688ea21d3f5202f}\label{structIir_1_1ChebyshevI_1_1HighShelf_a9fe4f6cee3b4b1aa4688ea21d3f5202f}} 
\index{Iir::ChebyshevI::HighShelf$<$ FilterOrder, StateType $>$@{Iir::ChebyshevI::HighShelf$<$ FilterOrder, StateType $>$}!setup@{setup}}
\index{setup@{setup}!Iir::ChebyshevI::HighShelf$<$ FilterOrder, StateType $>$@{Iir::ChebyshevI::HighShelf$<$ FilterOrder, StateType $>$}}
\doxyparagraph{\texorpdfstring{setup()}{setup()}\hspace{0.1cm}{\footnotesize\ttfamily [1/2]}}
{\footnotesize\ttfamily template$<$int Filter\+Order = 4, class State\+Type  = Direct\+Form\+II$>$ \\
void \mbox{\hyperlink{structIir_1_1ChebyshevI_1_1HighShelf}{Iir\+::\+Chebyshev\+I\+::\+High\+Shelf}}$<$ Filter\+Order, State\+Type $>$\+::setup (\begin{DoxyParamCaption}\item[{double}]{sample\+Rate,  }\item[{double}]{cutoff\+Frequency,  }\item[{double}]{gain\+Db,  }\item[{double}]{ripple\+Db }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients of the filter at the order Filter\+Order 
\begin{DoxyParams}{Parameters}
{\em sample\+Rate} & Sampling rate \\
\hline
{\em cutoff\+Frequency} & Cutoff frequency. \\
\hline
{\em gain\+Db} & Gain in the passband \\
\hline
{\em ripple\+Db} & Permitted ripples in dB in the passband \\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{structIir_1_1ChebyshevI_1_1HighShelf_a6e8901d8a4c2b804f4a4df780f1937cc}\label{structIir_1_1ChebyshevI_1_1HighShelf_a6e8901d8a4c2b804f4a4df780f1937cc}} 
\index{Iir::ChebyshevI::HighShelf$<$ FilterOrder, StateType $>$@{Iir::ChebyshevI::HighShelf$<$ FilterOrder, StateType $>$}!setup@{setup}}
\index{setup@{setup}!Iir::ChebyshevI::HighShelf$<$ FilterOrder, StateType $>$@{Iir::ChebyshevI::HighShelf$<$ FilterOrder, StateType $>$}}
\doxyparagraph{\texorpdfstring{setup()}{setup()}\hspace{0.1cm}{\footnotesize\ttfamily [2/2]}}
{\footnotesize\ttfamily template$<$int Filter\+Order = 4, class State\+Type  = Direct\+Form\+II$>$ \\
void \mbox{\hyperlink{structIir_1_1ChebyshevI_1_1HighShelf}{Iir\+::\+Chebyshev\+I\+::\+High\+Shelf}}$<$ Filter\+Order, State\+Type $>$\+::setup (\begin{DoxyParamCaption}\item[{int}]{req\+Order,  }\item[{double}]{sample\+Rate,  }\item[{double}]{cutoff\+Frequency,  }\item[{double}]{gain\+Db,  }\item[{double}]{ripple\+Db }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients of the filter at specified order 
\begin{DoxyParams}{Parameters}
{\em req\+Order} & Actual order for the filter calculations \\
\hline
{\em sample\+Rate} & Sampling rate \\
\hline
{\em cutoff\+Frequency} & Cutoff frequency. \\
\hline
{\em gain\+Db} & Gain in the passband \\
\hline
{\em ripple\+Db} & Permitted ripples in dB in the passband \\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{structIir_1_1ChebyshevI_1_1HighShelf_aa078bdf604ac9feeedd282174bd0cea9}\label{structIir_1_1ChebyshevI_1_1HighShelf_aa078bdf604ac9feeedd282174bd0cea9}} 
\index{Iir::ChebyshevI::HighShelf$<$ FilterOrder, StateType $>$@{Iir::ChebyshevI::HighShelf$<$ FilterOrder, StateType $>$}!setupN@{setupN}}
\index{setupN@{setupN}!Iir::ChebyshevI::HighShelf$<$ FilterOrder, StateType $>$@{Iir::ChebyshevI::HighShelf$<$ FilterOrder, StateType $>$}}
\doxyparagraph{\texorpdfstring{setupN()}{setupN()}\hspace{0.1cm}{\footnotesize\ttfamily [1/2]}}
{\footnotesize\ttfamily template$<$int Filter\+Order = 4, class State\+Type  = Direct\+Form\+II$>$ \\
void \mbox{\hyperlink{structIir_1_1ChebyshevI_1_1HighShelf}{Iir\+::\+Chebyshev\+I\+::\+High\+Shelf}}$<$ Filter\+Order, State\+Type $>$\+::setupN (\begin{DoxyParamCaption}\item[{double}]{cutoff\+Frequency,  }\item[{double}]{gain\+Db,  }\item[{double}]{ripple\+Db }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients of the filter at the order Filter\+Order 
\begin{DoxyParams}{Parameters}
{\em cutoff\+Frequency} & Normalised cutoff frequency (0..1/2) \\
\hline
{\em gain\+Db} & Gain in the passband \\
\hline
{\em ripple\+Db} & Permitted ripples in dB in the passband \\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{structIir_1_1ChebyshevI_1_1HighShelf_adbfa0e3487393075e3f178d50293f0bb}\label{structIir_1_1ChebyshevI_1_1HighShelf_adbfa0e3487393075e3f178d50293f0bb}} 
\index{Iir::ChebyshevI::HighShelf$<$ FilterOrder, StateType $>$@{Iir::ChebyshevI::HighShelf$<$ FilterOrder, StateType $>$}!setupN@{setupN}}
\index{setupN@{setupN}!Iir::ChebyshevI::HighShelf$<$ FilterOrder, StateType $>$@{Iir::ChebyshevI::HighShelf$<$ FilterOrder, StateType $>$}}
\doxyparagraph{\texorpdfstring{setupN()}{setupN()}\hspace{0.1cm}{\footnotesize\ttfamily [2/2]}}
{\footnotesize\ttfamily template$<$int Filter\+Order = 4, class State\+Type  = Direct\+Form\+II$>$ \\
void \mbox{\hyperlink{structIir_1_1ChebyshevI_1_1HighShelf}{Iir\+::\+Chebyshev\+I\+::\+High\+Shelf}}$<$ Filter\+Order, State\+Type $>$\+::setupN (\begin{DoxyParamCaption}\item[{int}]{req\+Order,  }\item[{double}]{cutoff\+Frequency,  }\item[{double}]{gain\+Db,  }\item[{double}]{ripple\+Db }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients of the filter at specified order 
\begin{DoxyParams}{Parameters}
{\em req\+Order} & Actual order for the filter calculations \\
\hline
{\em cutoff\+Frequency} & Normalised cutoff frequency (0..1/2) \\
\hline
{\em gain\+Db} & Gain in the passband \\
\hline
{\em ripple\+Db} & Permitted ripples in dB in the passband \\
\hline
\end{DoxyParams}


The documentation for this struct was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
iir/Chebyshev\+I.\+h\end{DoxyCompactItemize}
