/********************************************************************************
** Form generated from reading UI file 'miccalibrationform.ui'
**
** Created by: Qt User Interface Compiler version 5.15.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_MICCALIBRATIONFORM_H
#define UI_MICCALIBRATIONFORM_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_MicCalibrationForm
{
public:
    QVBoxLayout *verticalLayout_6;
    QWidget *WaveWidget;
    QWidget *widget_4;
    QHBoxLayout *horizontalLayout;
    QGroupBox *groupBox;
    QVBoxLayout *verticalLayout;
    QLabel *label;
    QLineEdit *CalibratorSPLlineEdit;
    QLabel *label_2;
    QLineEdit *CalibratorFrelineEdit;
    QGroupBox *groupBox_2;
    QVBoxLayout *verticalLayout_2;
    QLabel *label_3;
    QComboBox *SampcomboBox;
    QLabel *label_4;
    QComboBox *ChannelcomboBox;
    QWidget *widget_2;
    QVBoxLayout *verticalLayout_4;
    QLabel *label_7;
    QComboBox *MiccomboBox;
    QSpacerItem *verticalSpacer;
    QGroupBox *groupBox_3;
    QVBoxLayout *verticalLayout_3;
    QLabel *label_5;
    QLineEdit *ResultMvPalineEdit;
    QLabel *label_6;
    QLineEdit *ResultdBlineEdit;
    QWidget *widget_3;
    QVBoxLayout *verticalLayout_5;
    QPushButton *StarStopButton;
    QPushButton *SavepushButton;
    QPushButton *ExitButton;

    void setupUi(QWidget *MicCalibrationForm)
    {
        if (MicCalibrationForm->objectName().isEmpty())
            MicCalibrationForm->setObjectName(QString::fromUtf8("MicCalibrationForm"));
        MicCalibrationForm->resize(972, 493);
        verticalLayout_6 = new QVBoxLayout(MicCalibrationForm);
        verticalLayout_6->setObjectName(QString::fromUtf8("verticalLayout_6"));
        WaveWidget = new QWidget(MicCalibrationForm);
        WaveWidget->setObjectName(QString::fromUtf8("WaveWidget"));
        WaveWidget->setMinimumSize(QSize(950, 300));

        verticalLayout_6->addWidget(WaveWidget);

        widget_4 = new QWidget(MicCalibrationForm);
        widget_4->setObjectName(QString::fromUtf8("widget_4"));
        horizontalLayout = new QHBoxLayout(widget_4);
        horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));
        groupBox = new QGroupBox(widget_4);
        groupBox->setObjectName(QString::fromUtf8("groupBox"));
        groupBox->setMaximumSize(QSize(150, 16777215));
        verticalLayout = new QVBoxLayout(groupBox);
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        label = new QLabel(groupBox);
        label->setObjectName(QString::fromUtf8("label"));

        verticalLayout->addWidget(label);

        CalibratorSPLlineEdit = new QLineEdit(groupBox);
        CalibratorSPLlineEdit->setObjectName(QString::fromUtf8("CalibratorSPLlineEdit"));

        verticalLayout->addWidget(CalibratorSPLlineEdit);

        label_2 = new QLabel(groupBox);
        label_2->setObjectName(QString::fromUtf8("label_2"));

        verticalLayout->addWidget(label_2);

        CalibratorFrelineEdit = new QLineEdit(groupBox);
        CalibratorFrelineEdit->setObjectName(QString::fromUtf8("CalibratorFrelineEdit"));

        verticalLayout->addWidget(CalibratorFrelineEdit);


        horizontalLayout->addWidget(groupBox);

        groupBox_2 = new QGroupBox(widget_4);
        groupBox_2->setObjectName(QString::fromUtf8("groupBox_2"));
        groupBox_2->setMaximumSize(QSize(200, 16777215));
        verticalLayout_2 = new QVBoxLayout(groupBox_2);
        verticalLayout_2->setObjectName(QString::fromUtf8("verticalLayout_2"));
        label_3 = new QLabel(groupBox_2);
        label_3->setObjectName(QString::fromUtf8("label_3"));

        verticalLayout_2->addWidget(label_3);

        SampcomboBox = new QComboBox(groupBox_2);
        SampcomboBox->addItem(QString());
        SampcomboBox->addItem(QString());
        SampcomboBox->addItem(QString());
        SampcomboBox->addItem(QString());
        SampcomboBox->addItem(QString());
        SampcomboBox->addItem(QString());
        SampcomboBox->addItem(QString());
        SampcomboBox->addItem(QString());
        SampcomboBox->addItem(QString());
        SampcomboBox->addItem(QString());
        SampcomboBox->setObjectName(QString::fromUtf8("SampcomboBox"));
        QFont font;
        font.setPointSize(12);
        SampcomboBox->setFont(font);

        verticalLayout_2->addWidget(SampcomboBox);

        label_4 = new QLabel(groupBox_2);
        label_4->setObjectName(QString::fromUtf8("label_4"));

        verticalLayout_2->addWidget(label_4);

        ChannelcomboBox = new QComboBox(groupBox_2);
        ChannelcomboBox->setObjectName(QString::fromUtf8("ChannelcomboBox"));
        ChannelcomboBox->setFont(font);

        verticalLayout_2->addWidget(ChannelcomboBox);


        horizontalLayout->addWidget(groupBox_2);

        widget_2 = new QWidget(widget_4);
        widget_2->setObjectName(QString::fromUtf8("widget_2"));
        verticalLayout_4 = new QVBoxLayout(widget_2);
        verticalLayout_4->setObjectName(QString::fromUtf8("verticalLayout_4"));
        label_7 = new QLabel(widget_2);
        label_7->setObjectName(QString::fromUtf8("label_7"));

        verticalLayout_4->addWidget(label_7);

        MiccomboBox = new QComboBox(widget_2);
        MiccomboBox->setObjectName(QString::fromUtf8("MiccomboBox"));
        MiccomboBox->setFont(font);

        verticalLayout_4->addWidget(MiccomboBox);

        verticalSpacer = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout_4->addItem(verticalSpacer);


        horizontalLayout->addWidget(widget_2);

        groupBox_3 = new QGroupBox(widget_4);
        groupBox_3->setObjectName(QString::fromUtf8("groupBox_3"));
        groupBox_3->setMaximumSize(QSize(200, 16777215));
        groupBox_3->setStyleSheet(QString::fromUtf8(""));
        verticalLayout_3 = new QVBoxLayout(groupBox_3);
        verticalLayout_3->setObjectName(QString::fromUtf8("verticalLayout_3"));
        label_5 = new QLabel(groupBox_3);
        label_5->setObjectName(QString::fromUtf8("label_5"));

        verticalLayout_3->addWidget(label_5);

        ResultMvPalineEdit = new QLineEdit(groupBox_3);
        ResultMvPalineEdit->setObjectName(QString::fromUtf8("ResultMvPalineEdit"));

        verticalLayout_3->addWidget(ResultMvPalineEdit);

        label_6 = new QLabel(groupBox_3);
        label_6->setObjectName(QString::fromUtf8("label_6"));

        verticalLayout_3->addWidget(label_6);

        ResultdBlineEdit = new QLineEdit(groupBox_3);
        ResultdBlineEdit->setObjectName(QString::fromUtf8("ResultdBlineEdit"));

        verticalLayout_3->addWidget(ResultdBlineEdit);


        horizontalLayout->addWidget(groupBox_3);

        widget_3 = new QWidget(widget_4);
        widget_3->setObjectName(QString::fromUtf8("widget_3"));
        widget_3->setMaximumSize(QSize(150, 16777215));
        verticalLayout_5 = new QVBoxLayout(widget_3);
        verticalLayout_5->setObjectName(QString::fromUtf8("verticalLayout_5"));
        StarStopButton = new QPushButton(widget_3);
        StarStopButton->setObjectName(QString::fromUtf8("StarStopButton"));

        verticalLayout_5->addWidget(StarStopButton);

        SavepushButton = new QPushButton(widget_3);
        SavepushButton->setObjectName(QString::fromUtf8("SavepushButton"));

        verticalLayout_5->addWidget(SavepushButton);

        ExitButton = new QPushButton(widget_3);
        ExitButton->setObjectName(QString::fromUtf8("ExitButton"));

        verticalLayout_5->addWidget(ExitButton);


        horizontalLayout->addWidget(widget_3);


        verticalLayout_6->addWidget(widget_4);


        retranslateUi(MicCalibrationForm);

        QMetaObject::connectSlotsByName(MicCalibrationForm);
    } // setupUi

    void retranslateUi(QWidget *MicCalibrationForm)
    {
        MicCalibrationForm->setWindowTitle(QCoreApplication::translate("MicCalibrationForm", "Form", nullptr));
        groupBox->setTitle(QCoreApplication::translate("MicCalibrationForm", "\350\256\276\347\275\256\345\243\260\346\240\241\345\207\206\345\231\250\345\217\202\346\225\260", nullptr));
        label->setText(QCoreApplication::translate("MicCalibrationForm", "\345\243\260\345\216\213\347\272\247(dB)", nullptr));
        CalibratorSPLlineEdit->setText(QCoreApplication::translate("MicCalibrationForm", "94", nullptr));
        label_2->setText(QCoreApplication::translate("MicCalibrationForm", "\351\242\221\347\216\207(Hz)", nullptr));
        CalibratorFrelineEdit->setText(QCoreApplication::translate("MicCalibrationForm", "1000", nullptr));
        groupBox_2->setTitle(QCoreApplication::translate("MicCalibrationForm", "\350\256\276\347\275\256\351\200\232\351\201\223\344\277\241\346\201\257", nullptr));
        label_3->setText(QCoreApplication::translate("MicCalibrationForm", "\351\207\207\346\240\267\347\216\207", nullptr));
        SampcomboBox->setItemText(0, QCoreApplication::translate("MicCalibrationForm", "8000 Hz", nullptr));
        SampcomboBox->setItemText(1, QCoreApplication::translate("MicCalibrationForm", "11025 Hz", nullptr));
        SampcomboBox->setItemText(2, QCoreApplication::translate("MicCalibrationForm", "16000 Hz", nullptr));
        SampcomboBox->setItemText(3, QCoreApplication::translate("MicCalibrationForm", "22050 Hz", nullptr));
        SampcomboBox->setItemText(4, QCoreApplication::translate("MicCalibrationForm", "44100 Hz", nullptr));
        SampcomboBox->setItemText(5, QCoreApplication::translate("MicCalibrationForm", "48000 Hz", nullptr));
        SampcomboBox->setItemText(6, QCoreApplication::translate("MicCalibrationForm", "88200 Hz", nullptr));
        SampcomboBox->setItemText(7, QCoreApplication::translate("MicCalibrationForm", "96000 Hz", nullptr));
        SampcomboBox->setItemText(8, QCoreApplication::translate("MicCalibrationForm", "176400 Hz", nullptr));
        SampcomboBox->setItemText(9, QCoreApplication::translate("MicCalibrationForm", "192000 Hz", nullptr));

        label_4->setText(QCoreApplication::translate("MicCalibrationForm", "\351\200\232\351\201\223", nullptr));
        label_7->setText(QCoreApplication::translate("MicCalibrationForm", "\351\200\211\346\213\251\351\272\246\345\205\213\351\243\216", nullptr));
        groupBox_3->setTitle(QCoreApplication::translate("MicCalibrationForm", "\346\240\241\345\207\206\347\273\223\346\236\234", nullptr));
        label_5->setText(QCoreApplication::translate("MicCalibrationForm", "\345\243\260\345\216\213\347\201\265\346\225\217\345\272\246mV/Pa", nullptr));
        ResultMvPalineEdit->setText(QCoreApplication::translate("MicCalibrationForm", "0", nullptr));
        label_6->setText(QCoreApplication::translate("MicCalibrationForm", "\345\243\260\345\216\213\347\201\265\346\225\217\345\272\246\347\272\247dB", nullptr));
        ResultdBlineEdit->setText(QCoreApplication::translate("MicCalibrationForm", "0", nullptr));
        StarStopButton->setText(QCoreApplication::translate("MicCalibrationForm", "\345\274\200\345\247\213", nullptr));
        SavepushButton->setText(QCoreApplication::translate("MicCalibrationForm", "\344\277\235\345\255\230", nullptr));
        ExitButton->setText(QCoreApplication::translate("MicCalibrationForm", "\351\200\200\345\207\272", nullptr));
    } // retranslateUi

};

namespace Ui {
    class MicCalibrationForm: public Ui_MicCalibrationForm {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_MICCALIBRATIONFORM_H
