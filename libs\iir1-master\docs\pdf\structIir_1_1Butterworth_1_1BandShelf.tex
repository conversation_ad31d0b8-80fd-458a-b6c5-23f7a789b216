\hypertarget{structIir_1_1Butterworth_1_1BandShelf}{}\doxysubsection{Iir\+::Butterworth\+::Band\+Shelf$<$ Filter\+Order, State\+Type $>$ Struct Template Reference}
\label{structIir_1_1Butterworth_1_1BandShelf}\index{Iir::Butterworth::BandShelf$<$ FilterOrder, StateType $>$@{Iir::Butterworth::BandShelf$<$ FilterOrder, StateType $>$}}


{\ttfamily \#include $<$Butterworth.\+h$>$}

Inheritance diagram for Iir\+::Butterworth\+::Band\+Shelf$<$ Filter\+Order, State\+Type $>$\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=2.434783cm]{structIir_1_1Butterworth_1_1BandShelf}
\end{center}
\end{figure}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
void \mbox{\hyperlink{structIir_1_1Butterworth_1_1BandShelf_a0de40c55dae63d23127cf4dd21bcf050}{setup}} (double sample\+Rate, double center\+Frequency, double width\+Frequency, double gain\+Db)
\item 
void \mbox{\hyperlink{structIir_1_1Butterworth_1_1BandShelf_aa3f716cf9d393509c2caefb9e10ae8cc}{setup}} (int req\+Order, double sample\+Rate, double center\+Frequency, double width\+Frequency, double gain\+Db)
\item 
void \mbox{\hyperlink{structIir_1_1Butterworth_1_1BandShelf_a43e02ec9dcfcd382b87ce20bc48f2ee2}{setupN}} (double center\+Frequency, double width\+Frequency, double gain\+Db)
\item 
void \mbox{\hyperlink{structIir_1_1Butterworth_1_1BandShelf_a96cc173942cd6789b5aff603b37e5b2f}{setupN}} (int req\+Order, double center\+Frequency, double width\+Frequency, double gain\+Db)
\end{DoxyCompactItemize}


\doxysubsubsection{Detailed Description}
\subsubsection*{template$<$int Filter\+Order = 4, class State\+Type = Direct\+Form\+II$>$\newline
struct Iir\+::\+Butterworth\+::\+Band\+Shelf$<$ Filter\+Order, State\+Type $>$}

\mbox{\hyperlink{namespaceIir_1_1Butterworth}{Butterworth}} Bandshelf filter\+: it is a bandpass filter which amplifies at a specified gain in dB the frequencies in the passband. 
\begin{DoxyParams}{Parameters}
{\em Filter\+Order} & Reserves memory for a filter of the order Filter\+Order \\
\hline
{\em State\+Type} & The filter topology\+: \mbox{\hyperlink{classIir_1_1DirectFormI}{Direct\+FormI}}, \mbox{\hyperlink{classIir_1_1DirectFormII}{Direct\+Form\+II}}, ... \\
\hline
\end{DoxyParams}


\doxysubsubsection{Member Function Documentation}
\mbox{\Hypertarget{structIir_1_1Butterworth_1_1BandShelf_a0de40c55dae63d23127cf4dd21bcf050}\label{structIir_1_1Butterworth_1_1BandShelf_a0de40c55dae63d23127cf4dd21bcf050}} 
\index{Iir::Butterworth::BandShelf$<$ FilterOrder, StateType $>$@{Iir::Butterworth::BandShelf$<$ FilterOrder, StateType $>$}!setup@{setup}}
\index{setup@{setup}!Iir::Butterworth::BandShelf$<$ FilterOrder, StateType $>$@{Iir::Butterworth::BandShelf$<$ FilterOrder, StateType $>$}}
\doxyparagraph{\texorpdfstring{setup()}{setup()}\hspace{0.1cm}{\footnotesize\ttfamily [1/2]}}
{\footnotesize\ttfamily template$<$int Filter\+Order = 4, class State\+Type  = Direct\+Form\+II$>$ \\
void \mbox{\hyperlink{structIir_1_1Butterworth_1_1BandShelf}{Iir\+::\+Butterworth\+::\+Band\+Shelf}}$<$ Filter\+Order, State\+Type $>$\+::setup (\begin{DoxyParamCaption}\item[{double}]{sample\+Rate,  }\item[{double}]{center\+Frequency,  }\item[{double}]{width\+Frequency,  }\item[{double}]{gain\+Db }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients with the filter order provided by the instantiation 
\begin{DoxyParams}{Parameters}
{\em sample\+Rate} & Sampling rate \\
\hline
{\em center\+Frequency} & Centre frequency of the passband \\
\hline
{\em width\+Frequency} & Width of the passband \\
\hline
{\em gain\+Db} & The gain in the passband \\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{structIir_1_1Butterworth_1_1BandShelf_aa3f716cf9d393509c2caefb9e10ae8cc}\label{structIir_1_1Butterworth_1_1BandShelf_aa3f716cf9d393509c2caefb9e10ae8cc}} 
\index{Iir::Butterworth::BandShelf$<$ FilterOrder, StateType $>$@{Iir::Butterworth::BandShelf$<$ FilterOrder, StateType $>$}!setup@{setup}}
\index{setup@{setup}!Iir::Butterworth::BandShelf$<$ FilterOrder, StateType $>$@{Iir::Butterworth::BandShelf$<$ FilterOrder, StateType $>$}}
\doxyparagraph{\texorpdfstring{setup()}{setup()}\hspace{0.1cm}{\footnotesize\ttfamily [2/2]}}
{\footnotesize\ttfamily template$<$int Filter\+Order = 4, class State\+Type  = Direct\+Form\+II$>$ \\
void \mbox{\hyperlink{structIir_1_1Butterworth_1_1BandShelf}{Iir\+::\+Butterworth\+::\+Band\+Shelf}}$<$ Filter\+Order, State\+Type $>$\+::setup (\begin{DoxyParamCaption}\item[{int}]{req\+Order,  }\item[{double}]{sample\+Rate,  }\item[{double}]{center\+Frequency,  }\item[{double}]{width\+Frequency,  }\item[{double}]{gain\+Db }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients 
\begin{DoxyParams}{Parameters}
{\em req\+Order} & The actual order which can be less than the instantiated one \\
\hline
{\em sample\+Rate} & Sampling rate \\
\hline
{\em center\+Frequency} & Centre frequency of the passband \\
\hline
{\em width\+Frequency} & Width of the passband \\
\hline
{\em gain\+Db} & The gain in the passband \\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{structIir_1_1Butterworth_1_1BandShelf_a43e02ec9dcfcd382b87ce20bc48f2ee2}\label{structIir_1_1Butterworth_1_1BandShelf_a43e02ec9dcfcd382b87ce20bc48f2ee2}} 
\index{Iir::Butterworth::BandShelf$<$ FilterOrder, StateType $>$@{Iir::Butterworth::BandShelf$<$ FilterOrder, StateType $>$}!setupN@{setupN}}
\index{setupN@{setupN}!Iir::Butterworth::BandShelf$<$ FilterOrder, StateType $>$@{Iir::Butterworth::BandShelf$<$ FilterOrder, StateType $>$}}
\doxyparagraph{\texorpdfstring{setupN()}{setupN()}\hspace{0.1cm}{\footnotesize\ttfamily [1/2]}}
{\footnotesize\ttfamily template$<$int Filter\+Order = 4, class State\+Type  = Direct\+Form\+II$>$ \\
void \mbox{\hyperlink{structIir_1_1Butterworth_1_1BandShelf}{Iir\+::\+Butterworth\+::\+Band\+Shelf}}$<$ Filter\+Order, State\+Type $>$\+::setupN (\begin{DoxyParamCaption}\item[{double}]{center\+Frequency,  }\item[{double}]{width\+Frequency,  }\item[{double}]{gain\+Db }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients with the filter order provided by the instantiation 
\begin{DoxyParams}{Parameters}
{\em center\+Frequency} & Normalised centre frequency (0..1/2) of the passband \\
\hline
{\em width\+Frequency} & Width of the passband \\
\hline
{\em gain\+Db} & The gain in the passband \\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{structIir_1_1Butterworth_1_1BandShelf_a96cc173942cd6789b5aff603b37e5b2f}\label{structIir_1_1Butterworth_1_1BandShelf_a96cc173942cd6789b5aff603b37e5b2f}} 
\index{Iir::Butterworth::BandShelf$<$ FilterOrder, StateType $>$@{Iir::Butterworth::BandShelf$<$ FilterOrder, StateType $>$}!setupN@{setupN}}
\index{setupN@{setupN}!Iir::Butterworth::BandShelf$<$ FilterOrder, StateType $>$@{Iir::Butterworth::BandShelf$<$ FilterOrder, StateType $>$}}
\doxyparagraph{\texorpdfstring{setupN()}{setupN()}\hspace{0.1cm}{\footnotesize\ttfamily [2/2]}}
{\footnotesize\ttfamily template$<$int Filter\+Order = 4, class State\+Type  = Direct\+Form\+II$>$ \\
void \mbox{\hyperlink{structIir_1_1Butterworth_1_1BandShelf}{Iir\+::\+Butterworth\+::\+Band\+Shelf}}$<$ Filter\+Order, State\+Type $>$\+::setupN (\begin{DoxyParamCaption}\item[{int}]{req\+Order,  }\item[{double}]{center\+Frequency,  }\item[{double}]{width\+Frequency,  }\item[{double}]{gain\+Db }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [inline]}}

Calculates the coefficients 
\begin{DoxyParams}{Parameters}
{\em req\+Order} & The actual order which can be less than the instantiated one \\
\hline
{\em center\+Frequency} & Normalised centre frequency (0..1/2) of the passband \\
\hline
{\em width\+Frequency} & Width of the passband \\
\hline
{\em gain\+Db} & The gain in the passband \\
\hline
\end{DoxyParams}


The documentation for this struct was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
iir/Butterworth.\+h\end{DoxyCompactItemize}
