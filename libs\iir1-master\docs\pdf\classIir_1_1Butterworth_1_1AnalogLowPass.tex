\hypertarget{classIir_1_1Butterworth_1_1AnalogLowPass}{}\doxysubsection{Iir\+::Butterworth\+::Analog\+Low\+Pass Class Reference}
\label{classIir_1_1Butterworth_1_1AnalogLowPass}\index{Iir::Butterworth::AnalogLowPass@{Iir::Butterworth::AnalogLowPass}}


{\ttfamily \#include $<$Butterworth.\+h$>$}

Inheritance diagram for Iir\+::Butterworth\+::Analog\+Low\+Pass\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=2.000000cm]{classIir_1_1Butterworth_1_1AnalogLowPass}
\end{center}
\end{figure}


\doxysubsubsection{Detailed Description}
Analogue lowpass prototypes (s-\/plane) 

The documentation for this class was generated from the following files\+:\begin{DoxyCompactItemize}
\item 
iir/Butterworth.\+h\item 
iir/Butterworth.\+cpp\end{DoxyCompactItemize}
