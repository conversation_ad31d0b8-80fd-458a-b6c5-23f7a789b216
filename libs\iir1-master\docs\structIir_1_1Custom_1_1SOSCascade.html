<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>iir1: Iir::Custom::SOSCascade&lt; NSOS, StateType &gt; Struct Template Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">iir1
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespaceIir.html">Iir</a></li><li class="navelem"><a class="el" href="namespaceIir_1_1Custom.html">Custom</a></li><li class="navelem"><a class="el" href="structIir_1_1Custom_1_1SOSCascade.html">SOSCascade</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="structIir_1_1Custom_1_1SOSCascade-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">Iir::Custom::SOSCascade&lt; NSOS, StateType &gt; Struct Template Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;<a class="el" href="Custom_8h_source.html">Custom.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for Iir::Custom::SOSCascade&lt; NSOS, StateType &gt;:</div>
<div class="dyncontent">
 <div class="center">
  <img src="structIir_1_1Custom_1_1SOSCascade.png" usemap="#Iir::Custom::SOSCascade_3C_20NSOS_2C_20StateType_20_3E_map" alt=""/>
  <map id="Iir::Custom::SOSCascade_3C_20NSOS_2C_20StateType_20_3E_map" name="Iir::Custom::SOSCascade_3C_20NSOS_2C_20StateType_20_3E_map">
<area href="classIir_1_1CascadeStages.html" alt="Iir::CascadeStages&lt; NSOS, DirectFormII &gt;" shape="rect" coords="0,0,276,24"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a04671c1c84cacaf1c822adeb850ccc63"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structIir_1_1Custom_1_1SOSCascade.html#a04671c1c84cacaf1c822adeb850ccc63">SOSCascade</a> ()=default</td></tr>
<tr class="separator:a04671c1c84cacaf1c822adeb850ccc63"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a60654fe4bd861799dff82a6dce9dfd42"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structIir_1_1Custom_1_1SOSCascade.html#a60654fe4bd861799dff82a6dce9dfd42">SOSCascade</a> (const double(&amp;sosCoefficients)[NSOS][6])</td></tr>
<tr class="separator:a60654fe4bd861799dff82a6dce9dfd42"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aae603eb1bd4330411a6003e09ba36efc"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structIir_1_1Custom_1_1SOSCascade.html#aae603eb1bd4330411a6003e09ba36efc">setup</a> (const double(&amp;sosCoefficients)[NSOS][6])</td></tr>
<tr class="separator:aae603eb1bd4330411a6003e09ba36efc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_methods_classIir_1_1CascadeStages"><td colspan="2" onclick="javascript:toggleInherit('pub_methods_classIir_1_1CascadeStages')"><img src="closed.png" alt="-"/>&#160;Public Member Functions inherited from <a class="el" href="classIir_1_1CascadeStages.html">Iir::CascadeStages&lt; NSOS, DirectFormII &gt;</a></td></tr>
<tr class="memitem:ae5253fde0be7ccfa459a3f70fe8e3e31 inherit pub_methods_classIir_1_1CascadeStages"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classIir_1_1CascadeStages.html#ae5253fde0be7ccfa459a3f70fe8e3e31">reset</a> ()</td></tr>
<tr class="separator:ae5253fde0be7ccfa459a3f70fe8e3e31 inherit pub_methods_classIir_1_1CascadeStages"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a56d24da4cf09f898b534b1b05578244d inherit pub_methods_classIir_1_1CascadeStages"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classIir_1_1CascadeStages.html#a56d24da4cf09f898b534b1b05578244d">setup</a> (const double(&amp;sosCoefficients)[MaxStages][6])</td></tr>
<tr class="separator:a56d24da4cf09f898b534b1b05578244d inherit pub_methods_classIir_1_1CascadeStages"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa18e9abcaac65fd21be31ee859add3bb inherit pub_methods_classIir_1_1CascadeStages"><td class="memItemLeft" align="right" valign="top">Sample&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classIir_1_1CascadeStages.html#aa18e9abcaac65fd21be31ee859add3bb">filter</a> (const Sample in)</td></tr>
<tr class="separator:aa18e9abcaac65fd21be31ee859add3bb inherit pub_methods_classIir_1_1CascadeStages"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a034a9be8ae590b814c8499898a93987a inherit pub_methods_classIir_1_1CascadeStages"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="structIir_1_1Cascade_1_1Storage.html">Cascade::Storage</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classIir_1_1CascadeStages.html#a034a9be8ae590b814c8499898a93987a">getCascadeStorage</a> ()</td></tr>
<tr class="separator:a034a9be8ae590b814c8499898a93987a inherit pub_methods_classIir_1_1CascadeStages"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><h3>template&lt;int NSOS, class StateType = DirectFormII&gt;<br />
struct Iir::Custom::SOSCascade&lt; NSOS, StateType &gt;</h3>

<p>A custom cascade of 2nd order (SOS / biquads) filters. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">NSOS</td><td>The number of 2nd order filters / biquads. </td></tr>
    <tr><td class="paramname">StateType</td><td>The filter topology: <a class="el" href="classIir_1_1DirectFormI.html">DirectFormI</a>, <a class="el" href="classIir_1_1DirectFormII.html">DirectFormII</a>, ... </td></tr>
  </table>
  </dd>
</dl>
</div><h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="a04671c1c84cacaf1c822adeb850ccc63"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a04671c1c84cacaf1c822adeb850ccc63">&#9670;&nbsp;</a></span>SOSCascade() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int NSOS, class StateType  = DirectFormII&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structIir_1_1Custom_1_1SOSCascade.html">Iir::Custom::SOSCascade</a>&lt; NSOS, StateType &gt;::<a class="el" href="structIir_1_1Custom_1_1SOSCascade.html">SOSCascade</a> </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">default</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Default constructor which creates a unity gain filter of NSOS biquads. Set the filter coefficients later with the <a class="el" href="structIir_1_1Custom_1_1SOSCascade.html#aae603eb1bd4330411a6003e09ba36efc">setup()</a> method. </p>

</div>
</div>
<a id="a60654fe4bd861799dff82a6dce9dfd42"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a60654fe4bd861799dff82a6dce9dfd42">&#9670;&nbsp;</a></span>SOSCascade() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int NSOS, class StateType  = DirectFormII&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structIir_1_1Custom_1_1SOSCascade.html">Iir::Custom::SOSCascade</a>&lt; NSOS, StateType &gt;::<a class="el" href="structIir_1_1Custom_1_1SOSCascade.html">SOSCascade</a> </td>
          <td>(</td>
          <td class="paramtype">const double(&amp;)&#160;</td>
          <td class="paramname"><em>sosCoefficients</em>[NSOS][6]</td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Python scipy.signal-friendly setting of coefficients. Initialises the coefficients of the whole chain of biquads / SOS. The argument is a 2D array where the 1st dimension holds an array of 2nd order biquad / SOS coefficients. The six SOS coefficients are ordered "Python" style with first the FIR coefficients (B) and then the IIR coefficients (A). The 2D const double array needs to have exactly the size [NSOS][6]. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">sosCoefficients</td><td>2D array Python style sos[NSOS][6]. Indexing: 0-2: FIR-, 3-5: IIR-coefficients. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="aae603eb1bd4330411a6003e09ba36efc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aae603eb1bd4330411a6003e09ba36efc">&#9670;&nbsp;</a></span>setup()</h2>

<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int NSOS, class StateType  = DirectFormII&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void <a class="el" href="structIir_1_1Custom_1_1SOSCascade.html">Iir::Custom::SOSCascade</a>&lt; NSOS, StateType &gt;::setup </td>
          <td>(</td>
          <td class="paramtype">const double(&amp;)&#160;</td>
          <td class="paramname"><em>sosCoefficients</em>[NSOS][6]</td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Python scipy.signal-friendly setting of coefficients. Sets the coefficients of the whole chain of biquads / SOS. The argument is a 2D array where the 1st dimension holds an array of 2nd order biquad / SOS coefficients. The six SOS coefficients are ordered "Python" style with first the FIR coefficients (B) and then the IIR coefficients (A). The 2D const double array needs to have exactly the size [NSOS][6]. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">sosCoefficients</td><td>2D array Python style sos[NSOS][6]. Indexing: 0-2: FIR-, 3-5: IIR-coefficients. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li>iir/<a class="el" href="Custom_8h_source.html">Custom.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Fri Jan 6 2023 23:53:45 for iir1 by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
