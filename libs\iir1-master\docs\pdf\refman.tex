\let\mypdfximage\pdfximage\def\pdfximage{\immediate\mypdfximage}\documentclass[twoside]{article}

%% moved from doxygen.sty due to workaround for LaTex 2019 version and unmaintained tabu package
\usepackage{ifthen}
\ifx\requestedLaTeXdate\undefined
\usepackage{array}
\else
\usepackage{array}[=2016-10-06]
\fi
%%
% Packages required by doxygen
\usepackage{fixltx2e}
\usepackage{doxygen}
\usepackage{graphicx}
\usepackage[utf8]{inputenc}
\usepackage{makeidx}
\PassOptionsToPackage{warn}{textcomp}
\usepackage{textcomp}
\usepackage[nointegrals]{wasysym}
\usepackage{ifxetex}

% Font selection
\usepackage[T1]{fontenc}
\usepackage[scaled=.90]{helvet}
\usepackage{courier}
\renewcommand{\familydefault}{\sfdefault}
\usepackage{sectsty}
\allsectionsfont{%
  \fontseries{bc}\selectfont%
  \color{darkgray}%
}
\renewcommand{\DoxyLabelFont}{%
  \fontseries{bc}\selectfont%
  \color{darkgray}%
}
\newcommand{\+}{\discretionary{\mbox{\scriptsize$\hookleftarrow$}}{}{}}

% Arguments of doxygenemoji:
% 1) ':<text>:' form of the emoji, already "LaTeX"-escaped
% 2) file with the name of the emoji without the .png extension
% in case image exist use this otherwise use the ':<text>:' form
\newcommand{\doxygenemoji}[2]{%
  \IfFileExists{./#2.png}{\raisebox{-0.1em}{\includegraphics[height=0.9em]{./#2.png}}}{#1}%
}
% Page & text layout
\usepackage{geometry}
\geometry{%
  a4paper,%
  top=2.5cm,%
  bottom=2.5cm,%
  left=2.5cm,%
  right=2.5cm%
}
\tolerance=750
\hfuzz=15pt
\hbadness=750
\setlength{\emergencystretch}{15pt}
\setlength{\parindent}{0cm}
\newcommand{\doxynormalparskip}{\setlength{\parskip}{3ex plus 2ex minus 2ex}}
\newcommand{\doxytocparskip}{\setlength{\parskip}{1ex plus 0ex minus 0ex}}
\doxynormalparskip
\makeatletter
\renewcommand{\paragraph}{%
  \@startsection{paragraph}{4}{0ex}{-1.0ex}{1.0ex}{%
    \normalfont\normalsize\bfseries\SS@parafont%
  }%
}
\renewcommand{\subparagraph}{%
  \@startsection{subparagraph}{5}{0ex}{-1.0ex}{1.0ex}{%
    \normalfont\normalsize\bfseries\SS@subparafont%
  }%
}
\makeatother

\makeatletter
\newcommand\hrulefilll{\leavevmode\leaders\hrule\hskip 0pt plus 1filll\kern\z@}
\makeatother

% Headers & footers
\usepackage{fancyhdr}
\pagestyle{fancyplain}
\renewcommand{\footrulewidth}{0.4pt}
%
\fancypagestyle{fancyplain}{
\fancyhf{}
\fancyhead[LE, RO]{\bfseries\thepage}
\fancyhead[LO]{\bfseries\rightmark}
\fancyhead[RE]{\bfseries\leftmark}
\fancyfoot[LO, RE]{\bfseries\scriptsize Generated by Doxygen }
}
%
\fancypagestyle{plain}{
\fancyhf{}
\fancyfoot[LO, RE]{\bfseries\scriptsize Generated by Doxygen }
\renewcommand{\headrulewidth}{0pt}}
%
\pagestyle{fancyplain}
%
\renewcommand{\sectionmark}[1]{%
  \markright{\thesection\ #1}%
}

% Indices & bibliography
\usepackage{natbib}
\usepackage[titles]{tocloft}
\setcounter{tocdepth}{3}
\setcounter{secnumdepth}{5}
\makeindex

\usepackage{newunicodechar}
  \newunicodechar{⁻}{${}^{-}$}% Superscript minus
  \newunicodechar{²}{${}^{2}$}% Superscript two
  \newunicodechar{³}{${}^{3}$}% Superscript three

% Hyperlinks (required, but should be loaded last)
\ifpdf
  \usepackage[pdftex,pagebackref=true]{hyperref}
\else
  \ifxetex
    \usepackage[pagebackref=true]{hyperref}
  \else
    \usepackage[ps2pdf,pagebackref=true]{hyperref}
  \fi
\fi

\hypersetup{%
  colorlinks=true,%
  linkcolor=blue,%
  citecolor=blue,%
  unicode%
}

% Custom commands
\newcommand{\clearemptydoublepage}{%
  \newpage{\pagestyle{empty}\cleardoublepage}%
}

\usepackage{caption}
\captionsetup{labelsep=space,justification=centering,font={bf},singlelinecheck=off,skip=4pt,position=top}

\usepackage{etoc}
\etocsettocstyle{\doxytocparskip}{\doxynormalparskip}
\renewcommand{\numberline}[1]{#1~}
%===== C O N T E N T S =====

\begin{document}
\raggedbottom

% Titlepage & ToC
\hypersetup{pageanchor=false,
             bookmarksnumbered=true,
             pdfencoding=unicode
            }
\pagenumbering{alph}
\begin{titlepage}
\vspace*{7cm}
\begin{center}%
{\Large iir1 }\\
\vspace*{1cm}
{\large Generated by Doxygen 1.9.1}\\
\end{center}
\end{titlepage}
\pagenumbering{roman}
\tableofcontents
\pagenumbering{arabic}
\hypersetup{pageanchor=true}

%--- Begin generated contents ---
\doxysection{DSP IIR Realtime C++ filter library}
\label{index}\hypertarget{index}{}\input{index}
\doxysection{Namespace Index}
\input{namespaces}
\doxysection{Hierarchical Index}
\input{hierarchy}
\doxysection{Class Index}
\input{annotated}
\doxysection{Namespace Documentation}
\input{namespaceIir}
\input{namespaceIir_1_1Butterworth}
\input{namespaceIir_1_1ChebyshevI}
\input{namespaceIir_1_1ChebyshevII}
\input{namespaceIir_1_1Custom}
\doxysection{Class Documentation}
\input{structIir_1_1RBJ_1_1AllPass}
\input{classIir_1_1Butterworth_1_1AnalogLowPass}
\input{classIir_1_1ChebyshevI_1_1AnalogLowPass}
\input{classIir_1_1ChebyshevII_1_1AnalogLowPass}
\input{classIir_1_1Butterworth_1_1AnalogLowShelf}
\input{classIir_1_1ChebyshevI_1_1AnalogLowShelf}
\input{classIir_1_1ChebyshevII_1_1AnalogLowShelf}
\input{structIir_1_1Butterworth_1_1BandPass}
\input{structIir_1_1ChebyshevI_1_1BandPass}
\input{structIir_1_1ChebyshevII_1_1BandPass}
\input{structIir_1_1RBJ_1_1BandPass1}
\input{structIir_1_1RBJ_1_1BandPass2}
\input{structIir_1_1Butterworth_1_1BandPassBase}
\input{structIir_1_1ChebyshevI_1_1BandPassBase}
\input{structIir_1_1ChebyshevII_1_1BandPassBase}
\input{classIir_1_1BandPassTransform}
\input{structIir_1_1Butterworth_1_1BandShelf}
\input{structIir_1_1ChebyshevI_1_1BandShelf}
\input{structIir_1_1ChebyshevII_1_1BandShelf}
\input{structIir_1_1RBJ_1_1BandShelf}
\input{structIir_1_1Butterworth_1_1BandShelfBase}
\input{structIir_1_1ChebyshevI_1_1BandShelfBase}
\input{structIir_1_1ChebyshevII_1_1BandShelfBase}
\input{structIir_1_1Butterworth_1_1BandStop}
\input{structIir_1_1ChebyshevI_1_1BandStop}
\input{structIir_1_1ChebyshevII_1_1BandStop}
\input{structIir_1_1RBJ_1_1BandStop}
\input{structIir_1_1Butterworth_1_1BandStopBase}
\input{structIir_1_1ChebyshevI_1_1BandStopBase}
\input{structIir_1_1ChebyshevII_1_1BandStopBase}
\input{classIir_1_1BandStopTransform}
\input{classIir_1_1Biquad}
\input{structIir_1_1BiquadPoleState}
\input{classIir_1_1Cascade}
\input{classIir_1_1CascadeStages}
\input{structIir_1_1ComplexPair}
\input{classIir_1_1DirectFormI}
\input{classIir_1_1DirectFormII}
\input{structIir_1_1Butterworth_1_1HighPass}
\input{structIir_1_1ChebyshevI_1_1HighPass}
\input{structIir_1_1ChebyshevII_1_1HighPass}
\input{structIir_1_1RBJ_1_1HighPass}
\input{structIir_1_1Butterworth_1_1HighPassBase}
\input{structIir_1_1ChebyshevI_1_1HighPassBase}
\input{structIir_1_1ChebyshevII_1_1HighPassBase}
\input{classIir_1_1HighPassTransform}
\input{structIir_1_1Butterworth_1_1HighShelf}
\input{structIir_1_1ChebyshevI_1_1HighShelf}
\input{structIir_1_1ChebyshevII_1_1HighShelf}
\input{structIir_1_1RBJ_1_1HighShelf}
\input{structIir_1_1Butterworth_1_1HighShelfBase}
\input{structIir_1_1ChebyshevI_1_1HighShelfBase}
\input{structIir_1_1ChebyshevII_1_1HighShelfBase}
\input{structIir_1_1RBJ_1_1IIRNotch}
\input{classIir_1_1Layout}
\input{classIir_1_1LayoutBase}
\input{structIir_1_1Butterworth_1_1LowPass}
\input{structIir_1_1ChebyshevI_1_1LowPass}
\input{structIir_1_1ChebyshevII_1_1LowPass}
\input{structIir_1_1RBJ_1_1LowPass}
\input{structIir_1_1Butterworth_1_1LowPassBase}
\input{structIir_1_1ChebyshevI_1_1LowPassBase}
\input{structIir_1_1ChebyshevII_1_1LowPassBase}
\input{classIir_1_1LowPassTransform}
\input{structIir_1_1Butterworth_1_1LowShelf}
\input{structIir_1_1ChebyshevI_1_1LowShelf}
\input{structIir_1_1ChebyshevII_1_1LowShelf}
\input{structIir_1_1RBJ_1_1LowShelf}
\input{structIir_1_1Butterworth_1_1LowShelfBase}
\input{structIir_1_1ChebyshevI_1_1LowShelfBase}
\input{structIir_1_1ChebyshevII_1_1LowShelfBase}
\input{structIir_1_1Custom_1_1OnePole}
\input{structIir_1_1PoleFilter}
\input{classIir_1_1PoleFilterBase}
\input{classIir_1_1PoleFilterBase2}
\input{structIir_1_1PoleZeroPair}
\input{structIir_1_1RBJ_1_1RBJbase}
\input{structIir_1_1Custom_1_1SOSCascade}
\input{structIir_1_1Cascade_1_1Storage}
\input{classIir_1_1TransposedDirectFormII}
\input{structIir_1_1Custom_1_1TwoPole}
%--- End generated contents ---

% Index
\newpage
\phantomsection
\clearemptydoublepage
\addcontentsline{toc}{section}{\indexname}
\printindex

\end{document}
