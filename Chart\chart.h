#ifndef Chart_H
#define Chart_H

#include <QWidget>
#include <QMouseEvent>
#include <QTimer>
#include "waveformmdiarea.h"
#include <QTableWidgetItem>
#include <QDebug>
#include <QColorDialog>

namespace Ui {
class Chart;
}

class Chart : public QWidget
{
    Q_OBJECT

public:
    // 构造函数和析构函数
    explicit Chart(QWidget *parent = nullptr, const QString& title = "Chart Title",
                   const QString xName ="X轴",const QString yName ="Y轴");
    ~Chart();

    // 标题管理
    void setTitle(const QString title);
    QString title();
    void setTitleStyleSheet(bool isActive);
    bool getTitleStyleSheet();

    // 曲线操作
    void updateCurve(const QVector<double>& x, const QVector<double>& y, const QString& curveName = "新曲线");
    void clearGraphs();

protected:
    // 重写关闭事件
    void closeEvent(QCloseEvent *event) override;

signals:
    // 窗口即将关闭信号
    void aboutToClose();

    //信号：清除曲线数据时发送
    void clearCurves();

private slots:
    // 视图自适应
    void on_fitToView_clicked();
    // 切换游标显示
    void on_tracer_clicked();
    // 全屏切换
    void on_showHide_toggled(bool checked);
    // 切换到下一条曲线
    void on_nextCurve_clicked();
    // 模式切换
    void on_mode_toggled(bool checked);
    // 曲线颜色修改
    void onCurveTableItemDoubleClicked(QTableWidgetItem *item);
    // 表头全选复选框点击事件
    void onHeaderCheckBoxClicked();

private:
    QCheckBox *headerCheckBox;    // 表头全选复选框
    Ui::Chart *ui;                // UI界面
    WaveformMdiArea *waveformArea; // 波形显示区域

    // 标题样式
    QString inactiveStyle = "QLabel { background-color: #d0d0ff; padding: 5px; font-weight: bold; border: none; }";
    QString activeStyle = "QLabel { background-color: #FFA500; padding: 5px; font-weight: bold; border: none; }";

    // 初始化UI
    void initUi(const QString& title,const QString xName ="X轴",const QString yName ="Y轴");
    // 更新曲线表格
    void updateCurveTable();

    //按钮管理
    void haveCurve(bool have);
};

#endif // Chart_H
