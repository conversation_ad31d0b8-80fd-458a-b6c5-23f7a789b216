\doxysubsection{Class Hierarchy}
This inheritance list is sorted roughly, but not completely, alphabetically\+:\begin{DoxyCompactList}
\item Band\+Pass\+Base\begin{DoxyCompactList}
\item \contentsline{section}{Iir\+::Pole\+Filter$<$ Band\+Pass\+Base, Direct\+Form\+II, 4, 4 $\ast$2 $>$}{\pageref{structIir_1_1PoleFilter}}{}
\begin{DoxyCompactList}
\item \contentsline{section}{Iir\+::Butterworth\+::Band\+Pass$<$ Filter\+Order, State\+Type $>$}{\pageref{structIir_1_1Butterworth_1_1BandPass}}{}
\item \contentsline{section}{Iir\+::ChebyshevI\+::Band\+Pass$<$ Filter\+Order, State\+Type $>$}{\pageref{structIir_1_1ChebyshevI_1_1BandPass}}{}
\item \contentsline{section}{Iir\+::Chebyshev\+II\+::Band\+Pass$<$ Filter\+Order, State\+Type $>$}{\pageref{structIir_1_1ChebyshevII_1_1BandPass}}{}
\end{DoxyCompactList}
\end{DoxyCompactList}
\item \contentsline{section}{Iir\+::Band\+Pass\+Transform}{\pageref{classIir_1_1BandPassTransform}}{}
\item Band\+Shelf\+Base\begin{DoxyCompactList}
\item \contentsline{section}{Iir\+::Pole\+Filter$<$ Band\+Shelf\+Base, Direct\+Form\+II, 4, 4 $\ast$2 $>$}{\pageref{structIir_1_1PoleFilter}}{}
\begin{DoxyCompactList}
\item \contentsline{section}{Iir\+::Butterworth\+::Band\+Shelf$<$ Filter\+Order, State\+Type $>$}{\pageref{structIir_1_1Butterworth_1_1BandShelf}}{}
\item \contentsline{section}{Iir\+::ChebyshevI\+::Band\+Shelf$<$ Filter\+Order, State\+Type $>$}{\pageref{structIir_1_1ChebyshevI_1_1BandShelf}}{}
\item \contentsline{section}{Iir\+::Chebyshev\+II\+::Band\+Shelf$<$ Filter\+Order, State\+Type $>$}{\pageref{structIir_1_1ChebyshevII_1_1BandShelf}}{}
\end{DoxyCompactList}
\end{DoxyCompactList}
\item Band\+Stop\+Base\begin{DoxyCompactList}
\item \contentsline{section}{Iir\+::Pole\+Filter$<$ Band\+Stop\+Base, Direct\+Form\+II, 4, 4 $\ast$2 $>$}{\pageref{structIir_1_1PoleFilter}}{}
\begin{DoxyCompactList}
\item \contentsline{section}{Iir\+::Butterworth\+::Band\+Stop$<$ Filter\+Order, State\+Type $>$}{\pageref{structIir_1_1Butterworth_1_1BandStop}}{}
\item \contentsline{section}{Iir\+::ChebyshevI\+::Band\+Stop$<$ Filter\+Order, State\+Type $>$}{\pageref{structIir_1_1ChebyshevI_1_1BandStop}}{}
\item \contentsline{section}{Iir\+::Chebyshev\+II\+::Band\+Stop$<$ Filter\+Order, State\+Type $>$}{\pageref{structIir_1_1ChebyshevII_1_1BandStop}}{}
\end{DoxyCompactList}
\end{DoxyCompactList}
\item \contentsline{section}{Iir\+::Band\+Stop\+Transform}{\pageref{classIir_1_1BandStopTransform}}{}
\item Base\+Class\begin{DoxyCompactList}
\item \contentsline{section}{Iir\+::Pole\+Filter$<$ Base\+Class, State\+Type, Max\+Analog\+Poles, Max\+Digital\+Poles $>$}{\pageref{structIir_1_1PoleFilter}}{}
\end{DoxyCompactList}
\item \contentsline{section}{Iir\+::Biquad}{\pageref{classIir_1_1Biquad}}{}
\begin{DoxyCompactList}
\item \contentsline{section}{Iir\+::Custom\+::One\+Pole}{\pageref{structIir_1_1Custom_1_1OnePole}}{}
\item \contentsline{section}{Iir\+::Custom\+::Two\+Pole}{\pageref{structIir_1_1Custom_1_1TwoPole}}{}
\item \contentsline{section}{Iir\+::RBJ\+::RBJbase}{\pageref{structIir_1_1RBJ_1_1RBJbase}}{}
\begin{DoxyCompactList}
\item \contentsline{section}{Iir\+::RBJ\+::All\+Pass}{\pageref{structIir_1_1RBJ_1_1AllPass}}{}
\item \contentsline{section}{Iir\+::RBJ\+::Band\+Pass1}{\pageref{structIir_1_1RBJ_1_1BandPass1}}{}
\item \contentsline{section}{Iir\+::RBJ\+::Band\+Pass2}{\pageref{structIir_1_1RBJ_1_1BandPass2}}{}
\item \contentsline{section}{Iir\+::RBJ\+::Band\+Shelf}{\pageref{structIir_1_1RBJ_1_1BandShelf}}{}
\item \contentsline{section}{Iir\+::RBJ\+::Band\+Stop}{\pageref{structIir_1_1RBJ_1_1BandStop}}{}
\item \contentsline{section}{Iir\+::RBJ\+::High\+Pass}{\pageref{structIir_1_1RBJ_1_1HighPass}}{}
\item \contentsline{section}{Iir\+::RBJ\+::High\+Shelf}{\pageref{structIir_1_1RBJ_1_1HighShelf}}{}
\item \contentsline{section}{Iir\+::RBJ\+::IIRNotch}{\pageref{structIir_1_1RBJ_1_1IIRNotch}}{}
\item \contentsline{section}{Iir\+::RBJ\+::Low\+Pass}{\pageref{structIir_1_1RBJ_1_1LowPass}}{}
\item \contentsline{section}{Iir\+::RBJ\+::Low\+Shelf}{\pageref{structIir_1_1RBJ_1_1LowShelf}}{}
\end{DoxyCompactList}
\end{DoxyCompactList}
\item \contentsline{section}{Iir\+::Cascade}{\pageref{classIir_1_1Cascade}}{}
\begin{DoxyCompactList}
\item \contentsline{section}{Iir\+::Pole\+Filter\+Base2}{\pageref{classIir_1_1PoleFilterBase2}}{}
\begin{DoxyCompactList}
\item \contentsline{section}{Iir\+::Pole\+Filter\+Base$<$ Analog\+Low\+Pass $>$}{\pageref{classIir_1_1PoleFilterBase}}{}
\begin{DoxyCompactList}
\item \contentsline{section}{Iir\+::Butterworth\+::Band\+Pass\+Base}{\pageref{structIir_1_1Butterworth_1_1BandPassBase}}{}
\item \contentsline{section}{Iir\+::Butterworth\+::Band\+Stop\+Base}{\pageref{structIir_1_1Butterworth_1_1BandStopBase}}{}
\item \contentsline{section}{Iir\+::Butterworth\+::High\+Pass\+Base}{\pageref{structIir_1_1Butterworth_1_1HighPassBase}}{}
\item \contentsline{section}{Iir\+::Butterworth\+::Low\+Pass\+Base}{\pageref{structIir_1_1Butterworth_1_1LowPassBase}}{}
\item \contentsline{section}{Iir\+::ChebyshevI\+::Band\+Pass\+Base}{\pageref{structIir_1_1ChebyshevI_1_1BandPassBase}}{}
\item \contentsline{section}{Iir\+::ChebyshevI\+::Band\+Stop\+Base}{\pageref{structIir_1_1ChebyshevI_1_1BandStopBase}}{}
\item \contentsline{section}{Iir\+::ChebyshevI\+::High\+Pass\+Base}{\pageref{structIir_1_1ChebyshevI_1_1HighPassBase}}{}
\item \contentsline{section}{Iir\+::ChebyshevI\+::Low\+Pass\+Base}{\pageref{structIir_1_1ChebyshevI_1_1LowPassBase}}{}
\item \contentsline{section}{Iir\+::Chebyshev\+II\+::Band\+Pass\+Base}{\pageref{structIir_1_1ChebyshevII_1_1BandPassBase}}{}
\item \contentsline{section}{Iir\+::Chebyshev\+II\+::Band\+Stop\+Base}{\pageref{structIir_1_1ChebyshevII_1_1BandStopBase}}{}
\item \contentsline{section}{Iir\+::Chebyshev\+II\+::High\+Pass\+Base}{\pageref{structIir_1_1ChebyshevII_1_1HighPassBase}}{}
\item \contentsline{section}{Iir\+::Chebyshev\+II\+::Low\+Pass\+Base}{\pageref{structIir_1_1ChebyshevII_1_1LowPassBase}}{}
\end{DoxyCompactList}
\item \contentsline{section}{Iir\+::Pole\+Filter\+Base$<$ Analog\+Low\+Shelf $>$}{\pageref{classIir_1_1PoleFilterBase}}{}
\begin{DoxyCompactList}
\item \contentsline{section}{Iir\+::Butterworth\+::Band\+Shelf\+Base}{\pageref{structIir_1_1Butterworth_1_1BandShelfBase}}{}
\item \contentsline{section}{Iir\+::Butterworth\+::High\+Shelf\+Base}{\pageref{structIir_1_1Butterworth_1_1HighShelfBase}}{}
\item \contentsline{section}{Iir\+::Butterworth\+::Low\+Shelf\+Base}{\pageref{structIir_1_1Butterworth_1_1LowShelfBase}}{}
\item \contentsline{section}{Iir\+::ChebyshevI\+::Band\+Shelf\+Base}{\pageref{structIir_1_1ChebyshevI_1_1BandShelfBase}}{}
\item \contentsline{section}{Iir\+::ChebyshevI\+::High\+Shelf\+Base}{\pageref{structIir_1_1ChebyshevI_1_1HighShelfBase}}{}
\item \contentsline{section}{Iir\+::ChebyshevI\+::Low\+Shelf\+Base}{\pageref{structIir_1_1ChebyshevI_1_1LowShelfBase}}{}
\item \contentsline{section}{Iir\+::Chebyshev\+II\+::Band\+Shelf\+Base}{\pageref{structIir_1_1ChebyshevII_1_1BandShelfBase}}{}
\item \contentsline{section}{Iir\+::Chebyshev\+II\+::High\+Shelf\+Base}{\pageref{structIir_1_1ChebyshevII_1_1HighShelfBase}}{}
\item \contentsline{section}{Iir\+::Chebyshev\+II\+::Low\+Shelf\+Base}{\pageref{structIir_1_1ChebyshevII_1_1LowShelfBase}}{}
\end{DoxyCompactList}
\item \contentsline{section}{Iir\+::Pole\+Filter\+Base$<$ Analog\+Prototype $>$}{\pageref{classIir_1_1PoleFilterBase}}{}
\end{DoxyCompactList}
\end{DoxyCompactList}
\item \contentsline{section}{Iir\+::Cascade\+Stages$<$ Max\+Stages, State\+Type $>$}{\pageref{classIir_1_1CascadeStages}}{}
\begin{DoxyCompactList}
\item \contentsline{section}{Iir\+::Pole\+Filter$<$ Band\+Pass\+Base, Direct\+Form\+II, 4, 4 $\ast$2 $>$}{\pageref{structIir_1_1PoleFilter}}{}
\item \contentsline{section}{Iir\+::Pole\+Filter$<$ Band\+Shelf\+Base, Direct\+Form\+II, 4, 4 $\ast$2 $>$}{\pageref{structIir_1_1PoleFilter}}{}
\item \contentsline{section}{Iir\+::Pole\+Filter$<$ Low\+Pass\+Base, Direct\+Form\+II, 4 $>$}{\pageref{structIir_1_1PoleFilter}}{}
\begin{DoxyCompactList}
\item \contentsline{section}{Iir\+::Butterworth\+::Low\+Pass$<$ Filter\+Order, State\+Type $>$}{\pageref{structIir_1_1Butterworth_1_1LowPass}}{}
\item \contentsline{section}{Iir\+::ChebyshevI\+::Low\+Pass$<$ Filter\+Order, State\+Type $>$}{\pageref{structIir_1_1ChebyshevI_1_1LowPass}}{}
\item \contentsline{section}{Iir\+::Chebyshev\+II\+::Low\+Pass$<$ Filter\+Order, State\+Type $>$}{\pageref{structIir_1_1ChebyshevII_1_1LowPass}}{}
\end{DoxyCompactList}
\item \contentsline{section}{Iir\+::Pole\+Filter$<$ Low\+Shelf\+Base, Direct\+Form\+II, 4 $>$}{\pageref{structIir_1_1PoleFilter}}{}
\begin{DoxyCompactList}
\item \contentsline{section}{Iir\+::Butterworth\+::Low\+Shelf$<$ Filter\+Order, State\+Type $>$}{\pageref{structIir_1_1Butterworth_1_1LowShelf}}{}
\item \contentsline{section}{Iir\+::ChebyshevI\+::Low\+Shelf$<$ Filter\+Order, State\+Type $>$}{\pageref{structIir_1_1ChebyshevI_1_1LowShelf}}{}
\item \contentsline{section}{Iir\+::Chebyshev\+II\+::Low\+Shelf$<$ Filter\+Order, State\+Type $>$}{\pageref{structIir_1_1ChebyshevII_1_1LowShelf}}{}
\end{DoxyCompactList}
\item \contentsline{section}{Iir\+::Pole\+Filter$<$ Band\+Stop\+Base, Direct\+Form\+II, 4, 4 $\ast$2 $>$}{\pageref{structIir_1_1PoleFilter}}{}
\item \contentsline{section}{Iir\+::Pole\+Filter$<$ High\+Shelf\+Base, Direct\+Form\+II, 4 $>$}{\pageref{structIir_1_1PoleFilter}}{}
\begin{DoxyCompactList}
\item \contentsline{section}{Iir\+::Butterworth\+::High\+Shelf$<$ Filter\+Order, State\+Type $>$}{\pageref{structIir_1_1Butterworth_1_1HighShelf}}{}
\item \contentsline{section}{Iir\+::ChebyshevI\+::High\+Shelf$<$ Filter\+Order, State\+Type $>$}{\pageref{structIir_1_1ChebyshevI_1_1HighShelf}}{}
\item \contentsline{section}{Iir\+::Chebyshev\+II\+::High\+Shelf$<$ Filter\+Order, State\+Type $>$}{\pageref{structIir_1_1ChebyshevII_1_1HighShelf}}{}
\end{DoxyCompactList}
\item \contentsline{section}{Iir\+::Pole\+Filter$<$ High\+Pass\+Base, Direct\+Form\+II, 4 $>$}{\pageref{structIir_1_1PoleFilter}}{}
\begin{DoxyCompactList}
\item \contentsline{section}{Iir\+::Butterworth\+::High\+Pass$<$ Filter\+Order, State\+Type $>$}{\pageref{structIir_1_1Butterworth_1_1HighPass}}{}
\item \contentsline{section}{Iir\+::ChebyshevI\+::High\+Pass$<$ Filter\+Order, State\+Type $>$}{\pageref{structIir_1_1ChebyshevI_1_1HighPass}}{}
\item \contentsline{section}{Iir\+::Chebyshev\+II\+::High\+Pass$<$ Filter\+Order, State\+Type $>$}{\pageref{structIir_1_1ChebyshevII_1_1HighPass}}{}
\end{DoxyCompactList}
\end{DoxyCompactList}
\item \contentsline{section}{Iir\+::Cascade\+Stages$<$ NSOS, Direct\+Form\+II $>$}{\pageref{classIir_1_1CascadeStages}}{}
\begin{DoxyCompactList}
\item \contentsline{section}{Iir\+::Custom\+::SOSCascade$<$ NSOS, State\+Type $>$}{\pageref{structIir_1_1Custom_1_1SOSCascade}}{}
\end{DoxyCompactList}
\item \contentsline{section}{Iir\+::Cascade\+Stages$<$(Max\+Analog\+Poles+1)/2, State\+Type $>$}{\pageref{classIir_1_1CascadeStages}}{}
\begin{DoxyCompactList}
\item \contentsline{section}{Iir\+::Pole\+Filter$<$ Base\+Class, State\+Type, Max\+Analog\+Poles, Max\+Digital\+Poles $>$}{\pageref{structIir_1_1PoleFilter}}{}
\end{DoxyCompactList}
\item complex\+\_\+pair\+\_\+t\begin{DoxyCompactList}
\item \contentsline{section}{Iir\+::Complex\+Pair}{\pageref{structIir_1_1ComplexPair}}{}
\end{DoxyCompactList}
\item \contentsline{section}{Iir\+::Direct\+FormI}{\pageref{classIir_1_1DirectFormI}}{}
\item \contentsline{section}{Iir\+::Direct\+Form\+II}{\pageref{classIir_1_1DirectFormII}}{}
\item High\+Pass\+Base\begin{DoxyCompactList}
\item \contentsline{section}{Iir\+::Pole\+Filter$<$ High\+Pass\+Base, Direct\+Form\+II, 4 $>$}{\pageref{structIir_1_1PoleFilter}}{}
\end{DoxyCompactList}
\item \contentsline{section}{Iir\+::High\+Pass\+Transform}{\pageref{classIir_1_1HighPassTransform}}{}
\item High\+Shelf\+Base\begin{DoxyCompactList}
\item \contentsline{section}{Iir\+::Pole\+Filter$<$ High\+Shelf\+Base, Direct\+Form\+II, 4 $>$}{\pageref{structIir_1_1PoleFilter}}{}
\end{DoxyCompactList}
\item \contentsline{section}{Iir\+::Layout$<$ Max\+Poles $>$}{\pageref{classIir_1_1Layout}}{}
\item \contentsline{section}{Iir\+::Layout$<$ Max\+Analog\+Poles $>$}{\pageref{classIir_1_1Layout}}{}
\item \contentsline{section}{Iir\+::Layout\+Base}{\pageref{classIir_1_1LayoutBase}}{}
\begin{DoxyCompactList}
\item \contentsline{section}{Iir\+::Butterworth\+::Analog\+Low\+Pass}{\pageref{classIir_1_1Butterworth_1_1AnalogLowPass}}{}
\item \contentsline{section}{Iir\+::Butterworth\+::Analog\+Low\+Shelf}{\pageref{classIir_1_1Butterworth_1_1AnalogLowShelf}}{}
\item \contentsline{section}{Iir\+::ChebyshevI\+::Analog\+Low\+Pass}{\pageref{classIir_1_1ChebyshevI_1_1AnalogLowPass}}{}
\item \contentsline{section}{Iir\+::ChebyshevI\+::Analog\+Low\+Shelf}{\pageref{classIir_1_1ChebyshevI_1_1AnalogLowShelf}}{}
\item \contentsline{section}{Iir\+::Chebyshev\+II\+::Analog\+Low\+Pass}{\pageref{classIir_1_1ChebyshevII_1_1AnalogLowPass}}{}
\item \contentsline{section}{Iir\+::Chebyshev\+II\+::Analog\+Low\+Shelf}{\pageref{classIir_1_1ChebyshevII_1_1AnalogLowShelf}}{}
\end{DoxyCompactList}
\item Low\+Pass\+Base\begin{DoxyCompactList}
\item \contentsline{section}{Iir\+::Pole\+Filter$<$ Low\+Pass\+Base, Direct\+Form\+II, 4 $>$}{\pageref{structIir_1_1PoleFilter}}{}
\end{DoxyCompactList}
\item \contentsline{section}{Iir\+::Low\+Pass\+Transform}{\pageref{classIir_1_1LowPassTransform}}{}
\item Low\+Shelf\+Base\begin{DoxyCompactList}
\item \contentsline{section}{Iir\+::Pole\+Filter$<$ Low\+Shelf\+Base, Direct\+Form\+II, 4 $>$}{\pageref{structIir_1_1PoleFilter}}{}
\end{DoxyCompactList}
\item \contentsline{section}{Iir\+::Pole\+Zero\+Pair}{\pageref{structIir_1_1PoleZeroPair}}{}
\begin{DoxyCompactList}
\item \contentsline{section}{Iir\+::Biquad\+Pole\+State}{\pageref{structIir_1_1BiquadPoleState}}{}
\end{DoxyCompactList}
\item \contentsline{section}{Iir\+::Cascade\+::Storage}{\pageref{structIir_1_1Cascade_1_1Storage}}{}
\item \contentsline{section}{Iir\+::Transposed\+Direct\+Form\+II}{\pageref{classIir_1_1TransposedDirectFormII}}{}
\end{DoxyCompactList}
