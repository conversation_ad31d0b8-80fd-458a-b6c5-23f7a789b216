\hypertarget{structIir_1_1ChebyshevII_1_1HighShelfBase}{}\doxysubsection{Iir\+::Chebyshev\+II\+::High\+Shelf\+Base Struct Reference}
\label{structIir_1_1ChebyshevII_1_1HighShelfBase}\index{Iir::ChebyshevII::HighShelfBase@{Iir::ChebyshevII::HighShelfBase}}
Inheritance diagram for Iir\+::Chebyshev\+II\+::High\+Shelf\+Base\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=4.000000cm]{structIir_1_1ChebyshevII_1_1HighShelfBase}
\end{center}
\end{figure}
\doxysubsubsection*{Additional Inherited Members}


The documentation for this struct was generated from the following files\+:\begin{DoxyCompactItemize}
\item 
iir/Chebyshev\+II.\+h\item 
iir/Chebyshev\+II.\+cpp\end{DoxyCompactItemize}
