/********************************************************************************
** Form generated from reading UI file 'calibrateonechannelform.ui'
**
** Created by: Qt User Interface Compiler version 5.15.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_CALIBRATEONECHANNELFORM_H
#define UI_CALIBRATEONECHANNELFORM_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_CalibrateOneChannelForm
{
public:
    QVBoxLayout *verticalLayout_5;
    QWidget *Sinwidget;
    QWidget *widget_6;
    QGridLayout *gridLayout;
    QWidget *widget_3;
    QVBoxLayout *verticalLayout_3;
    QLabel *label_4;
    QLineEdit *MultimeterValue;
    QWidget *widget;
    QVBoxLayout *verticalLayout;
    QLabel *label_2;
    QComboBox *SampcomboBox;
    QWidget *widget_9;
    QVBoxLayout *verticalLayout_6;
    QLabel *label_7;
    QComboBox *InChannelcomboBox;
    QWidget *widget_11;
    QVBoxLayout *verticalLayout_8;
    QLabel *label_9;
    QLineEdit *InGainlineEdit;
    QWidget *widget_2;
    QVBoxLayout *verticalLayout_2;
    QLabel *label_3;
    QComboBox *OutChannelcomboBox;
    QWidget *widget_4;
    QVBoxLayout *verticalLayout_4;
    QLabel *label_5;
    QLineEdit *OutGainlineEdit;
    QWidget *widget_5;
    QHBoxLayout *horizontalLayout;
    QPushButton *StarStopBtn;
    QPushButton *SaveBtn;
    QPushButton *ExitBtn;

    void setupUi(QWidget *CalibrateOneChannelForm)
    {
        if (CalibrateOneChannelForm->objectName().isEmpty())
            CalibrateOneChannelForm->setObjectName(QString::fromUtf8("CalibrateOneChannelForm"));
        CalibrateOneChannelForm->resize(980, 592);
        verticalLayout_5 = new QVBoxLayout(CalibrateOneChannelForm);
        verticalLayout_5->setObjectName(QString::fromUtf8("verticalLayout_5"));
        Sinwidget = new QWidget(CalibrateOneChannelForm);
        Sinwidget->setObjectName(QString::fromUtf8("Sinwidget"));
        Sinwidget->setMinimumSize(QSize(600, 300));

        verticalLayout_5->addWidget(Sinwidget);

        widget_6 = new QWidget(CalibrateOneChannelForm);
        widget_6->setObjectName(QString::fromUtf8("widget_6"));
        gridLayout = new QGridLayout(widget_6);
        gridLayout->setObjectName(QString::fromUtf8("gridLayout"));
        widget_3 = new QWidget(widget_6);
        widget_3->setObjectName(QString::fromUtf8("widget_3"));
        widget_3->setMaximumSize(QSize(220, 16777215));
        verticalLayout_3 = new QVBoxLayout(widget_3);
        verticalLayout_3->setSpacing(0);
        verticalLayout_3->setObjectName(QString::fromUtf8("verticalLayout_3"));
        verticalLayout_3->setContentsMargins(0, 0, 0, 0);
        label_4 = new QLabel(widget_3);
        label_4->setObjectName(QString::fromUtf8("label_4"));
        QSizePolicy sizePolicy(QSizePolicy::Fixed, QSizePolicy::Fixed);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(label_4->sizePolicy().hasHeightForWidth());
        label_4->setSizePolicy(sizePolicy);

        verticalLayout_3->addWidget(label_4);

        MultimeterValue = new QLineEdit(widget_3);
        MultimeterValue->setObjectName(QString::fromUtf8("MultimeterValue"));
        MultimeterValue->setMinimumSize(QSize(0, 50));
        MultimeterValue->setMaximumSize(QSize(210, 16777215));
        QFont font;
        font.setPointSize(12);
        MultimeterValue->setFont(font);

        verticalLayout_3->addWidget(MultimeterValue);


        gridLayout->addWidget(widget_3, 1, 0, 1, 1);

        widget = new QWidget(widget_6);
        widget->setObjectName(QString::fromUtf8("widget"));
        widget->setMaximumSize(QSize(220, 16777215));
        verticalLayout = new QVBoxLayout(widget);
        verticalLayout->setSpacing(0);
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        verticalLayout->setContentsMargins(0, 0, 0, 0);
        label_2 = new QLabel(widget);
        label_2->setObjectName(QString::fromUtf8("label_2"));
        sizePolicy.setHeightForWidth(label_2->sizePolicy().hasHeightForWidth());
        label_2->setSizePolicy(sizePolicy);
        label_2->setMinimumSize(QSize(0, 0));
        label_2->setMaximumSize(QSize(16777215, 30));

        verticalLayout->addWidget(label_2);

        SampcomboBox = new QComboBox(widget);
        SampcomboBox->addItem(QString());
        SampcomboBox->addItem(QString());
        SampcomboBox->addItem(QString());
        SampcomboBox->addItem(QString());
        SampcomboBox->addItem(QString());
        SampcomboBox->addItem(QString());
        SampcomboBox->addItem(QString());
        SampcomboBox->addItem(QString());
        SampcomboBox->addItem(QString());
        SampcomboBox->addItem(QString());
        SampcomboBox->setObjectName(QString::fromUtf8("SampcomboBox"));
        SampcomboBox->setMinimumSize(QSize(150, 50));
        SampcomboBox->setMaximumSize(QSize(210, 16777215));
        SampcomboBox->setFont(font);

        verticalLayout->addWidget(SampcomboBox);


        gridLayout->addWidget(widget, 0, 0, 1, 1);

        widget_9 = new QWidget(widget_6);
        widget_9->setObjectName(QString::fromUtf8("widget_9"));
        verticalLayout_6 = new QVBoxLayout(widget_9);
        verticalLayout_6->setObjectName(QString::fromUtf8("verticalLayout_6"));
        verticalLayout_6->setContentsMargins(0, 0, 0, 0);
        label_7 = new QLabel(widget_9);
        label_7->setObjectName(QString::fromUtf8("label_7"));
        sizePolicy.setHeightForWidth(label_7->sizePolicy().hasHeightForWidth());
        label_7->setSizePolicy(sizePolicy);

        verticalLayout_6->addWidget(label_7);

        InChannelcomboBox = new QComboBox(widget_9);
        InChannelcomboBox->setObjectName(QString::fromUtf8("InChannelcomboBox"));
        InChannelcomboBox->setMinimumSize(QSize(200, 50));
        InChannelcomboBox->setFont(font);

        verticalLayout_6->addWidget(InChannelcomboBox);


        gridLayout->addWidget(widget_9, 1, 1, 1, 1);

        widget_11 = new QWidget(widget_6);
        widget_11->setObjectName(QString::fromUtf8("widget_11"));
        widget_11->setMaximumSize(QSize(260, 16777215));
        verticalLayout_8 = new QVBoxLayout(widget_11);
        verticalLayout_8->setObjectName(QString::fromUtf8("verticalLayout_8"));
        verticalLayout_8->setContentsMargins(0, 0, 0, 0);
        label_9 = new QLabel(widget_11);
        label_9->setObjectName(QString::fromUtf8("label_9"));
        sizePolicy.setHeightForWidth(label_9->sizePolicy().hasHeightForWidth());
        label_9->setSizePolicy(sizePolicy);

        verticalLayout_8->addWidget(label_9);

        InGainlineEdit = new QLineEdit(widget_11);
        InGainlineEdit->setObjectName(QString::fromUtf8("InGainlineEdit"));
        InGainlineEdit->setMinimumSize(QSize(0, 50));
        InGainlineEdit->setMaximumSize(QSize(250, 16777215));
        QFont font1;
        font1.setPointSize(15);
        font1.setBold(true);
        font1.setWeight(75);
        InGainlineEdit->setFont(font1);

        verticalLayout_8->addWidget(InGainlineEdit);


        gridLayout->addWidget(widget_11, 1, 2, 1, 1);

        widget_2 = new QWidget(widget_6);
        widget_2->setObjectName(QString::fromUtf8("widget_2"));
        verticalLayout_2 = new QVBoxLayout(widget_2);
        verticalLayout_2->setObjectName(QString::fromUtf8("verticalLayout_2"));
        verticalLayout_2->setContentsMargins(0, 0, 0, 0);
        label_3 = new QLabel(widget_2);
        label_3->setObjectName(QString::fromUtf8("label_3"));
        sizePolicy.setHeightForWidth(label_3->sizePolicy().hasHeightForWidth());
        label_3->setSizePolicy(sizePolicy);

        verticalLayout_2->addWidget(label_3);

        OutChannelcomboBox = new QComboBox(widget_2);
        OutChannelcomboBox->setObjectName(QString::fromUtf8("OutChannelcomboBox"));
        OutChannelcomboBox->setMinimumSize(QSize(200, 50));
        OutChannelcomboBox->setFont(font);

        verticalLayout_2->addWidget(OutChannelcomboBox);


        gridLayout->addWidget(widget_2, 0, 1, 1, 1);

        widget_4 = new QWidget(widget_6);
        widget_4->setObjectName(QString::fromUtf8("widget_4"));
        widget_4->setMaximumSize(QSize(260, 16777215));
        verticalLayout_4 = new QVBoxLayout(widget_4);
        verticalLayout_4->setObjectName(QString::fromUtf8("verticalLayout_4"));
        verticalLayout_4->setContentsMargins(0, 0, 0, 0);
        label_5 = new QLabel(widget_4);
        label_5->setObjectName(QString::fromUtf8("label_5"));
        sizePolicy.setHeightForWidth(label_5->sizePolicy().hasHeightForWidth());
        label_5->setSizePolicy(sizePolicy);

        verticalLayout_4->addWidget(label_5);

        OutGainlineEdit = new QLineEdit(widget_4);
        OutGainlineEdit->setObjectName(QString::fromUtf8("OutGainlineEdit"));
        OutGainlineEdit->setMinimumSize(QSize(0, 50));
        OutGainlineEdit->setMaximumSize(QSize(250, 16777215));
        OutGainlineEdit->setFont(font1);

        verticalLayout_4->addWidget(OutGainlineEdit);


        gridLayout->addWidget(widget_4, 0, 2, 1, 1);


        verticalLayout_5->addWidget(widget_6);

        widget_5 = new QWidget(CalibrateOneChannelForm);
        widget_5->setObjectName(QString::fromUtf8("widget_5"));
        horizontalLayout = new QHBoxLayout(widget_5);
        horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));
        horizontalLayout->setContentsMargins(0, 0, 0, 0);
        StarStopBtn = new QPushButton(widget_5);
        StarStopBtn->setObjectName(QString::fromUtf8("StarStopBtn"));
        StarStopBtn->setMinimumSize(QSize(0, 50));
        QFont font2;
        font2.setPointSize(13);
        font2.setBold(false);
        font2.setWeight(50);
        StarStopBtn->setFont(font2);

        horizontalLayout->addWidget(StarStopBtn);

        SaveBtn = new QPushButton(widget_5);
        SaveBtn->setObjectName(QString::fromUtf8("SaveBtn"));
        SaveBtn->setMinimumSize(QSize(0, 50));
        QFont font3;
        font3.setPointSize(13);
        SaveBtn->setFont(font3);

        horizontalLayout->addWidget(SaveBtn);

        ExitBtn = new QPushButton(widget_5);
        ExitBtn->setObjectName(QString::fromUtf8("ExitBtn"));
        ExitBtn->setMinimumSize(QSize(0, 50));
        ExitBtn->setFont(font3);

        horizontalLayout->addWidget(ExitBtn);


        verticalLayout_5->addWidget(widget_5);


        retranslateUi(CalibrateOneChannelForm);

        QMetaObject::connectSlotsByName(CalibrateOneChannelForm);
    } // setupUi

    void retranslateUi(QWidget *CalibrateOneChannelForm)
    {
        CalibrateOneChannelForm->setWindowTitle(QCoreApplication::translate("CalibrateOneChannelForm", "Form", nullptr));
        label_4->setText(QCoreApplication::translate("CalibrateOneChannelForm", "<html><head/><body><p><span style=\" font-size:12pt;\">\344\270\207\347\224\250\350\241\250\346\265\213\351\207\217\345\200\274</span></p></body></html>", nullptr));
        label_2->setText(QCoreApplication::translate("CalibrateOneChannelForm", "<html><head/><body><p><span style=\" font-size:12pt;\">\351\207\207\346\240\267\347\216\207</span></p></body></html>", nullptr));
        SampcomboBox->setItemText(0, QCoreApplication::translate("CalibrateOneChannelForm", "8000 Hz", nullptr));
        SampcomboBox->setItemText(1, QCoreApplication::translate("CalibrateOneChannelForm", "11025 Hz", nullptr));
        SampcomboBox->setItemText(2, QCoreApplication::translate("CalibrateOneChannelForm", "16000 Hz", nullptr));
        SampcomboBox->setItemText(3, QCoreApplication::translate("CalibrateOneChannelForm", "22050 Hz", nullptr));
        SampcomboBox->setItemText(4, QCoreApplication::translate("CalibrateOneChannelForm", "44100 Hz", nullptr));
        SampcomboBox->setItemText(5, QCoreApplication::translate("CalibrateOneChannelForm", "48000 Hz", nullptr));
        SampcomboBox->setItemText(6, QCoreApplication::translate("CalibrateOneChannelForm", "88200 Hz", nullptr));
        SampcomboBox->setItemText(7, QCoreApplication::translate("CalibrateOneChannelForm", "96000 Hz", nullptr));
        SampcomboBox->setItemText(8, QCoreApplication::translate("CalibrateOneChannelForm", "176400 Hz", nullptr));
        SampcomboBox->setItemText(9, QCoreApplication::translate("CalibrateOneChannelForm", "192000 Hz", nullptr));

        label_7->setText(QCoreApplication::translate("CalibrateOneChannelForm", "<html><head/><body><p><span style=\" font-size:12pt;\">\350\276\223\345\205\245\351\200\232\351\201\223\351\200\211\346\213\251</span></p></body></html>", nullptr));
        label_9->setText(QCoreApplication::translate("CalibrateOneChannelForm", "<html><head/><body><p><span style=\" font-size:12pt; font-weight:600;\">\350\276\223\345\205\245\345\242\236\347\233\212</span></p></body></html>", nullptr));
        label_3->setText(QCoreApplication::translate("CalibrateOneChannelForm", "<html><head/><body><p><span style=\" font-size:12pt;\">\350\276\223\345\207\272\351\200\232\351\201\223\351\200\211\346\213\251</span></p></body></html>", nullptr));
        label_5->setText(QCoreApplication::translate("CalibrateOneChannelForm", "<html><head/><body><p><span style=\" font-size:12pt; font-weight:600;\">\350\276\223\345\207\272\345\242\236\347\233\212</span></p></body></html>", nullptr));
        StarStopBtn->setText(QCoreApplication::translate("CalibrateOneChannelForm", "\345\274\200\345\247\213", nullptr));
        SaveBtn->setText(QCoreApplication::translate("CalibrateOneChannelForm", "\344\277\235\345\255\230", nullptr));
        ExitBtn->setText(QCoreApplication::translate("CalibrateOneChannelForm", "\351\200\200\345\207\272", nullptr));
    } // retranslateUi

};

namespace Ui {
    class CalibrateOneChannelForm: public Ui_CalibrateOneChannelForm {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_CALIBRATEONECHANNELFORM_H
