{"configurations": [{"name": "Win32", "includePath": ["${workspaceFolder}/**", "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/*/include", "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Auxiliary/VS/include"], "defines": ["HAVE_RTAUDIO", "HAVE_FFTW3", "HAVE_CONCURRENT_QUEUE", "HAVE_IIR"], "windowsSdkVersion": "10.0.22621.0", "compilerPath": "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.36.32532/bin/Hostx64/x64/cl.exe", "cStandard": "c17", "cppStandard": "c++17", "intelliSenseMode": "windows-msvc-x64"}], "version": 4}