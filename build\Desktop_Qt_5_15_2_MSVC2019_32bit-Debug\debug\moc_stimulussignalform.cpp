/****************************************************************************
** Meta object code from reading C++ file 'stimulussignalform.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.15.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../MainSet/signal/stimulussignalform.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'stimulussignalform.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.15.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_StimulusSignalForm_t {
    QByteArrayData data[51];
    char stringdata0[1783];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_StimulusSignalForm_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_StimulusSignalForm_t qt_meta_stringdata_StimulusSignalForm = {
    {
QT_MOC_LITERAL(0, 0, 18), // "StimulusSignalForm"
QT_MOC_LITERAL(1, 19, 15), // "showContextMenu"
QT_MOC_LITERAL(2, 35, 0), // ""
QT_MOC_LITERAL(3, 36, 3), // "pos"
QT_MOC_LITERAL(4, 40, 6), // "addRow"
QT_MOC_LITERAL(5, 47, 7), // "editRow"
QT_MOC_LITERAL(6, 55, 7), // "copyRow"
QT_MOC_LITERAL(7, 63, 9), // "deleteRow"
QT_MOC_LITERAL(8, 73, 17), // "handleCellClicked"
QT_MOC_LITERAL(9, 91, 3), // "row"
QT_MOC_LITERAL(10, 95, 6), // "column"
QT_MOC_LITERAL(11, 102, 23), // "handleCellDoubleClicked"
QT_MOC_LITERAL(12, 126, 34), // "on_Gain_dBlineEdit_editingFin..."
QT_MOC_LITERAL(13, 161, 40), // "on_Gain_constantlineEdit_edit..."
QT_MOC_LITERAL(14, 202, 29), // "on_lineEdit_3_editingFinished"
QT_MOC_LITERAL(15, 232, 48), // "on_Step_sweep_start_freqlineE..."
QT_MOC_LITERAL(16, 281, 47), // "on_Step_sweep_stop_freqlineEd..."
QT_MOC_LITERAL(17, 329, 50), // "on_Step_sweep_min_durationlin..."
QT_MOC_LITERAL(18, 380, 49), // "on_Step_sweep_min_circlesline..."
QT_MOC_LITERAL(19, 430, 32), // "on_Step_sweepInputButton_clicked"
QT_MOC_LITERAL(20, 463, 33), // "on_Step_sweepOutputButton_cli..."
QT_MOC_LITERAL(21, 497, 49), // "on_conlogsweep_start_freqline..."
QT_MOC_LITERAL(22, 547, 48), // "on_conlogsweep_stop_freqlineE..."
QT_MOC_LITERAL(23, 596, 47), // "on_conlogsweep_durationlineEd..."
QT_MOC_LITERAL(24, 644, 51), // "on_conlogsweep_pre_durationli..."
QT_MOC_LITERAL(25, 696, 53), // "on_conlogsweep_delay_duration..."
QT_MOC_LITERAL(26, 750, 47), // "on_level_step_frequencylineEd..."
QT_MOC_LITERAL(27, 798, 49), // "on_level_step_start_levelline..."
QT_MOC_LITERAL(28, 848, 48), // "on_level_step_stop_levellineE..."
QT_MOC_LITERAL(29, 897, 51), // "on_level_step_step_durationli..."
QT_MOC_LITERAL(30, 949, 50), // "on_level_step_step_circleslin..."
QT_MOC_LITERAL(31, 1000, 44), // "on_level_step_pointslineEdit_..."
QT_MOC_LITERAL(32, 1045, 32), // "on_level_stepInputButton_clicked"
QT_MOC_LITERAL(33, 1078, 33), // "on_level_stepOutPutButton_cli..."
QT_MOC_LITERAL(34, 1112, 44), // "on_PS_stepped_pointslineEdit_..."
QT_MOC_LITERAL(35, 1157, 48), // "on_PS_stepped_start_freqlineE..."
QT_MOC_LITERAL(36, 1206, 47), // "on_PS_stepped_stop_freqlineEd..."
QT_MOC_LITERAL(37, 1254, 32), // "on_PS_steppedInputButton_clicked"
QT_MOC_LITERAL(38, 1287, 33), // "on_PS_steppedOutputButton_cli..."
QT_MOC_LITERAL(39, 1321, 43), // "on_Step_sweep_octave_typecomb..."
QT_MOC_LITERAL(40, 1365, 5), // "index"
QT_MOC_LITERAL(41, 1371, 46), // "on_Step_sweep_fre_point_typec..."
QT_MOC_LITERAL(42, 1418, 42), // "on_stimulus_signal_type_combo..."
QT_MOC_LITERAL(43, 1461, 32), // "on_signal_unitcomboBox_activated"
QT_MOC_LITERAL(44, 1494, 33), // "on_sample_rate_combobox_activ..."
QT_MOC_LITERAL(45, 1528, 45), // "on_level_step_levelTypeSampco..."
QT_MOC_LITERAL(46, 1574, 48), // "on_level_step_amplitdeTypeSam..."
QT_MOC_LITERAL(47, 1623, 41), // "on_PS_stepped_freq_typecomboB..."
QT_MOC_LITERAL(48, 1665, 51), // "on_PS_stepped_freq_typecomboB..."
QT_MOC_LITERAL(49, 1717, 41), // "on_PS_stepped_step_typecomboB..."
QT_MOC_LITERAL(50, 1759, 23) // "on_pushButton_8_clicked"

    },
    "StimulusSignalForm\0showContextMenu\0\0"
    "pos\0addRow\0editRow\0copyRow\0deleteRow\0"
    "handleCellClicked\0row\0column\0"
    "handleCellDoubleClicked\0"
    "on_Gain_dBlineEdit_editingFinished\0"
    "on_Gain_constantlineEdit_editingFinished\0"
    "on_lineEdit_3_editingFinished\0"
    "on_Step_sweep_start_freqlineEdit_editingFinished\0"
    "on_Step_sweep_stop_freqlineEdit_editingFinished\0"
    "on_Step_sweep_min_durationlineEdit_editingFinished\0"
    "on_Step_sweep_min_circleslineEdit_editingFinished\0"
    "on_Step_sweepInputButton_clicked\0"
    "on_Step_sweepOutputButton_clicked\0"
    "on_conlogsweep_start_freqlineEdit_editingFinished\0"
    "on_conlogsweep_stop_freqlineEdit_editingFinished\0"
    "on_conlogsweep_durationlineEdit_editingFinished\0"
    "on_conlogsweep_pre_durationlineEdit_editingFinished\0"
    "on_conlogsweep_delay_durationlineEdit_editingFinished\0"
    "on_level_step_frequencylineEdit_editingFinished\0"
    "on_level_step_start_levellineEdit_editingFinished\0"
    "on_level_step_stop_levellineEdit_editingFinished\0"
    "on_level_step_step_durationlineEdit_editingFinished\0"
    "on_level_step_step_circleslineEdit_editingFinished\0"
    "on_level_step_pointslineEdit_editingFinished\0"
    "on_level_stepInputButton_clicked\0"
    "on_level_stepOutPutButton_clicked\0"
    "on_PS_stepped_pointslineEdit_editingFinished\0"
    "on_PS_stepped_start_freqlineEdit_editingFinished\0"
    "on_PS_stepped_stop_freqlineEdit_editingFinished\0"
    "on_PS_steppedInputButton_clicked\0"
    "on_PS_steppedOutputButton_clicked\0"
    "on_Step_sweep_octave_typecomboBox_activated\0"
    "index\0on_Step_sweep_fre_point_typecomboBox_activated\0"
    "on_stimulus_signal_type_combobox_activated\0"
    "on_signal_unitcomboBox_activated\0"
    "on_sample_rate_combobox_activated\0"
    "on_level_step_levelTypeSampcomboBox_activated\0"
    "on_level_step_amplitdeTypeSampcomboBox_activated\0"
    "on_PS_stepped_freq_typecomboBox_activated\0"
    "on_PS_stepped_freq_typecomboBox_currentIndexChanged\0"
    "on_PS_stepped_step_typecomboBox_activated\0"
    "on_pushButton_8_clicked"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_StimulusSignalForm[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      45,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    1,  239,    2, 0x08 /* Private */,
       4,    0,  242,    2, 0x08 /* Private */,
       5,    0,  243,    2, 0x08 /* Private */,
       6,    0,  244,    2, 0x08 /* Private */,
       7,    0,  245,    2, 0x08 /* Private */,
       8,    2,  246,    2, 0x08 /* Private */,
      11,    2,  251,    2, 0x08 /* Private */,
      12,    0,  256,    2, 0x08 /* Private */,
      13,    0,  257,    2, 0x08 /* Private */,
      14,    0,  258,    2, 0x08 /* Private */,
      15,    0,  259,    2, 0x08 /* Private */,
      16,    0,  260,    2, 0x08 /* Private */,
      17,    0,  261,    2, 0x08 /* Private */,
      18,    0,  262,    2, 0x08 /* Private */,
      19,    0,  263,    2, 0x08 /* Private */,
      20,    0,  264,    2, 0x08 /* Private */,
      21,    0,  265,    2, 0x08 /* Private */,
      22,    0,  266,    2, 0x08 /* Private */,
      23,    0,  267,    2, 0x08 /* Private */,
      24,    0,  268,    2, 0x08 /* Private */,
      25,    0,  269,    2, 0x08 /* Private */,
      26,    0,  270,    2, 0x08 /* Private */,
      27,    0,  271,    2, 0x08 /* Private */,
      28,    0,  272,    2, 0x08 /* Private */,
      29,    0,  273,    2, 0x08 /* Private */,
      30,    0,  274,    2, 0x08 /* Private */,
      31,    0,  275,    2, 0x08 /* Private */,
      32,    0,  276,    2, 0x08 /* Private */,
      33,    0,  277,    2, 0x08 /* Private */,
      34,    0,  278,    2, 0x08 /* Private */,
      35,    0,  279,    2, 0x08 /* Private */,
      36,    0,  280,    2, 0x08 /* Private */,
      37,    0,  281,    2, 0x08 /* Private */,
      38,    0,  282,    2, 0x08 /* Private */,
      39,    1,  283,    2, 0x08 /* Private */,
      41,    1,  286,    2, 0x08 /* Private */,
      42,    1,  289,    2, 0x08 /* Private */,
      43,    1,  292,    2, 0x08 /* Private */,
      44,    1,  295,    2, 0x08 /* Private */,
      45,    1,  298,    2, 0x08 /* Private */,
      46,    1,  301,    2, 0x08 /* Private */,
      47,    1,  304,    2, 0x08 /* Private */,
      48,    1,  307,    2, 0x08 /* Private */,
      49,    1,  310,    2, 0x08 /* Private */,
      50,    0,  313,    2, 0x08 /* Private */,

 // slots: parameters
    QMetaType::Void, QMetaType::QPoint,    3,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int, QMetaType::Int,    9,   10,
    QMetaType::Void, QMetaType::Int, QMetaType::Int,    9,   10,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int,   40,
    QMetaType::Void, QMetaType::Int,   40,
    QMetaType::Void, QMetaType::Int,   40,
    QMetaType::Void, QMetaType::Int,   40,
    QMetaType::Void, QMetaType::Int,   40,
    QMetaType::Void, QMetaType::Int,   40,
    QMetaType::Void, QMetaType::Int,   40,
    QMetaType::Void, QMetaType::Int,   40,
    QMetaType::Void, QMetaType::Int,   40,
    QMetaType::Void, QMetaType::Int,   40,
    QMetaType::Void,

       0        // eod
};

void StimulusSignalForm::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<StimulusSignalForm *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->showContextMenu((*reinterpret_cast< const QPoint(*)>(_a[1]))); break;
        case 1: _t->addRow(); break;
        case 2: _t->editRow(); break;
        case 3: _t->copyRow(); break;
        case 4: _t->deleteRow(); break;
        case 5: _t->handleCellClicked((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 6: _t->handleCellDoubleClicked((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 7: _t->on_Gain_dBlineEdit_editingFinished(); break;
        case 8: _t->on_Gain_constantlineEdit_editingFinished(); break;
        case 9: _t->on_lineEdit_3_editingFinished(); break;
        case 10: _t->on_Step_sweep_start_freqlineEdit_editingFinished(); break;
        case 11: _t->on_Step_sweep_stop_freqlineEdit_editingFinished(); break;
        case 12: _t->on_Step_sweep_min_durationlineEdit_editingFinished(); break;
        case 13: _t->on_Step_sweep_min_circleslineEdit_editingFinished(); break;
        case 14: _t->on_Step_sweepInputButton_clicked(); break;
        case 15: _t->on_Step_sweepOutputButton_clicked(); break;
        case 16: _t->on_conlogsweep_start_freqlineEdit_editingFinished(); break;
        case 17: _t->on_conlogsweep_stop_freqlineEdit_editingFinished(); break;
        case 18: _t->on_conlogsweep_durationlineEdit_editingFinished(); break;
        case 19: _t->on_conlogsweep_pre_durationlineEdit_editingFinished(); break;
        case 20: _t->on_conlogsweep_delay_durationlineEdit_editingFinished(); break;
        case 21: _t->on_level_step_frequencylineEdit_editingFinished(); break;
        case 22: _t->on_level_step_start_levellineEdit_editingFinished(); break;
        case 23: _t->on_level_step_stop_levellineEdit_editingFinished(); break;
        case 24: _t->on_level_step_step_durationlineEdit_editingFinished(); break;
        case 25: _t->on_level_step_step_circleslineEdit_editingFinished(); break;
        case 26: _t->on_level_step_pointslineEdit_editingFinished(); break;
        case 27: _t->on_level_stepInputButton_clicked(); break;
        case 28: _t->on_level_stepOutPutButton_clicked(); break;
        case 29: _t->on_PS_stepped_pointslineEdit_editingFinished(); break;
        case 30: _t->on_PS_stepped_start_freqlineEdit_editingFinished(); break;
        case 31: _t->on_PS_stepped_stop_freqlineEdit_editingFinished(); break;
        case 32: _t->on_PS_steppedInputButton_clicked(); break;
        case 33: _t->on_PS_steppedOutputButton_clicked(); break;
        case 34: _t->on_Step_sweep_octave_typecomboBox_activated((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 35: _t->on_Step_sweep_fre_point_typecomboBox_activated((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 36: _t->on_stimulus_signal_type_combobox_activated((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 37: _t->on_signal_unitcomboBox_activated((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 38: _t->on_sample_rate_combobox_activated((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 39: _t->on_level_step_levelTypeSampcomboBox_activated((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 40: _t->on_level_step_amplitdeTypeSampcomboBox_activated((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 41: _t->on_PS_stepped_freq_typecomboBox_activated((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 42: _t->on_PS_stepped_freq_typecomboBox_currentIndexChanged((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 43: _t->on_PS_stepped_step_typecomboBox_activated((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 44: _t->on_pushButton_8_clicked(); break;
        default: ;
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject StimulusSignalForm::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_meta_stringdata_StimulusSignalForm.data,
    qt_meta_data_StimulusSignalForm,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *StimulusSignalForm::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *StimulusSignalForm::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_StimulusSignalForm.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int StimulusSignalForm::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 45)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 45;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 45)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 45;
    }
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
