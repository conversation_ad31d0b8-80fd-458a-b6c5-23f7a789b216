\hypertarget{namespaceIir_1_1ChebyshevII}{}\doxysubsection{Iir\+::Chebyshev\+II Namespace Reference}
\label{namespaceIir_1_1ChebyshevII}\index{Iir::ChebyshevII@{Iir::Chebyshev<PERSON>}}
\doxysubsubsection*{Classes}
\begin{DoxyCompactItemize}
\item 
class \mbox{\hyperlink{classIir_1_1ChebyshevII_1_1AnalogLowPass}{Analog\+Low\+Pass}}
\item 
class \mbox{\hyperlink{classIir_1_1ChebyshevII_1_1AnalogLowShelf}{Analog\+Low\+Shelf}}
\item 
struct \mbox{\hyperlink{structIir_1_1ChebyshevII_1_1LowPassBase}{Low\+Pass\+Base}}
\item 
struct \mbox{\hyperlink{structIir_1_1ChebyshevII_1_1HighPassBase}{High\+Pass\+Base}}
\item 
struct \mbox{\hyperlink{structIir_1_1ChebyshevII_1_1BandPassBase}{Band\+Pass\+Base}}
\item 
struct \mbox{\hyperlink{structIir_1_1ChebyshevII_1_1BandStopBase}{Band\+Stop\+Base}}
\item 
struct \mbox{\hyperlink{structIir_1_1ChebyshevII_1_1LowShelfBase}{Low\+Shelf\+Base}}
\item 
struct \mbox{\hyperlink{structIir_1_1ChebyshevII_1_1HighShelfBase}{High\+Shelf\+Base}}
\item 
struct \mbox{\hyperlink{structIir_1_1ChebyshevII_1_1BandShelfBase}{Band\+Shelf\+Base}}
\item 
struct \mbox{\hyperlink{structIir_1_1ChebyshevII_1_1LowPass}{Low\+Pass}}
\item 
struct \mbox{\hyperlink{structIir_1_1ChebyshevII_1_1HighPass}{High\+Pass}}
\item 
struct \mbox{\hyperlink{structIir_1_1ChebyshevII_1_1BandPass}{Band\+Pass}}
\item 
struct \mbox{\hyperlink{structIir_1_1ChebyshevII_1_1BandStop}{Band\+Stop}}
\item 
struct \mbox{\hyperlink{structIir_1_1ChebyshevII_1_1LowShelf}{Low\+Shelf}}
\item 
struct \mbox{\hyperlink{structIir_1_1ChebyshevII_1_1HighShelf}{High\+Shelf}}
\item 
struct \mbox{\hyperlink{structIir_1_1ChebyshevII_1_1BandShelf}{Band\+Shelf}}
\end{DoxyCompactItemize}


\doxysubsubsection{Detailed Description}
Filters with \mbox{\hyperlink{namespaceIir_1_1ChebyshevII}{Chebyshev\+II}} response characteristics. The last parameter defines the minimal stopband rejection requested. Generally there will be frequencies where the rejection is much better but this parameter guarantees that the rejection is at least as specified. 