%!PS-Adobe-2.0 EPSF-2.0
%%Title: ClassName
%%Creator: Doxygen
%%CreationDate: Time
%%For: 
%Magnification: 1.00
%%Orientation: Portrait
%%BoundingBox: 0 0 500 21.645021
%%Pages: 0
%%BeginSetup
%%EndSetup
%%EndComments

% ----- variables -----

/boxwidth 0 def
/boxheight 40 def
/fontheight 24 def
/marginwidth 10 def
/distx 20 def
/disty 40 def
/boundaspect 23.100000 def  % aspect ratio of the BoundingBox (width/height)
/boundx 500 def
/boundy boundx boundaspect div def
/xspacing 0 def
/yspacing 0 def
/rows 5 def
/cols 14 def
/scalefactor 0 def
/boxfont /Times-Roman findfont fontheight scalefont def

% ----- procedures -----

/dotted { [1 4] 0 setdash } def
/dashed { [5] 0 setdash } def
/solid  { [] 0 setdash } def

/max % result = MAX(arg1,arg2)
{
  /a exch def
  /b exch def
  a b gt {a} {b} ifelse
} def

/xoffset % result = MAX(0,(scalefactor-(boxwidth*cols+distx*(cols-1)))/2)
{
  0 scalefactor boxwidth cols mul distx cols 1 sub mul add sub 2 div max
} def

/cw % boxwidth = MAX(boxwidth, stringwidth(arg1))
{
  /str exch def
  /boxwidth boxwidth str stringwidth pop max def
} def

/box % draws a box with text 'arg1' at grid pos (arg2,arg3)
{ gsave
  2 setlinewidth
  newpath
  exch xspacing mul xoffset add
  exch yspacing mul
  moveto
  boxwidth 0 rlineto 
  0 boxheight rlineto 
  boxwidth neg 0 rlineto 
  0 boxheight neg rlineto 
  closepath
  dup stringwidth pop neg boxwidth add 2 div
  boxheight fontheight 2 div sub 2 div
  rmoveto show stroke
  grestore
} def  

/mark
{ newpath
  exch xspacing mul xoffset add boxwidth add
  exch yspacing mul
  moveto
  0 boxheight 4 div rlineto
  boxheight neg 4 div boxheight neg 4 div rlineto
  closepath
  eofill
  stroke
} def

/arrow
{ newpath
  moveto
  3 -8 rlineto
  -6 0 rlineto
  3 8 rlineto
  closepath
  eofill
  stroke
} def

/out % draws an output connector for the block at (arg1,arg2)
{
  newpath
  exch xspacing mul xoffset add boxwidth 2 div add
  exch yspacing mul boxheight add
  /y exch def
  /x exch def
  x y moveto
  0 disty 2 div rlineto 
  stroke
  1 eq { x y disty 2 div add arrow } if
} def

/in % draws an input connector for the block at (arg1,arg2)
{
  newpath
  exch xspacing mul xoffset add boxwidth 2 div add
  exch yspacing mul disty 2 div sub
  /y exch def
  /x exch def
  x y moveto
  0 disty 2 div rlineto
  stroke
  1 eq { x y disty 2 div add arrow } if
} def

/hedge
{
  exch xspacing mul xoffset add boxwidth 2 div add
  exch yspacing mul boxheight 2 div sub
  /y exch def
  /x exch def
  newpath
  x y moveto
  boxwidth 2 div distx add 0 rlineto
  stroke
  1 eq
  { newpath x boxwidth 2 div distx add add y moveto
    -8 3 rlineto
    0 -6 rlineto
    8 3 rlineto
    closepath
    eofill
    stroke
  } if
} def

/vedge
{
  /ye exch def
  /ys exch def
  /xs exch def
  newpath
  xs xspacing mul xoffset add boxwidth 2 div add dup
  ys yspacing mul boxheight 2 div sub
  moveto
  ye yspacing mul boxheight 2 div sub
  lineto
  stroke
} def

/conn % connections the blocks from col 'arg1' to 'arg2' of row 'arg3'
{
  /ys exch def
  /xe exch def
  /xs exch def
  newpath
  xs xspacing mul xoffset add boxwidth 2 div add
  ys yspacing mul disty 2 div sub
  moveto
  xspacing xe xs sub mul 0
  rlineto
  stroke
} def

% ----- main ------

boxfont setfont
1 boundaspect scale
(Iir::CascadeStages< MaxStages, StateType >) cw
(Iir::PoleFilter< BandPassBase, DirectFormII, 4, 4 *2 >) cw
(Iir::PoleFilter< BandShelfBase, DirectFormII, 4, 4 *2 >) cw
(Iir::PoleFilter< LowPassBase, DirectFormII, 4 >) cw
(Iir::PoleFilter< LowShelfBase, DirectFormII, 4 >) cw
(Iir::PoleFilter< BandStopBase, DirectFormII, 4, 4 *2 >) cw
(Iir::PoleFilter< HighShelfBase, DirectFormII, 4 >) cw
(Iir::PoleFilter< HighPassBase, DirectFormII, 4 >) cw
(Iir::Butterworth::BandPass< FilterOrder, StateType >) cw
(Iir::ChebyshevI::BandPass< FilterOrder, StateType >) cw
(Iir::ChebyshevII::BandPass< FilterOrder, StateType >) cw
(Iir::Butterworth::BandShelf< FilterOrder, StateType >) cw
(Iir::ChebyshevI::BandShelf< FilterOrder, StateType >) cw
(Iir::ChebyshevII::BandShelf< FilterOrder, StateType >) cw
(Iir::Butterworth::LowPass< FilterOrder, StateType >) cw
(Iir::ChebyshevI::LowPass< FilterOrder, StateType >) cw
(Iir::ChebyshevII::LowPass< FilterOrder, StateType >) cw
(Iir::Butterworth::LowShelf< FilterOrder, StateType >) cw
(Iir::ChebyshevI::LowShelf< FilterOrder, StateType >) cw
(Iir::ChebyshevII::LowShelf< FilterOrder, StateType >) cw
(Iir::Butterworth::BandStop< FilterOrder, StateType >) cw
(Iir::ChebyshevI::BandStop< FilterOrder, StateType >) cw
(Iir::ChebyshevII::BandStop< FilterOrder, StateType >) cw
(Iir::Butterworth::HighShelf< FilterOrder, StateType >) cw
(Iir::ChebyshevI::HighShelf< FilterOrder, StateType >) cw
(Iir::ChebyshevII::HighShelf< FilterOrder, StateType >) cw
(Iir::Butterworth::HighPass< FilterOrder, StateType >) cw
(Iir::ChebyshevI::HighPass< FilterOrder, StateType >) cw
(Iir::ChebyshevII::HighPass< FilterOrder, StateType >) cw
/boxwidth boxwidth marginwidth 2 mul add def
/xspacing boxwidth distx add def
/yspacing boxheight disty add def
/scalefactor 
  boxwidth cols mul distx cols 1 sub mul add
  boxheight rows mul disty rows 1 sub mul add boundaspect mul 
  max def
boundx scalefactor div boundy scalefactor div scale

% ----- classes -----

 (Iir::CascadeStages< MaxStages, StateType >) 6.000000 4.000000 box
 (Iir::PoleFilter< BandPassBase, DirectFormII, 4, 4 *2 >) 0.000000 3.000000 box
 (Iir::PoleFilter< BandShelfBase, DirectFormII, 4, 4 *2 >) 2.000000 3.000000 box
 (Iir::PoleFilter< LowPassBase, DirectFormII, 4 >) 4.000000 3.000000 box
 (Iir::PoleFilter< LowShelfBase, DirectFormII, 4 >) 6.000000 3.000000 box
 (Iir::PoleFilter< BandStopBase, DirectFormII, 4, 4 *2 >) 8.000000 3.000000 box
 (Iir::PoleFilter< HighShelfBase, DirectFormII, 4 >) 10.000000 3.000000 box
 (Iir::PoleFilter< HighPassBase, DirectFormII, 4 >) 12.000000 3.000000 box
 (Iir::Butterworth::BandPass< FilterOrder, StateType >) 1.000000 2.000000 box
 (Iir::ChebyshevI::BandPass< FilterOrder, StateType >) 1.000000 1.000000 box
 (Iir::ChebyshevII::BandPass< FilterOrder, StateType >) 1.000000 0.000000 box
 (Iir::Butterworth::BandShelf< FilterOrder, StateType >) 3.000000 2.000000 box
 (Iir::ChebyshevI::BandShelf< FilterOrder, StateType >) 3.000000 1.000000 box
 (Iir::ChebyshevII::BandShelf< FilterOrder, StateType >) 3.000000 0.000000 box
 (Iir::Butterworth::LowPass< FilterOrder, StateType >) 5.000000 2.000000 box
 (Iir::ChebyshevI::LowPass< FilterOrder, StateType >) 5.000000 1.000000 box
 (Iir::ChebyshevII::LowPass< FilterOrder, StateType >) 5.000000 0.000000 box
 (Iir::Butterworth::LowShelf< FilterOrder, StateType >) 7.000000 2.000000 box
 (Iir::ChebyshevI::LowShelf< FilterOrder, StateType >) 7.000000 1.000000 box
 (Iir::ChebyshevII::LowShelf< FilterOrder, StateType >) 7.000000 0.000000 box
 (Iir::Butterworth::BandStop< FilterOrder, StateType >) 9.000000 2.000000 box
 (Iir::ChebyshevI::BandStop< FilterOrder, StateType >) 9.000000 1.000000 box
 (Iir::ChebyshevII::BandStop< FilterOrder, StateType >) 9.000000 0.000000 box
 (Iir::Butterworth::HighShelf< FilterOrder, StateType >) 11.000000 2.000000 box
 (Iir::ChebyshevI::HighShelf< FilterOrder, StateType >) 11.000000 1.000000 box
 (Iir::ChebyshevII::HighShelf< FilterOrder, StateType >) 11.000000 0.000000 box
 (Iir::Butterworth::HighPass< FilterOrder, StateType >) 13.000000 2.000000 box
 (Iir::ChebyshevI::HighPass< FilterOrder, StateType >) 13.000000 1.000000 box
 (Iir::ChebyshevII::HighPass< FilterOrder, StateType >) 13.000000 0.000000 box

% ----- relations -----

solid
1 6.000000 3.250000 out
solid
0.000000 12.000000 4.000000 conn
solid
0 0.000000 3.750000 in
solid
1 0.000000 2.250000 out
solid
0 2.000000 3.750000 in
solid
1 2.000000 2.250000 out
solid
0 4.000000 3.750000 in
solid
1 4.000000 2.250000 out
solid
0 6.000000 3.750000 in
solid
1 6.000000 2.250000 out
solid
0 8.000000 3.750000 in
solid
1 8.000000 2.250000 out
solid
0 10.000000 3.750000 in
solid
1 10.000000 2.250000 out
solid
0 12.000000 3.750000 in
solid
1 12.000000 2.250000 out
solid
0 0.000000 2.500000 hedge
solid
0 0.000000 1.500000 hedge
solid
0 0.000000 0.500000 hedge
solid
0.000000 3.000000 0.500000 vedge
solid
0 2.000000 2.500000 hedge
solid
0 2.000000 1.500000 hedge
solid
0 2.000000 0.500000 hedge
solid
2.000000 3.000000 0.500000 vedge
solid
0 4.000000 2.500000 hedge
solid
0 4.000000 1.500000 hedge
solid
0 4.000000 0.500000 hedge
solid
4.000000 3.000000 0.500000 vedge
solid
0 6.000000 2.500000 hedge
solid
0 6.000000 1.500000 hedge
solid
0 6.000000 0.500000 hedge
solid
6.000000 3.000000 0.500000 vedge
solid
0 8.000000 2.500000 hedge
solid
0 8.000000 1.500000 hedge
solid
0 8.000000 0.500000 hedge
solid
8.000000 3.000000 0.500000 vedge
solid
0 10.000000 2.500000 hedge
solid
0 10.000000 1.500000 hedge
solid
0 10.000000 0.500000 hedge
solid
10.000000 3.000000 0.500000 vedge
solid
0 12.000000 2.500000 hedge
solid
0 12.000000 1.500000 hedge
solid
0 12.000000 0.500000 hedge
solid
12.000000 3.000000 0.500000 vedge
