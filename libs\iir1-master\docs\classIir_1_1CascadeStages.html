<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>iir1: Iir::CascadeStages&lt; MaxStages, StateType &gt; Class Template Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">iir1
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespaceIir.html">Iir</a></li><li class="navelem"><a class="el" href="classIir_1_1CascadeStages.html">CascadeStages</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="classIir_1_1CascadeStages-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">Iir::CascadeStages&lt; MaxStages, StateType &gt; Class Template Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;<a class="el" href="Cascade_8h_source.html">Cascade.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for Iir::CascadeStages&lt; MaxStages, StateType &gt;:</div>
<div class="dyncontent">
 <div class="center">
  <img src="classIir_1_1CascadeStages.png" usemap="#Iir::CascadeStages_3C_20MaxStages_2C_20StateType_20_3E_map" alt=""/>
  <map id="Iir::CascadeStages_3C_20MaxStages_2C_20StateType_20_3E_map" name="Iir::CascadeStages_3C_20MaxStages_2C_20StateType_20_3E_map">
<area href="structIir_1_1PoleFilter.html" alt="Iir::PoleFilter&lt; BandPassBase, DirectFormII, 4, 4 *2 &gt;" shape="rect" coords="0,56,322,80"/>
<area href="structIir_1_1PoleFilter.html" alt="Iir::PoleFilter&lt; BandShelfBase, DirectFormII, 4, 4 *2 &gt;" shape="rect" coords="664,56,986,80"/>
<area href="structIir_1_1PoleFilter.html" alt="Iir::PoleFilter&lt; LowPassBase, DirectFormII, 4 &gt;" shape="rect" coords="1328,56,1650,80"/>
<area href="structIir_1_1PoleFilter.html" alt="Iir::PoleFilter&lt; LowShelfBase, DirectFormII, 4 &gt;" shape="rect" coords="1992,56,2314,80"/>
<area href="structIir_1_1PoleFilter.html" alt="Iir::PoleFilter&lt; BandStopBase, DirectFormII, 4, 4 *2 &gt;" shape="rect" coords="2656,56,2978,80"/>
<area href="structIir_1_1PoleFilter.html" alt="Iir::PoleFilter&lt; HighShelfBase, DirectFormII, 4 &gt;" shape="rect" coords="3320,56,3642,80"/>
<area href="structIir_1_1PoleFilter.html" alt="Iir::PoleFilter&lt; HighPassBase, DirectFormII, 4 &gt;" shape="rect" coords="3984,56,4306,80"/>
<area href="structIir_1_1Butterworth_1_1BandPass.html" alt="Iir::Butterworth::BandPass&lt; FilterOrder, StateType &gt;" shape="rect" coords="332,112,654,136"/>
<area href="structIir_1_1ChebyshevI_1_1BandPass.html" alt="Iir::ChebyshevI::BandPass&lt; FilterOrder, StateType &gt;" shape="rect" coords="332,168,654,192"/>
<area href="structIir_1_1ChebyshevII_1_1BandPass.html" alt="Iir::ChebyshevII::BandPass&lt; FilterOrder, StateType &gt;" shape="rect" coords="332,224,654,248"/>
<area href="structIir_1_1Butterworth_1_1BandShelf.html" alt="Iir::Butterworth::BandShelf&lt; FilterOrder, StateType &gt;" shape="rect" coords="996,112,1318,136"/>
<area href="structIir_1_1ChebyshevI_1_1BandShelf.html" alt="Iir::ChebyshevI::BandShelf&lt; FilterOrder, StateType &gt;" shape="rect" coords="996,168,1318,192"/>
<area href="structIir_1_1ChebyshevII_1_1BandShelf.html" alt="Iir::ChebyshevII::BandShelf&lt; FilterOrder, StateType &gt;" shape="rect" coords="996,224,1318,248"/>
<area href="structIir_1_1Butterworth_1_1LowPass.html" alt="Iir::Butterworth::LowPass&lt; FilterOrder, StateType &gt;" shape="rect" coords="1660,112,1982,136"/>
<area href="structIir_1_1ChebyshevI_1_1LowPass.html" alt="Iir::ChebyshevI::LowPass&lt; FilterOrder, StateType &gt;" shape="rect" coords="1660,168,1982,192"/>
<area href="structIir_1_1ChebyshevII_1_1LowPass.html" alt="Iir::ChebyshevII::LowPass&lt; FilterOrder, StateType &gt;" shape="rect" coords="1660,224,1982,248"/>
<area href="structIir_1_1Butterworth_1_1LowShelf.html" alt="Iir::Butterworth::LowShelf&lt; FilterOrder, StateType &gt;" shape="rect" coords="2324,112,2646,136"/>
<area href="structIir_1_1ChebyshevI_1_1LowShelf.html" alt="Iir::ChebyshevI::LowShelf&lt; FilterOrder, StateType &gt;" shape="rect" coords="2324,168,2646,192"/>
<area href="structIir_1_1ChebyshevII_1_1LowShelf.html" alt="Iir::ChebyshevII::LowShelf&lt; FilterOrder, StateType &gt;" shape="rect" coords="2324,224,2646,248"/>
<area href="structIir_1_1Butterworth_1_1BandStop.html" alt="Iir::Butterworth::BandStop&lt; FilterOrder, StateType &gt;" shape="rect" coords="2988,112,3310,136"/>
<area href="structIir_1_1ChebyshevI_1_1BandStop.html" alt="Iir::ChebyshevI::BandStop&lt; FilterOrder, StateType &gt;" shape="rect" coords="2988,168,3310,192"/>
<area href="structIir_1_1ChebyshevII_1_1BandStop.html" alt="Iir::ChebyshevII::BandStop&lt; FilterOrder, StateType &gt;" shape="rect" coords="2988,224,3310,248"/>
<area href="structIir_1_1Butterworth_1_1HighShelf.html" alt="Iir::Butterworth::HighShelf&lt; FilterOrder, StateType &gt;" shape="rect" coords="3652,112,3974,136"/>
<area href="structIir_1_1ChebyshevI_1_1HighShelf.html" alt="Iir::ChebyshevI::HighShelf&lt; FilterOrder, StateType &gt;" shape="rect" coords="3652,168,3974,192"/>
<area href="structIir_1_1ChebyshevII_1_1HighShelf.html" alt="Iir::ChebyshevII::HighShelf&lt; FilterOrder, StateType &gt;" shape="rect" coords="3652,224,3974,248"/>
<area href="structIir_1_1Butterworth_1_1HighPass.html" alt="Iir::Butterworth::HighPass&lt; FilterOrder, StateType &gt;" shape="rect" coords="4316,112,4638,136"/>
<area href="structIir_1_1ChebyshevI_1_1HighPass.html" alt="Iir::ChebyshevI::HighPass&lt; FilterOrder, StateType &gt;" shape="rect" coords="4316,168,4638,192"/>
<area href="structIir_1_1ChebyshevII_1_1HighPass.html" alt="Iir::ChebyshevII::HighPass&lt; FilterOrder, StateType &gt;" shape="rect" coords="4316,224,4638,248"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:ae5253fde0be7ccfa459a3f70fe8e3e31"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classIir_1_1CascadeStages.html#ae5253fde0be7ccfa459a3f70fe8e3e31">reset</a> ()</td></tr>
<tr class="separator:ae5253fde0be7ccfa459a3f70fe8e3e31"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a56d24da4cf09f898b534b1b05578244d"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classIir_1_1CascadeStages.html#a56d24da4cf09f898b534b1b05578244d">setup</a> (const double(&amp;sosCoefficients)[MaxStages][6])</td></tr>
<tr class="separator:a56d24da4cf09f898b534b1b05578244d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa18e9abcaac65fd21be31ee859add3bb"><td class="memTemplParams" colspan="2">template&lt;typename Sample &gt; </td></tr>
<tr class="memitem:aa18e9abcaac65fd21be31ee859add3bb"><td class="memTemplItemLeft" align="right" valign="top">Sample&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="classIir_1_1CascadeStages.html#aa18e9abcaac65fd21be31ee859add3bb">filter</a> (const Sample in)</td></tr>
<tr class="separator:aa18e9abcaac65fd21be31ee859add3bb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a034a9be8ae590b814c8499898a93987a"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="structIir_1_1Cascade_1_1Storage.html">Cascade::Storage</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classIir_1_1CascadeStages.html#a034a9be8ae590b814c8499898a93987a">getCascadeStorage</a> ()</td></tr>
<tr class="separator:a034a9be8ae590b814c8499898a93987a"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><h3>template&lt;int MaxStages, class StateType&gt;<br />
class Iir::CascadeStages&lt; MaxStages, StateType &gt;</h3>

<p>Storage for <a class="el" href="classIir_1_1Cascade.html">Cascade</a>: This holds a chain of 2nd order filters with its coefficients. </p>
</div><h2 class="groupheader">Member Function Documentation</h2>
<a id="aa18e9abcaac65fd21be31ee859add3bb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa18e9abcaac65fd21be31ee859add3bb">&#9670;&nbsp;</a></span>filter()</h2>

<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int MaxStages, class StateType &gt; </div>
<div class="memtemplate">
template&lt;typename Sample &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">Sample <a class="el" href="classIir_1_1CascadeStages.html">Iir::CascadeStages</a>&lt; MaxStages, StateType &gt;::filter </td>
          <td>(</td>
          <td class="paramtype">const Sample&#160;</td>
          <td class="paramname"><em>in</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Filters one sample through the whole chain of biquads and return the result </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">in</td><td>Sample to be filtered </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>filtered sample </dd></dl>

</div>
</div>
<a id="a034a9be8ae590b814c8499898a93987a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a034a9be8ae590b814c8499898a93987a">&#9670;&nbsp;</a></span>getCascadeStorage()</h2>

<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int MaxStages, class StateType &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="structIir_1_1Cascade_1_1Storage.html">Cascade::Storage</a> <a class="el" href="classIir_1_1CascadeStages.html">Iir::CascadeStages</a>&lt; MaxStages, StateType &gt;::getCascadeStorage </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the coefficients of the entire <a class="el" href="classIir_1_1Biquad.html">Biquad</a> chain </p>

</div>
</div>
<a id="ae5253fde0be7ccfa459a3f70fe8e3e31"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae5253fde0be7ccfa459a3f70fe8e3e31">&#9670;&nbsp;</a></span>reset()</h2>

<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int MaxStages, class StateType &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void <a class="el" href="classIir_1_1CascadeStages.html">Iir::CascadeStages</a>&lt; MaxStages, StateType &gt;::reset </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Resets all biquads (i.e. the delay lines but not the coefficients) </p>

</div>
</div>
<a id="a56d24da4cf09f898b534b1b05578244d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a56d24da4cf09f898b534b1b05578244d">&#9670;&nbsp;</a></span>setup()</h2>

<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int MaxStages, class StateType &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void <a class="el" href="classIir_1_1CascadeStages.html">Iir::CascadeStages</a>&lt; MaxStages, StateType &gt;::setup </td>
          <td>(</td>
          <td class="paramtype">const double(&amp;)&#160;</td>
          <td class="paramname"><em>sosCoefficients</em>[MaxStages][6]</td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Sets the coefficients of the whole chain of biquads. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">sosCoefficients</td><td>2D array in Python style sos ordering: 0-2: FIR, 3-5: IIR coeff. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>iir/<a class="el" href="Cascade_8h_source.html">Cascade.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Fri Jan 6 2023 23:53:33 for iir1 by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
