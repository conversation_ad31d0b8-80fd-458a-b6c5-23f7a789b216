/****************************************************************************
** Meta object code from reading C++ file 'moothcalibrationform.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.15.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../MainSet/sensor/moothcalibrationform.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'moothcalibrationform.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.15.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_MoothCalibrationForm_t {
    QByteArrayData data[15];
    char stringdata0[296];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_MoothCalibrationForm_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_MoothCalibrationForm_t qt_meta_stringdata_MoothCalibrationForm = {
    {
QT_MOC_LITERAL(0, 0, 20), // "MoothCalibrationForm"
QT_MOC_LITERAL(1, 21, 26), // "on_pushButton_Fist_clicked"
QT_MOC_LITERAL(2, 48, 0), // ""
QT_MOC_LITERAL(3, 49, 25), // "on_pushButton_Sco_clicked"
QT_MOC_LITERAL(4, 75, 22), // "handlePlaybackFinished"
QT_MOC_LITERAL(5, 98, 27), // "AudioProcessor::ChannelType"
QT_MOC_LITERAL(6, 126, 4), // "type"
QT_MOC_LITERAL(7, 131, 11), // "channelName"
QT_MOC_LITERAL(8, 143, 10), // "updatePlot"
QT_MOC_LITERAL(9, 154, 37), // "on_radioButton_WaveFreRaspose..."
QT_MOC_LITERAL(10, 192, 7), // "checked"
QT_MOC_LITERAL(11, 200, 30), // "on_radioButton_WaveTHD_clicked"
QT_MOC_LITERAL(12, 231, 29), // "on_radioButton_Signal_clicked"
QT_MOC_LITERAL(13, 261, 19), // "UpdateStreamLatency"
QT_MOC_LITERAL(14, 281, 14) // "latencySamples"

    },
    "MoothCalibrationForm\0on_pushButton_Fist_clicked\0"
    "\0on_pushButton_Sco_clicked\0"
    "handlePlaybackFinished\0"
    "AudioProcessor::ChannelType\0type\0"
    "channelName\0updatePlot\0"
    "on_radioButton_WaveFreRaspose_clicked\0"
    "checked\0on_radioButton_WaveTHD_clicked\0"
    "on_radioButton_Signal_clicked\0"
    "UpdateStreamLatency\0latencySamples"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_MoothCalibrationForm[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       8,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    0,   54,    2, 0x08 /* Private */,
       3,    0,   55,    2, 0x08 /* Private */,
       4,    2,   56,    2, 0x08 /* Private */,
       8,    2,   61,    2, 0x08 /* Private */,
       9,    1,   66,    2, 0x08 /* Private */,
      11,    1,   69,    2, 0x08 /* Private */,
      12,    1,   72,    2, 0x08 /* Private */,
      13,    2,   75,    2, 0x08 /* Private */,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 5, QMetaType::QString,    6,    7,
    QMetaType::Void, 0x80000000 | 5, QMetaType::QString,    6,    7,
    QMetaType::Void, QMetaType::Bool,   10,
    QMetaType::Void, QMetaType::Bool,   10,
    QMetaType::Void, QMetaType::Bool,   10,
    QMetaType::Void, QMetaType::QString, QMetaType::UInt,    7,   14,

       0        // eod
};

void MoothCalibrationForm::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<MoothCalibrationForm *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->on_pushButton_Fist_clicked(); break;
        case 1: _t->on_pushButton_Sco_clicked(); break;
        case 2: _t->handlePlaybackFinished((*reinterpret_cast< AudioProcessor::ChannelType(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 3: _t->updatePlot((*reinterpret_cast< AudioProcessor::ChannelType(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 4: _t->on_radioButton_WaveFreRaspose_clicked((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 5: _t->on_radioButton_WaveTHD_clicked((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 6: _t->on_radioButton_Signal_clicked((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 7: _t->UpdateStreamLatency((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< uint(*)>(_a[2]))); break;
        default: ;
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject MoothCalibrationForm::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_meta_stringdata_MoothCalibrationForm.data,
    qt_meta_data_MoothCalibrationForm,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *MoothCalibrationForm::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *MoothCalibrationForm::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_MoothCalibrationForm.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int MoothCalibrationForm::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 8)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 8;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 8)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 8;
    }
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
