\label{index_md_doxy}%
\Hypertarget{index_md_doxy}%
 An infinite impulse response (IIR) filter library for Linux, Mac OSX and Windows which implements Butterworth, RBJ, Chebychev filters and can easily import coefficients generated by Python (scipy).

The filter processes the data sample by sample for realtime processing.

It uses templates to allocate the required memory so that it can run without any malloc / new commands. Memory is allocated at compile time so that there is never any risk of memory leaks.

This library has been further developed from Vinnie Falco\textquotesingle{}s great original work which can be found here\+:

\href{https://github.com/vinniefalco/DSPFilters}{\texttt{ https\+://github.\+com/vinniefalco/\+DSPFilters}}

Bernd Porr -- \href{http://www.berndporr.me.uk}{\texttt{ http\+://www.\+berndporr.\+me.\+uk}} 