\hypertarget{namespaceIir_1_1Butter<PERSON>}{}\doxysubsection{Iir\+::Butterworth Namespace Reference}
\label{namespaceIir_1_1B<PERSON><PERSON>}\index{Iir::Butterworth@{Iir::Butterworth}}
\doxysubsubsection*{Classes}
\begin{DoxyCompactItemize}
\item 
class \mbox{\hyperlink{classIir_1_1Butterworth_1_1AnalogLowPass}{Analog\+Low\+Pass}}
\item 
class \mbox{\hyperlink{classIir_1_1Butterworth_1_1AnalogLowShelf}{Analog\+Low\+Shelf}}
\item 
struct \mbox{\hyperlink{structIir_1_1Butterworth_1_1LowPassBase}{Low\+Pass\+Base}}
\item 
struct \mbox{\hyperlink{structIir_1_1Butterworth_1_1HighPassBase}{High\+Pass\+Base}}
\item 
struct \mbox{\hyperlink{structIir_1_1Butterworth_1_1BandPassBase}{Band\+Pass\+Base}}
\item 
struct \mbox{\hyperlink{structIir_1_1Butterworth_1_1BandStopBase}{Band\+Stop\+Base}}
\item 
struct \mbox{\hyperlink{structIir_1_1Butterworth_1_1LowShelfBase}{Low\+Shelf\+Base}}
\item 
struct \mbox{\hyperlink{structIir_1_1Butterworth_1_1HighShelfBase}{High\+Shelf\+Base}}
\item 
struct \mbox{\hyperlink{structIir_1_1Butterworth_1_1BandShelfBase}{Band\+Shelf\+Base}}
\item 
struct \mbox{\hyperlink{structIir_1_1Butterworth_1_1LowPass}{Low\+Pass}}
\item 
struct \mbox{\hyperlink{structIir_1_1Butterworth_1_1HighPass}{High\+Pass}}
\item 
struct \mbox{\hyperlink{structIir_1_1Butterworth_1_1BandPass}{Band\+Pass}}
\item 
struct \mbox{\hyperlink{structIir_1_1Butterworth_1_1BandStop}{Band\+Stop}}
\item 
struct \mbox{\hyperlink{structIir_1_1Butterworth_1_1LowShelf}{Low\+Shelf}}
\item 
struct \mbox{\hyperlink{structIir_1_1Butterworth_1_1HighShelf}{High\+Shelf}}
\item 
struct \mbox{\hyperlink{structIir_1_1Butterworth_1_1BandShelf}{Band\+Shelf}}
\end{DoxyCompactItemize}


\doxysubsubsection{Detailed Description}
Filters with \mbox{\hyperlink{namespaceIir_1_1Butterworth}{Butterworth}} response characteristics. The filter order is usually set via the template parameter which reserves the correct space and is then automatically passed to the setup function. Optionally one can also provde the filter order at setup time to force a lower order than the default one. 