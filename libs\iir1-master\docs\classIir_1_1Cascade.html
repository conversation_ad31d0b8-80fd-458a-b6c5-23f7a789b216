<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>iir1: Iir::Cascade Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">iir1
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespaceIir.html">Iir</a></li><li class="navelem"><a class="el" href="classIir_1_1Cascade.html">Cascade</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#nested-classes">Classes</a> &#124;
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="classIir_1_1Cascade-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">Iir::Cascade Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;<a class="el" href="Cascade_8h_source.html">Cascade.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for Iir::Cascade:</div>
<div class="dyncontent">
 <div class="center">
  <img src="classIir_1_1Cascade.png" usemap="#Iir::Cascade_map" alt=""/>
  <map id="Iir::Cascade_map" name="Iir::Cascade_map">
<area href="classIir_1_1PoleFilterBase2.html" alt="Iir::PoleFilterBase2" shape="rect" coords="478,56,707,80"/>
<area href="classIir_1_1PoleFilterBase.html" alt="Iir::PoleFilterBase&lt; AnalogLowPass &gt;" shape="rect" coords="0,112,229,136"/>
<area href="classIir_1_1PoleFilterBase.html" alt="Iir::PoleFilterBase&lt; AnalogLowShelf &gt;" shape="rect" coords="478,112,707,136"/>
<area href="classIir_1_1PoleFilterBase.html" alt="Iir::PoleFilterBase&lt; AnalogPrototype &gt;" shape="rect" coords="717,112,946,136"/>
<area href="structIir_1_1Butterworth_1_1BandPassBase.html" alt="Iir::Butterworth::BandPassBase" shape="rect" coords="239,168,468,192"/>
<area href="structIir_1_1Butterworth_1_1BandStopBase.html" alt="Iir::Butterworth::BandStopBase" shape="rect" coords="239,224,468,248"/>
<area href="structIir_1_1Butterworth_1_1HighPassBase.html" alt="Iir::Butterworth::HighPassBase" shape="rect" coords="239,280,468,304"/>
<area href="structIir_1_1Butterworth_1_1LowPassBase.html" alt="Iir::Butterworth::LowPassBase" shape="rect" coords="239,336,468,360"/>
<area href="structIir_1_1ChebyshevI_1_1BandPassBase.html" alt="Iir::ChebyshevI::BandPassBase" shape="rect" coords="239,392,468,416"/>
<area href="structIir_1_1ChebyshevI_1_1BandStopBase.html" alt="Iir::ChebyshevI::BandStopBase" shape="rect" coords="239,448,468,472"/>
<area href="structIir_1_1ChebyshevI_1_1HighPassBase.html" alt="Iir::ChebyshevI::HighPassBase" shape="rect" coords="239,504,468,528"/>
<area href="structIir_1_1ChebyshevI_1_1LowPassBase.html" alt="Iir::ChebyshevI::LowPassBase" shape="rect" coords="239,560,468,584"/>
<area href="structIir_1_1ChebyshevII_1_1BandPassBase.html" alt="Iir::ChebyshevII::BandPassBase" shape="rect" coords="239,616,468,640"/>
<area href="structIir_1_1ChebyshevII_1_1BandStopBase.html" alt="Iir::ChebyshevII::BandStopBase" shape="rect" coords="239,672,468,696"/>
<area href="structIir_1_1ChebyshevII_1_1HighPassBase.html" alt="Iir::ChebyshevII::HighPassBase" shape="rect" coords="239,728,468,752"/>
<area href="structIir_1_1ChebyshevII_1_1LowPassBase.html" alt="Iir::ChebyshevII::LowPassBase" shape="rect" coords="239,784,468,808"/>
<area href="structIir_1_1Butterworth_1_1BandShelfBase.html" alt="Iir::Butterworth::BandShelfBase" shape="rect" coords="717,168,946,192"/>
<area href="structIir_1_1Butterworth_1_1HighShelfBase.html" alt="Iir::Butterworth::HighShelfBase" shape="rect" coords="717,224,946,248"/>
<area href="structIir_1_1Butterworth_1_1LowShelfBase.html" alt="Iir::Butterworth::LowShelfBase" shape="rect" coords="717,280,946,304"/>
<area href="structIir_1_1ChebyshevI_1_1BandShelfBase.html" alt="Iir::ChebyshevI::BandShelfBase" shape="rect" coords="717,336,946,360"/>
<area href="structIir_1_1ChebyshevI_1_1HighShelfBase.html" alt="Iir::ChebyshevI::HighShelfBase" shape="rect" coords="717,392,946,416"/>
<area href="structIir_1_1ChebyshevI_1_1LowShelfBase.html" alt="Iir::ChebyshevI::LowShelfBase" shape="rect" coords="717,448,946,472"/>
<area href="structIir_1_1ChebyshevII_1_1BandShelfBase.html" alt="Iir::ChebyshevII::BandShelfBase" shape="rect" coords="717,504,946,528"/>
<area href="structIir_1_1ChebyshevII_1_1HighShelfBase.html" alt="Iir::ChebyshevII::HighShelfBase" shape="rect" coords="717,560,946,584"/>
<area href="structIir_1_1ChebyshevII_1_1LowShelfBase.html" alt="Iir::ChebyshevII::LowShelfBase" shape="rect" coords="717,616,946,640"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="nested-classes"></a>
Classes</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structIir_1_1Cascade_1_1Storage.html">Storage</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a9694b85c160e3689a4d71fd51ca6175d"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classIir_1_1Cascade.html#a9694b85c160e3689a4d71fd51ca6175d">getNumStages</a> () const</td></tr>
<tr class="separator:a9694b85c160e3689a4d71fd51ca6175d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afc58e4b464b2cdef11e77b01e1a80668"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="classIir_1_1Biquad.html">Biquad</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classIir_1_1Cascade.html#afc58e4b464b2cdef11e77b01e1a80668">operator[]</a> (int index)</td></tr>
<tr class="separator:afc58e4b464b2cdef11e77b01e1a80668"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa76e09e3868829a80e954d0444390f90"><td class="memItemLeft" align="right" valign="top">complex_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classIir_1_1Cascade.html#aa76e09e3868829a80e954d0444390f90">response</a> (double normalizedFrequency) const</td></tr>
<tr class="separator:aa76e09e3868829a80e954d0444390f90"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a18df8bebec4a5e8e3ddc28c35b6bb2f8"><td class="memItemLeft" align="right" valign="top">std::vector&lt; <a class="el" href="structIir_1_1PoleZeroPair.html">PoleZeroPair</a> &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classIir_1_1Cascade.html#a18df8bebec4a5e8e3ddc28c35b6bb2f8">getPoleZeros</a> () const</td></tr>
<tr class="separator:a18df8bebec4a5e8e3ddc28c35b6bb2f8"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Holds coefficients for a cascade of second order sections. </p>
</div><h2 class="groupheader">Member Function Documentation</h2>
<a id="a9694b85c160e3689a4d71fd51ca6175d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9694b85c160e3689a4d71fd51ca6175d">&#9670;&nbsp;</a></span>getNumStages()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int Iir::Cascade::getNumStages </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the number of Biquads kept here </p>

</div>
</div>
<a id="a18df8bebec4a5e8e3ddc28c35b6bb2f8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a18df8bebec4a5e8e3ddc28c35b6bb2f8">&#9670;&nbsp;</a></span>getPoleZeros()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">std::vector&lt; <a class="el" href="structIir_1_1PoleZeroPair.html">PoleZeroPair</a> &gt; Iir::Cascade::getPoleZeros </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Returns a vector with all pole/zero pairs of the whole Biqad cascade </p>

</div>
</div>
<a id="afc58e4b464b2cdef11e77b01e1a80668"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afc58e4b464b2cdef11e77b01e1a80668">&#9670;&nbsp;</a></span>operator[]()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="classIir_1_1Biquad.html">Biquad</a>&amp; Iir::Cascade::operator[] </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>index</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns a reference to a biquad </p>

</div>
</div>
<a id="aa76e09e3868829a80e954d0444390f90"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa76e09e3868829a80e954d0444390f90">&#9670;&nbsp;</a></span>response()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">complex_t Iir::Cascade::response </td>
          <td>(</td>
          <td class="paramtype">double&#160;</td>
          <td class="paramname"><em>normalizedFrequency</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Calculate filter response at the given normalized frequency </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">normalizedFrequency</td><td>Frequency from 0 to 0.5 (Nyquist) </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following files:<ul>
<li>iir/<a class="el" href="Cascade_8h_source.html">Cascade.h</a></li>
<li>iir/Cascade.cpp</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Fri Jan 6 2023 23:53:33 for iir1 by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
