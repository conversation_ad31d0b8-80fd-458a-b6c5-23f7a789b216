/********************************************************************************
** Form generated from reading UI file 'quicktoolform.ui'
**
** Created by: Qt User Interface Compiler version 5.15.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_QUICKTOOLFORM_H
#define UI_QUICKTOOLFORM_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_QuickToolForm
{
public:
    QWidget *layoutWidget;
    QGridLayout *gridLayout;
    QPushButton *pushButton_13;
    QPushButton *pushButton_14;
    QPushButton *pushButton_15;
    QPushButton *pushButton_16;
    QPushButton *pushButton_18;
    QPushButton *pushButton_19;
    QPushButton *pushButton_20;
    QPushButton *pushButton_17;
    QPushButton *pushButton_21;
    QPushButton *pushButton_22;

    void setupUi(QWidget *QuickToolForm)
    {
        if (QuickToolForm->objectName().isEmpty())
            QuickToolForm->setObjectName(QString::fromUtf8("QuickToolForm"));
        QuickToolForm->resize(975, 554);
        layoutWidget = new QWidget(QuickToolForm);
        layoutWidget->setObjectName(QString::fromUtf8("layoutWidget"));
        layoutWidget->setGeometry(QRect(110, 90, 741, 311));
        gridLayout = new QGridLayout(layoutWidget);
        gridLayout->setObjectName(QString::fromUtf8("gridLayout"));
        gridLayout->setContentsMargins(0, 0, 0, 0);
        pushButton_13 = new QPushButton(layoutWidget);
        pushButton_13->setObjectName(QString::fromUtf8("pushButton_13"));
        pushButton_13->setMinimumSize(QSize(170, 60));
        pushButton_13->setStyleSheet(QString::fromUtf8("QPushButton:enabled {\n"
"	font: 15pt \"\351\273\221\344\275\223\";\n"
"    border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 1px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"    background-color: white;  /* \351\273\230\350\256\244\350\203\214\346\231\257\350\211\262 */	\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"}\n"
"QPushButton:enabled:hover {\n"
"    border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 5px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"    background-color: lightblue; /* \346\202\254\346\265\256\346\227\266\350\203"
                        "\214\346\231\257\345\217\230\350\211\262 */\n"
"}\n"
"/* \347\246\201\347\224\250\347\212\266\346\200\201\344\270\213\347\232\204\346\240\267\345\274\217 */\n"
"    QPushButton:disabled {\n"
"        border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"        padding: 15px; /* \344\270\216\345\220\257\347\224\250\347\212\266\346\200\201\344\270\200\350\207\264 */\n"
"        background-color: rgb(229, 229, 229); /* \347\201\260\350\211\262\350\203\214\346\231\257 */\n"
"        border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"    }"));

        gridLayout->addWidget(pushButton_13, 0, 0, 1, 1);

        pushButton_14 = new QPushButton(layoutWidget);
        pushButton_14->setObjectName(QString::fromUtf8("pushButton_14"));
        pushButton_14->setMinimumSize(QSize(170, 60));
        pushButton_14->setStyleSheet(QString::fromUtf8("QPushButton:enabled {\n"
"	font: 15pt \"\351\273\221\344\275\223\";\n"
"    border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 1px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"    background-color: white;  /* \351\273\230\350\256\244\350\203\214\346\231\257\350\211\262 */	\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"}\n"
"QPushButton:enabled:hover {\n"
"    border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 5px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"    background-color: lightblue; /* \346\202\254\346\265\256\346\227\266\350\203"
                        "\214\346\231\257\345\217\230\350\211\262 */\n"
"}\n"
"/* \347\246\201\347\224\250\347\212\266\346\200\201\344\270\213\347\232\204\346\240\267\345\274\217 */\n"
"    QPushButton:disabled {\n"
"        border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"        padding: 15px; /* \344\270\216\345\220\257\347\224\250\347\212\266\346\200\201\344\270\200\350\207\264 */\n"
"        background-color: rgb(229, 229, 229); /* \347\201\260\350\211\262\350\203\214\346\231\257 */\n"
"        border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"    }"));

        gridLayout->addWidget(pushButton_14, 0, 1, 1, 1);

        pushButton_15 = new QPushButton(layoutWidget);
        pushButton_15->setObjectName(QString::fromUtf8("pushButton_15"));
        pushButton_15->setMinimumSize(QSize(170, 60));
        pushButton_15->setStyleSheet(QString::fromUtf8("QPushButton:enabled {\n"
"	font: 15pt \"\351\273\221\344\275\223\";\n"
"    border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 1px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"    background-color: white;  /* \351\273\230\350\256\244\350\203\214\346\231\257\350\211\262 */	\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"}\n"
"QPushButton:enabled:hover {\n"
"    border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 5px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"    background-color: lightblue; /* \346\202\254\346\265\256\346\227\266\350\203"
                        "\214\346\231\257\345\217\230\350\211\262 */\n"
"}\n"
"/* \347\246\201\347\224\250\347\212\266\346\200\201\344\270\213\347\232\204\346\240\267\345\274\217 */\n"
"    QPushButton:disabled {\n"
"        border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"        padding: 15px; /* \344\270\216\345\220\257\347\224\250\347\212\266\346\200\201\344\270\200\350\207\264 */\n"
"        background-color: rgb(229, 229, 229); /* \347\201\260\350\211\262\350\203\214\346\231\257 */\n"
"        border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"    }"));

        gridLayout->addWidget(pushButton_15, 0, 2, 1, 1);

        pushButton_16 = new QPushButton(layoutWidget);
        pushButton_16->setObjectName(QString::fromUtf8("pushButton_16"));
        pushButton_16->setMinimumSize(QSize(170, 60));
        pushButton_16->setStyleSheet(QString::fromUtf8("QPushButton:enabled {\n"
"	font: 15pt \"\351\273\221\344\275\223\";\n"
"    border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 1px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"    background-color: white;  /* \351\273\230\350\256\244\350\203\214\346\231\257\350\211\262 */	\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"}\n"
"QPushButton:enabled:hover {\n"
"    border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 5px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"    background-color: lightblue; /* \346\202\254\346\265\256\346\227\266\350\203"
                        "\214\346\231\257\345\217\230\350\211\262 */\n"
"}\n"
"/* \347\246\201\347\224\250\347\212\266\346\200\201\344\270\213\347\232\204\346\240\267\345\274\217 */\n"
"    QPushButton:disabled {\n"
"        border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"        padding: 15px; /* \344\270\216\345\220\257\347\224\250\347\212\266\346\200\201\344\270\200\350\207\264 */\n"
"        background-color: rgb(229, 229, 229); /* \347\201\260\350\211\262\350\203\214\346\231\257 */\n"
"        border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"    }"));

        gridLayout->addWidget(pushButton_16, 0, 3, 1, 1);

        pushButton_18 = new QPushButton(layoutWidget);
        pushButton_18->setObjectName(QString::fromUtf8("pushButton_18"));
        pushButton_18->setMinimumSize(QSize(170, 60));
        pushButton_18->setStyleSheet(QString::fromUtf8("QPushButton:enabled {\n"
"	font: 15pt \"\351\273\221\344\275\223\";\n"
"    border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 1px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"    background-color: white;  /* \351\273\230\350\256\244\350\203\214\346\231\257\350\211\262 */	\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"}\n"
"QPushButton:enabled:hover {\n"
"    border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 5px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"    background-color: lightblue; /* \346\202\254\346\265\256\346\227\266\350\203"
                        "\214\346\231\257\345\217\230\350\211\262 */\n"
"}\n"
"/* \347\246\201\347\224\250\347\212\266\346\200\201\344\270\213\347\232\204\346\240\267\345\274\217 */\n"
"    QPushButton:disabled {\n"
"        border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"        padding: 15px; /* \344\270\216\345\220\257\347\224\250\347\212\266\346\200\201\344\270\200\350\207\264 */\n"
"        background-color: rgb(229, 229, 229); /* \347\201\260\350\211\262\350\203\214\346\231\257 */\n"
"        border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"    }"));

        gridLayout->addWidget(pushButton_18, 1, 0, 1, 1);

        pushButton_19 = new QPushButton(layoutWidget);
        pushButton_19->setObjectName(QString::fromUtf8("pushButton_19"));
        pushButton_19->setMinimumSize(QSize(170, 60));
        pushButton_19->setStyleSheet(QString::fromUtf8("QPushButton:enabled {\n"
"	font: 15pt \"\351\273\221\344\275\223\";\n"
"    border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 1px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"    background-color: white;  /* \351\273\230\350\256\244\350\203\214\346\231\257\350\211\262 */	\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"}\n"
"QPushButton:enabled:hover {\n"
"    border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 5px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"    background-color: lightblue; /* \346\202\254\346\265\256\346\227\266\350\203"
                        "\214\346\231\257\345\217\230\350\211\262 */\n"
"}\n"
"/* \347\246\201\347\224\250\347\212\266\346\200\201\344\270\213\347\232\204\346\240\267\345\274\217 */\n"
"    QPushButton:disabled {\n"
"        border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"        padding: 15px; /* \344\270\216\345\220\257\347\224\250\347\212\266\346\200\201\344\270\200\350\207\264 */\n"
"        background-color: rgb(229, 229, 229); /* \347\201\260\350\211\262\350\203\214\346\231\257 */\n"
"        border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"    }"));

        gridLayout->addWidget(pushButton_19, 1, 1, 1, 1);

        pushButton_20 = new QPushButton(layoutWidget);
        pushButton_20->setObjectName(QString::fromUtf8("pushButton_20"));
        pushButton_20->setMinimumSize(QSize(170, 60));
        pushButton_20->setStyleSheet(QString::fromUtf8("QPushButton:enabled {\n"
"	font: 15pt \"\351\273\221\344\275\223\";\n"
"    border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 1px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"    background-color: white;  /* \351\273\230\350\256\244\350\203\214\346\231\257\350\211\262 */	\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"}\n"
"QPushButton:enabled:hover {\n"
"    border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 5px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"    background-color: lightblue; /* \346\202\254\346\265\256\346\227\266\350\203"
                        "\214\346\231\257\345\217\230\350\211\262 */\n"
"}\n"
"/* \347\246\201\347\224\250\347\212\266\346\200\201\344\270\213\347\232\204\346\240\267\345\274\217 */\n"
"    QPushButton:disabled {\n"
"        border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"        padding: 15px; /* \344\270\216\345\220\257\347\224\250\347\212\266\346\200\201\344\270\200\350\207\264 */\n"
"        background-color: rgb(229, 229, 229); /* \347\201\260\350\211\262\350\203\214\346\231\257 */\n"
"        border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"    }"));

        gridLayout->addWidget(pushButton_20, 1, 2, 1, 1);

        pushButton_17 = new QPushButton(layoutWidget);
        pushButton_17->setObjectName(QString::fromUtf8("pushButton_17"));
        pushButton_17->setMinimumSize(QSize(170, 60));
        pushButton_17->setStyleSheet(QString::fromUtf8("QPushButton:enabled {\n"
"	font: 15pt \"\351\273\221\344\275\223\";\n"
"    border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 1px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"    background-color: white;  /* \351\273\230\350\256\244\350\203\214\346\231\257\350\211\262 */	\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"}\n"
"QPushButton:enabled:hover {\n"
"    border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 5px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"    background-color: lightblue; /* \346\202\254\346\265\256\346\227\266\350\203"
                        "\214\346\231\257\345\217\230\350\211\262 */\n"
"}\n"
"/* \347\246\201\347\224\250\347\212\266\346\200\201\344\270\213\347\232\204\346\240\267\345\274\217 */\n"
"    QPushButton:disabled {\n"
"        border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"        padding: 15px; /* \344\270\216\345\220\257\347\224\250\347\212\266\346\200\201\344\270\200\350\207\264 */\n"
"        background-color: rgb(229, 229, 229); /* \347\201\260\350\211\262\350\203\214\346\231\257 */\n"
"        border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"    }"));

        gridLayout->addWidget(pushButton_17, 1, 3, 1, 1);

        pushButton_21 = new QPushButton(layoutWidget);
        pushButton_21->setObjectName(QString::fromUtf8("pushButton_21"));
        pushButton_21->setMinimumSize(QSize(170, 60));
        pushButton_21->setStyleSheet(QString::fromUtf8("QPushButton:enabled {\n"
"	font: 15pt \"\351\273\221\344\275\223\";\n"
"    border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 1px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"    background-color: white;  /* \351\273\230\350\256\244\350\203\214\346\231\257\350\211\262 */	\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"}\n"
"QPushButton:enabled:hover {\n"
"    border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 5px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"    background-color: lightblue; /* \346\202\254\346\265\256\346\227\266\350\203"
                        "\214\346\231\257\345\217\230\350\211\262 */\n"
"}\n"
"/* \347\246\201\347\224\250\347\212\266\346\200\201\344\270\213\347\232\204\346\240\267\345\274\217 */\n"
"    QPushButton:disabled {\n"
"        border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"        padding: 15px; /* \344\270\216\345\220\257\347\224\250\347\212\266\346\200\201\344\270\200\350\207\264 */\n"
"        background-color: rgb(229, 229, 229); /* \347\201\260\350\211\262\350\203\214\346\231\257 */\n"
"        border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"    }"));

        gridLayout->addWidget(pushButton_21, 2, 0, 1, 1);

        pushButton_22 = new QPushButton(layoutWidget);
        pushButton_22->setObjectName(QString::fromUtf8("pushButton_22"));
        pushButton_22->setMinimumSize(QSize(170, 60));
        pushButton_22->setStyleSheet(QString::fromUtf8("QPushButton:enabled {\n"
"	font: 15pt \"\351\273\221\344\275\223\";\n"
"    border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 1px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"    background-color: white;  /* \351\273\230\350\256\244\350\203\214\346\231\257\350\211\262 */	\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"}\n"
"QPushButton:enabled:hover {\n"
"    border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"	padding: 5px; /* \345\242\236\345\212\240\345\206\205\350\276\271\350\267\235\357\274\214\351\201\277\345\205\215\345\206\205\345\256\271\350\264\264\350\277\221\350\276\271\346\241\206 */\n"
"	border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"    background-color: lightblue; /* \346\202\254\346\265\256\346\227\266\350\203"
                        "\214\346\231\257\345\217\230\350\211\262 */\n"
"}\n"
"/* \347\246\201\347\224\250\347\212\266\346\200\201\344\270\213\347\232\204\346\240\267\345\274\217 */\n"
"    QPushButton:disabled {\n"
"        border: 1px solid lightblack; /* \351\273\230\350\256\244\350\276\271\346\241\206 */\n"
"        padding: 15px; /* \344\270\216\345\220\257\347\224\250\347\212\266\346\200\201\344\270\200\350\207\264 */\n"
"        background-color: rgb(229, 229, 229); /* \347\201\260\350\211\262\350\203\214\346\231\257 */\n"
"        border-radius: 10px; /* \345\234\206\350\247\222\350\276\271\346\241\206 */\n"
"    }"));

        gridLayout->addWidget(pushButton_22, 2, 1, 1, 1);


        retranslateUi(QuickToolForm);

        QMetaObject::connectSlotsByName(QuickToolForm);
    } // setupUi

    void retranslateUi(QWidget *QuickToolForm)
    {
        QuickToolForm->setWindowTitle(QCoreApplication::translate("QuickToolForm", "\345\267\245\345\205\267", nullptr));
        pushButton_13->setText(QCoreApplication::translate("QuickToolForm", "NULL", nullptr));
        pushButton_14->setText(QCoreApplication::translate("QuickToolForm", "NULL", nullptr));
        pushButton_15->setText(QCoreApplication::translate("QuickToolForm", "NULL", nullptr));
        pushButton_16->setText(QCoreApplication::translate("QuickToolForm", "NULL", nullptr));
        pushButton_18->setText(QCoreApplication::translate("QuickToolForm", "NULL", nullptr));
        pushButton_19->setText(QCoreApplication::translate("QuickToolForm", "NULL", nullptr));
        pushButton_20->setText(QCoreApplication::translate("QuickToolForm", "NULL", nullptr));
        pushButton_17->setText(QCoreApplication::translate("QuickToolForm", "NULL", nullptr));
        pushButton_21->setText(QCoreApplication::translate("QuickToolForm", "NULL", nullptr));
        pushButton_22->setText(QCoreApplication::translate("QuickToolForm", "NULL", nullptr));
    } // retranslateUi

};

namespace Ui {
    class QuickToolForm: public Ui_QuickToolForm {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_QUICKTOOLFORM_H
